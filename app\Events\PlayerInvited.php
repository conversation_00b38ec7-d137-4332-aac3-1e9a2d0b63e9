<?php

namespace App\Events;

use App\Models\User;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class PlayerInvited
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public $player;
    public $emails;
    public $coach;

    public function __construct(User $player, array $emails, User $coach)
    {
        $this->player = $player;
        $this->emails = $emails;
        $this->coach = $coach;
    }
}

