<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Program;
use Illuminate\Http\JsonResponse;
use Exception;

class ApiController extends Controller
{
public function allPrograms(Request $request): JsonResponse
{
    try {
        $query = Program::query();
        $filters = $request->input('filters', []);

        if ($filters) {
            if (($filters['season'])) {
                $season = $filters['season'];

                $query->where('season', $season);
            }

            if (($filters['gender'])) {
                $gender = $filters['gender'];
                $query->where('gender', $gender);
            }
        }


        $query->where('registration_closing_date', '>=', now());




        $programs = $query->paginate(20);

        return response()->json([
            'success' => true,
            'data' => $programs,
            'message' => 'Programs fetched successfully.'
        ]);
    } catch (Exception $e) {
        return response()->json([
            'success' => false,
            'message' => 'Failed to fetch programs. Please try again later.',
            'error' => $e->getMessage()
        ], 500);
    }
}




}
