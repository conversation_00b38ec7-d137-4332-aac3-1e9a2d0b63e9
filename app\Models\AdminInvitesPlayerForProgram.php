<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class AdminInvitesPlayerForProgram extends Model
{
    use HasFactory;

     protected $table = 'admin_invites_player_for_program';
        protected $fillable = ['user_id', 'program_id', 'invited_by', 'invited_at', 'status'];

        public function user(){

            return $this->belongsTo(User::class, 'user_id');
        }

        public function program(){

            return $this->belongsTo(Program::class, 'program_id');
        }
}


