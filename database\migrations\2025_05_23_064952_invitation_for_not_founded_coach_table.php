<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Drop the old invitation_for_not_founded_coaches table if it exists
        if (Schema::hasTable('invitation_for_not_founded_coaches')) {
            Schema::dropIfExists('invitation_for_not_founded_coaches');
        }

        // Create the new consolidated invitation_for_not_founded_coaches table
        Schema::create('invitation_for_not_founded_coaches', function (Blueprint $table) {
            $table->id();
            $table->foreignId('invited_by')->constrained('users')->onDelete('cascade');
            $table->string('email');
            $table->string('invitation_token', 64)->nullable();
            $table->foreignId('team_id')->constrained('teams')->onDelete('cascade');
            $table->boolean('is_joined')->default(false);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('invitation_for_not_founded_coaches');
    }
};
