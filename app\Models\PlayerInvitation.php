<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class PlayerInvitation extends Model
{
    use HasFactory;

    protected $fillable = [
        'coach_id',
        'user_id',
        'player_id',
        'team_id',
        'invitation_status',
        'status',
        'balance_due',
        'program_id',
        'balance_assigned',
        'invited_by',
        'invited_at'
    ];

    protected $casts = [
        'invited_at' => 'datetime',
    ];

    public function player()
    {
        return $this->belongsTo(User::class, 'user_id');
    }

    public function user()
    {
        return $this->belongsTo(User::class, 'user_id');
    }

    public function coach()
    {
        return $this->belongsTo(User::class, 'coach_id');
    }

    public function invitedBy()
    {
        return $this->belongsTo(User::class, 'invited_by');
    }

    public function team()
    {
        return $this->belongsTo(Team::class, 'team_id');
    }

    public function program()
    {
        return $this->belongsTo(Program::class, 'program_id');
    }
}
