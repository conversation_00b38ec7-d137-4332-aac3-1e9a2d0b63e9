@extends('layouts.app')
@section('title', 'Admin')
@section('content')
    <section class="page_title text-center pt-5">
        <h1 class="text-uppercase lh-1 mb-0"><br> Financial Report</h1>

        @if (session('success'))
            <div id="successMessageForSession">
                <span id="successText">{{ session('success') }}</span>
            </div>
        @endif

        @if (session('error'))
            <div id="errorMessageForSession">
                <span id="errorText">{{ session('error') }}</span>
            </div>
        @endif
    </section>

    <section class="sec admin-welcome">
        <div class="container">

            <div>
                <a class="cta" href="{{ url()->previous() }}">Back</a>

            </div>
            <div class="heading align-items-center d-flex flex-column text-center mb-5">
                <span class="hero-bar d-inline-flex"></span>
            </div>

            <form class="form row table-filter justify-content-center" id="filterForm">
                <div class="col-md-auto">
                    <div class="filter-option">
                        <div class="mb-4">
                            <label class="form-label text-uppercase" for="sport-filter">Filter By</label>
                            <div class="select-arrow position-relative">
                                <select class="form-control" id="sport-filter" name="sport-filter">
                                    <option value="" {{ request('sport-filter') == '' ? 'selected' : '' }}>Sport
                                    </option>
                                    <option value="basketball"
                                        {{ request('sport-filter') == 'basketball' ? 'selected' : '' }}>Basketball</option>
                                    <option value="volleyball"
                                        {{ request('sport-filter') == 'volleyball' ? 'selected' : '' }}>Volleyball</option>
                                    <option value="pickleball"
                                        {{ request('sport-filter') == 'pickleball' ? 'selected' : '' }}>Pickleball</option>
                                </select>
                                <span class="arrow"><i class="bi bi-chevron-down"></i></span>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-md-auto">
                    <div class="filter-option">
                        <div class="mb-4">
                            <label class="form-label text-uppercase" for="sport-filter">Filter By</label>
                            <div class="select-arrow position-relative">
                                <select class="form-control" id="age-filter" name="age-filter">
                                    <option value="" {{ request('age-filter') == '' ? 'selected' : '' }}>Age Group
                                    </option>
                                    <option value="adult" {{ request('age-filter') == 'adult' ? 'selected' : '' }}>Adult
                                    </option>
                                    <option value="u18" {{ request('age-filter') == 'u18' ? 'selected' : '' }}>U18
                                    </option>
                                    <option value="u16" {{ request('age-filter') == 'u16' ? 'selected' : '' }}>U16
                                    </option>
                                    <option value="u14" {{ request('age-filter') == 'u14' ? 'selected' : '' }}>U14
                                    </option>
                                    <option value="u12" {{ request('age-filter') == 'u12' ? 'selected' : '' }}>U12
                                    </option>
                                    <option value="u10" {{ request('age-filter') == 'u10' ? 'selected' : '' }}>U10
                                    </option>
                                    <option value="u8" {{ request('age-filter') == 'u8' ? 'selected' : '' }}>U8
                                    </option>
                                </select>
                                <span class="arrow"><i class="bi bi-chevron-down"></i></span>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-md-auto">
                    <div class="filter-option">
                        <div class="mb-4">
                            <label class="form-label text-uppercase" for="date-range-filter">Select Date Range</label>
                            <div class="d-flex">
                                <input type="date" class="form-control me-2" id="start-date" name="start-date"
                                    value="{{ request('start-date') }}" placeholder="Start Date">
                                <span class="text-muted" style="align-self: center;">to</span>
                                <input type="date" class="form-control ms-2" id="end-date" name="end-date"
                                    value="{{ request('end-date') }}" placeholder="End Date">
                            </div>
                        </div>
                    </div>
                </div>


                <div class="col-md-auto">
                    <div class="filter-option">
                        <div class="mb-4">
                            <label class="form-label text-uppercase" for="town-filter">Filter By</label>
                            <div class="select-arrow position-relative">
                                <select class="form-control" id="town-filter" name="town-filter">
                                    <option value="" {{ request('town-filter') == '' ? 'selected' : '' }}>
                                        Town
                                    </option>
                                    @foreach ($towns as $town)
                                        <option value="{{ $town }}"
                                            {{ request('town-filter') == $town ? 'selected' : '' }}>
                                            {{ $town }}
                                        </option>
                                    @endforeach
                                </select>
                                <span class="arrow"><i class="bi bi-chevron-down"></i></span>
                            </div>
                        </div>
                    </div>
                </div>

            </form>


            <div class="container">
                <div class="program-table">
                    <div class="table-program table-responsive" id="payment-div">

                        @include('admin.reports.partialFinancialReport')
                    </div>
                </div>
            </div>
    </section>
@endsection

<script>
    document.addEventListener("DOMContentLoaded", function() {
        const sportFilter = document.getElementById("sport-filter");
        const ageFilter = document.getElementById("age-filter");
        const townFilter = document.getElementById('town-filter');
        const startDateFilter = document.getElementById("start-date");
        const endDateFilter = document.getElementById("end-date");
        const filterForm = document.getElementById("filterForm");
        const paymentDiv = document.getElementById('payment-div');

        filterForm.addEventListener('submit', function(event) {
            event.preventDefault();
        });


        [sportFilter, ageFilter, townFilter, startDateFilter, endDateFilter].forEach(filter => {
            filter.addEventListener("change", () => filterPrograms());
        });


        document.addEventListener('click', function(event) {
            const target = event.target;


            if (target.matches('.pagination a')) {
                event.preventDefault();
                const url = target.href;
                fetchData(url);
            }
        });

        function getFilterParams() {
            const filters = {
                "sport-filter": sportFilter.value,
                "age-filter": ageFilter.value,
                "start-date": startDateFilter.value,
                "end-date": endDateFilter.value,
                "town-filter": townFilter.value
            };

            return Object.entries(filters)
                .filter(([_, value]) => value)
                .reduce((params, [key, value]) => {
                    params.append(key, value);
                    return params;
                }, new URLSearchParams());
        }

        function filterPrograms() {
            const baseUrl = route("admin.financialReport");
            const queryParams = getFilterParams();
            const fullUrl = `${baseUrl}${queryParams.toString() ? "?" + queryParams.toString() : ""}`;
            fetchData(fullUrl);
        }

        function fetchData(url) {

            if (!url.includes('sport-filter')) {
                const queryParams = getFilterParams();
                const separator = url.includes('?') ? '&' : '?';
                url = `${url}${separator}${queryParams.toString()}`;
            }

            fetch(url, {
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest'
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (paymentDiv && data.allPayments) {
                        paymentDiv.innerHTML = data.allPayments;


                        window.history.pushState({}, '', url);
                    }
                })
                .catch(error => console.error('Error:', error));
        }
    });
</script>
