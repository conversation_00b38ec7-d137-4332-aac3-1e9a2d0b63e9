<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Queue\SerializesModels;

class GuardianInvitationMail extends Mailable
{
    use Queueable, SerializesModels;

    public  $coachInviteToken;
    public $guardianName;
    public $playerName;
    public $teamName;
    public $programName;
    public $startingDate;
    public $coach;

    /**
     * Create a new message instance.
     *
     * @param  string  $coachInviteToken
     * @param  string  $guardianName
     * @param  string  $playerName
     * @param  string  $teamName
     * @param  string  $programName
     * @param  string  $startingDate
     * @param  object  $coach
     * @return void
     */
    public function __construct( $coachInviteToken, $guardianName, $playerName, $teamName, $programName, $startingDate, $coach)
    {
        $this->coachInviteToken =  $coachInviteToken;
        $this->guardianName = $guardianName;
        $this->playerName = $playerName;
        $this->teamName = $teamName;
        $this->programName = $programName;
        $this->startingDate = $startingDate;
        $this->coach = $coach;
    }

    /**
     * Get the message envelope.
     */
    public function envelope(): Envelope
    {
        return new Envelope(
            subject: 'Invitation to Sign Up for Upcoming League on MassPremierCourts.com',
        );
    }

    /**
     * Get the message content definition.
     */
    public function content(): Content
    {
        return new Content(
            view: 'mail.guardian_invitation',
        );
    }

    /**
     * Get the attachments for the message.
     *
     * @return array<int, \Illuminate\Mail\Mailables\Attachment>
     */
    public function attachments(): array
    {
        return [];
    }
}

