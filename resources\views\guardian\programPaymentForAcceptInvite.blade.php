@extends('layouts.app')

@section('title', 'payment')


@section('content')

    <div id="errorMessage">
        <span id="errorText"></span>
    </div>

    <section class="page_title text-center pt-5">
        <h1 class="text-uppercase lh-1 mb-0">Payment</h1>
        <div class="mt-3">
            <h2 class="text-uppercase" style="color: inherit;">
                <span style="color: #0b4499; font-size: 1.2em;">$<span id="paymentAmount"></span></span>
            </h2>
        </div>
    </section>
    <section class="sec program-welcome">
        <div class="container">
            <div class="heading align-items-center d-flex flex-column text-center">
                <span class="hero-bar d-inline-flex mb-5"></span>
                <div class="d-flex flex-column-reverse">
                    <h3 class="text-uppercase mb-4">Payment Options:</h3>
                </div>
            </div>
            <div class="row justify-content-center">
                <div class="col-xl-10 payment-form">
                    <form class="form" id="paymentForm">
                        <!-- Full Amount Payment Option -->
                        <div class="row mb-4">
                            <div class="col-md-auto">
                                <input class="form-check-input" id="fullAmount" type="radio" name="payment"
                                    value="fullAmount" />
                                <label class="form-check-label text-uppercase" for="fullAmount">
                                    I will be paying this amount in full
                                </label>
                            </div>
                        </div>

                        <!-- Specific Amount Payment Option -->
                        <div class="row align-items-center mb-4">
                            <div class="col-md-auto">
                                <input class="form-check-input" id="specificAmount" type="radio" name="payment"
                                    value="specificAmount" />
                                <label class="form-check-label text-uppercase" for="specificAmount">
                                    I will be paying
                                </label>
                            </div>
                            <div class="col-md-auto">
                                <input class="form-control" id="specificMoney" type="number" step="0.01"
                                    placeholder="Enter amount" />
                            </div>
                        </div>

                        <!-- Recurring Payments Option -->
                        <div class="row align-items-center mb-4">
                            <div class="col-md-auto">
                                <input class="form-check-input" id="recurringPayments" type="radio" name="payment"
                                    value="recurringPayments" />
                                <label class="form-check-label text-uppercase" for="recurringPayments">
                                    I will be making monthly recurring payments of
                                </label>
                            </div>
                            <div class="col-md-auto">
                                <input class="form-control" id="recurringAmount" type="number"
                                    placeholder="Enter amount" />
                            </div>
                        </div>

                        <div class="row align-items-center mb-4">
                            <div class="col-md-auto">
                                <input class="form-check-input" id="otherGuardian" type="radio" name="payment"
                                    value="otherGuardian" />
                                <label class="form-check-label text-uppercase" for="invoiceEmail">
                                    This payment will be paid by
                                </label>
                            </div>
                            <div class="col-md-auto">
                                <input class="form-control" id="invoiceEmail" type="email"
                                    placeholder="Enter email address" />
                            </div>
                        </div>

                        <!-- Submit Button -->
                        <div class="row mt-5">
                            <div class="col text-center">
                                <button type="submit" class="cta px-4 py-2">Proceed to Payment</button>
                            </div>
                        </div>


                    </form>
                </div>
            </div>

        </div>
    </section>
    <script src="https://cdn.jsdelivr.net/npm/choices.js/public/assets/scripts/choices.min.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const paymentData = @json(session('payment_data_forAcceptInvite'));
            if (paymentData && paymentData.totalAmountToBePaid) {
                document.getElementById('paymentAmount').textContent = paymentData.totalAmountToBePaid;
            }

            const fullAmountRadio = document.getElementById('fullAmount');
            const specificAmountRadio = document.getElementById('specificAmount');
            const recurringPaymentsRadio = document.getElementById('recurringPayments');
            const otherGuardianRadio = document.getElementById('otherGuardian');
            const invoiceEmail = document.getElementById('invoiceEmail');
            recurringInput = document.getElementById('recurringAmount');

            const errorMessageDiv = document.getElementById('errorMessage');
            const errorText = document.getElementById('errorText');

            function showError(message) {
                errorText.textContent = message;
                errorMessageDiv.style.display = 'block';
                setTimeout(() => {
                    errorMessageDiv.style.display = 'none';
                }, 2000);
            }

            if (paymentData.programPayment == "split") {

                recurringInput = document.getElementById('recurringAmount');
                recurringInput.disabled = true;

                recurringPaymentsRadio.disabled = true;
                fullAmountRadio.disabled = false;
                specificAmountRadio.disabled = false;
                otherGuardianRadio.disabled = false;
            } else if (paymentData.programPayment == "full") {

                let recurringInput = document.getElementById('recurringAmount');
                let specificMoney = document.getElementById('specificMoney');
                specificMoney.disabled = true;
                recurringInput.disabled = true;
                recurringPaymentsRadio.disabled = true;
                fullAmountRadio.disabled = false;
                specificAmountRadio.disabled = true;
                otherGuardianRadio.disabled = false;

            } else {

                let recurringInput = document.getElementById('recurringAmount');
                let specificMoney = document.getElementById('specificMoney');
                specificMoney.disabled = true;
                recurringInput.disabled = false;
                recurringInput.value = paymentData.minimum_recurring_amount;
                recurringPaymentsRadio.disabled = false;
                fullAmountRadio.disabled = false;
                specificAmountRadio.disabled = true;
                otherGuardianRadio.disabled = false;
            }


            const paymentForm = document.getElementById('paymentForm');
            paymentForm.addEventListener('submit', function(e) {
                e.preventDefault();

                if (paymentData.programPayment == "recurring") {
                    if (recurringInput.value < paymentData.minimum_recurring_amount) {
                        showError('You have to pay the minimum recurring amount');

                        return;
                    }
                }


                if (!fullAmountRadio.checked && !recurringPaymentsRadio.checked && !specificAmountRadio
                    .checked && !otherGuardianRadio.checked) {
                    showError('Please select a valid payment option');
                    return;
                }




                let paymentType;
                let paymentAmount = 0;
                let email = '';

                if (fullAmountRadio.checked) {
                    paymentType = 'fullAmount';
                    paymentAmount = paymentData.amount;
                } else if (specificAmountRadio.checked) {
                    paymentType = 'specificAmount';
                    paymentAmount = parseFloat(specificMoney.value);
                } else if (recurringPaymentsRadio.checked) {
                    paymentType = 'recurringPayments';
                    let recurringInput = document.getElementById('recurringAmount');
                    paymentAmount = parseFloat(recurringInput.value);
                } else if (otherGuardianRadio.checked) {
                    paymentType = 'otherGuardian';
                    email = invoiceEmail.value;
                }


                if ((paymentType === 'specificAmount' || paymentType === 'recurringPayments') && isNaN(
                        paymentAmount)) {
                    showError('Please enter valid details')

                    return;
                }

                if (paymentType === 'otherGuardian' && !email) {
                    showError('Please enter a valid email address.');

                    return;
                }


                const formData = {
                    user_id: paymentData.user_id,
                    program_id: paymentData.program_id,
                    payment_type: paymentType,
                    paidAmount: paymentAmount,
                    totalAmountToBePaid: paymentData.totalAmountToBePaid,
                    email: email,
                    player_ids: paymentData.player_ids,
                    team_id: paymentData.teamId
                };

                const url = route('guardian.paymentMethod');

                fetch(url, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')
                                .getAttribute('content')
                        },
                        body: JSON.stringify(formData),
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {

                            window.location.href = data.redirect_url; // Redirect to card details
                        } else {

                            showError('Payment failed: ' + data.message);

                        }
                    })
                    .catch(error => {

                        console.error('Error submitting payment:', error);
                        showError('An error occurred. Please try again.');
                    });
            });
        });
    </script>
@endsection
