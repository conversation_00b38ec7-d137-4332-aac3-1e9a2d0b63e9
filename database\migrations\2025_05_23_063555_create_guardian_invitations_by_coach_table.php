<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('guardian_invitations_by_coach', function (Blueprint $table) {
            $table->id();
             $table->foreignId('coach_id')->constrained('users')->onDelete('cascade');
            $table->string('guardian_email');
            $table->string('guardian_name');
            $table->string('player_name');
            $table->string('token')->unique();
            $table->enum('status', ['pending', 'accepted'])->default('pending');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('guardian_invitations_by_coach');
    }
};
