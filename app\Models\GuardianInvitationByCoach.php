<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class GuardianInvitationByCoach extends Model
{
    use HasFactory;

    protected $table = 'guardian_invitations_by_coach';

    protected $fillable = [
        'coach_id',
        'guardian_email',
        'guardian_name',
        'player_name',
        'token',
        'status',
    ];
    public function coach()
    {
        return $this->belongsTo(User::class, 'coach_id');
    }
}

