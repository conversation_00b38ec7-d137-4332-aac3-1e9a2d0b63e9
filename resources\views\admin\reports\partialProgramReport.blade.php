<div class="tables_heading table-program table-responsive">
    <table class="table user-management-table">
        <thead>
            <tr>
                <th style="width: 40px" class="py-4">&nbsp;</th>
                <th style="width: 200px" class="py-4">Name</th>
                <th style="width: 150px" class="py-4">Sport</th>
                <th style="width: 80px" class="py-4">Gender</th>
                <th style="width: 60px" class="py-4">Age</th>
                <th style="width: 100px" class="py-4">Dates</th>
                <th style="width: 150px" class="py-4">Days</th>
                <th style="width: 120px" class="py-4">Enrollment</th>
                <th style="width: 100px" class="py-4">Enrolled</th>
                <th style="width: 100px" class="py-4">Cost</th>
                <th style="width: 120px" class="py-4">Payment</th>
                <th style="width: 200px">&nbsp;</th>
            </tr>
        </thead>
    </table>
</div>

@foreach ($programData as $program)
    <div class="tables_collapse_heading table-heading-grey">
        <table class="table table-hover user-management-table">
            <tr>
                <td style="width: 40px">
                    <div class="icon">
                        <i class="bi bi-caret-up-fill toggle-arrow" style="color:#0B4499; cursor: pointer;"
                            data-toggle="{{ $program['program_slug'] }}"></i>
                    </div>
                </td>
                <td style="width: 200px" class="text-custom">{{ $program['name'] }}</td>
                <td style="width: 150px" class="text-custom">{{ $program['sport'] }}</td>
                <td style="width: 80px" class="text-custom">{{ $program['gender'] }}</td>
                <td style="width: 60px" class="text-custom">
                    {{ $program['age_restriction_from'] }}-{{ $program['age_restriction_to'] }}</td>
                <td style="width: 100px" class="text-custom">
                    {{ $program['start_date'] }}<br>{{ $program['end_date'] }}
                </td>
                <td style="width: 150px" class="text-custom">
                    {{ implode(', ', $program['frequency_days']) }}<br>
                    {{ implode('-', array_map('ucfirst', explode('-', strtolower($program['frequency'])))) }}
                </td>
                <td style="width: 120px" class="text-custom">{{ $program['number_of_registers'] }}</td>
                <td style="width: 100px" class="text-custom">{{ $program['registrations'] }}</td>
                <td style="width: 100px" class="text-custom">${{ $program['cost'] }}</td>
                <td style="width: 120px" class="text-custom">{{ $program['payment'] }}</td>
                <td style="width: 200px">
                    <table class="user-management-actions-table">
                        <tr>
                            <td class="text-center pe-3" data-program-slug="{{ $program['program_slug'] }}">
                                <i class="bi bi-filetype-xls export-xls-icon"
                                    style="color: #0B4499; font-size: 1.5rem;"></i>
                            </td>
                            <td class="text-center pe-3">
                                <i class="bi bi-printer-fill print-icon"
                                    data-program-slug="{{ $program['program_slug'] }}"
                                    style="color: #0B4499; font-size: 1.5rem;"></i>
                            </td>
                            <td class="text-center pe-3">
                                <a href="{{ route('admin.program.edit', $program['program_slug']) }}"
                                    class="action edit" id="edit-admin">
                                    <img src="{{ asset('images/edit-icon.svg') }}" alt="Edit" width="20"
                                        height="20" />
                                </a>
                            </td>
                            <td class="text-center">
                                <form id="deleteProgram-{{ $program['program_slug'] }}"
                                    onsubmit="showConfirmation(event, 'deleteProgram-{{ $program['program_slug'] }}')"
                                    action="{{ route('admin.program.destroy', $program['program_slug']) }}"
                                    method="POST" class="delete-form">
                                    @csrf
                                    @method('DELETE')
                                    <button type="submit" class="action edit bg-transparent border-0 p-0">
                                        <img src="{{ asset('images/delete-icon.svg') }}" alt="Delete" width="20"
                                            height="20" />
                                    </button>
                                </form>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
        </table>
    </div>
    <div class="table_collapse_table table-program table-responsive table-grey">
        @if ($program['type'] == 'Team')
            <table class="table table-hover team-table d-none" width="100%"
                id="program-table{{ $program['program_slug'] }}">
                <tr>
                    <th width="40">
                        &nbsp;</th>
                    <th width="200">Team Name</th>
                    <th width="200">Coach</th>
                    <th width="250">Email</th>
                    <th width="250">Assistant Coach Email</th>
                    <th width="200">Player Count</th>
                    <th width="200">Team Balance</th>
                    <th width="200" class="empty">&nbsp;</th>
                </tr>
                @foreach ($program['teams'] as $team)
                    <tr>
                        <td>&nbsp;</td>
                        <td>{{ $team['team_name'] }}</td>
                        <td>{{ $team['coach_firstName'] }} {{ $team['coach_lastName'] }}</td>

                        <td>{{ $team['coach_email'] }}</td>
                        <td>{{ $team['assistantCoachEmail'] ?? 'N/A' }}</td>
                        <td class="pl-1">{{ $team['player_count'] }}</td>
                        <td>${{ number_format($team['team_balance'], 2) }}</td>
                        <td>
                            <table>
                                <tr>
                                    <td class="pe-5">
                                        <a href="{{ route('admin.editTeams', ['program' => $program['program_slug'], 'team' => $team['team_id'], 'coach' => $team['coach_slug']]) }}"
                                            class="action edit">
                                            <img src="{{ asset('images/edit-icon.svg') }}" alt="Edit"
                                                width="20" height="20" />
                                        </a>
                                    </td>
                                    <td>
                                        <form id="removeTeamFromProgram-{{ $team['team_id'] }}"
                                            onsubmit="showConfirmation(event, 'removeTeamFromProgram-{{ $team['team_id'] }}')"
                                            action="{{ route('admin.removeTeamFromProgram', ['program' => $program['program_slug'], 'team' => $team['team_id'], 'coach' => $team['coach_slug']]) }}"
                                            method="POST" class="inline-form mb-0">
                                            @csrf
                                            @method('DELETE')
                                            <button type="submit" class="action edit bg-transparent border-0 p-0">
                                                <img src="{{ asset('images/delete-icon.svg') }}" alt="Delete"
                                                    width="18" height="20" />
                                            </button>
                                        </form>
                                    </td>

                                </tr>
                            </table>
                        </td>
                    </tr>
                @endforeach
            </table>
            <button class="cta openModalButton d-none" id="add-team-btn{{ $program['program_slug'] }}"
                data-program-id={{ $program['program_slug'] }}
                style="font-size: 10px; line-height: 1; font-family: 'Poppins', sans-serif; margin-left: 50px; margin-bottom: 20px;">
                ADD Team to program
            </button>
        @elseif ($program['type'] == 'Individual' || $program['type'] == 'AAU' || $program['type'] == 'Tryout')
            <table class="table table-hover team-table d-none" width="100%"
                id="program-table{{ $program['program_slug'] }}">
                <tr>
                    <th width="40">&nbsp;</th>
                    <th width="200">Player Name</th>
                    <th width="200">Email</th>
                    <th width="200">Guardian Email</th>
                    <th width="200">Program Dates</th>
                    <th width="200" class="empty"></th>
                    <th width="200" class="empy"></th>
                    <th width="200" class="empty">&nbsp;</th>
                </tr>
                @foreach ($program['players'] as $individual)
                    <tr>
                        <td>&nbsp;</td>
                        <td>{{ $individual['player_name'] }}</td>
                        <td>{{ $individual['player_email'] }}</td>
                        <td>{{ $individual['guardian_email'] }}</td>
                        <td>{{ $individual['program_dates']['start_date'] }} -
                            {{ $individual['program_dates']['end_date'] }}</td>
                        <td></td>
                        <td></td>
                        <td>
                            <table>
                                <tr>
                                    <td class="pe-5">
                                        <a href="#" class="action edit">
                                            <img src="{{ asset('images/edit-icon.svg') }}" alt="Edit"
                                                width="20" height="20" />
                                        </a>
                                    </td>
                                    <td>
                                        <form id="removePlayerForm-{{ $individual['player_id'] }}"
                                            action="{{ route('admin.removePlayerFromProgram', ['program' => $program['program_slug'], 'player' => $individual['player_id']]) }}"
                                            method="POST"
                                            onsubmit="showConfirmation(event, 'removePlayerForm-{{ $individual['player_id'] }}')"
                                            class="inline-form mb-0">
                                            @csrf
                                            @method('DELETE')
                                            <button type="submit" class="action edit bg-transparent border-0 p-0">
                                                <img src="{{ asset('images/delete-icon.svg') }}" alt="Delete"
                                                    width="18" height="20" />
                                            </button>
                                        </form>
                                    </td>
                                </tr>
                            </table>
                        </td>
                    </tr>
                @endforeach



                {{-- @foreach ($program['invited_players'] as $invitedPlayer)
                    <tr>
                        <td>&nbsp;</td>
                        <td>{{ $invitedPlayer['first_name'] }} {{ $invitedPlayer['last_name'] }}</td>
                        <td>{{ $invitedPlayer['email'] }}</td>
                        <td>{{ $invitedPlayer['guardian_email'] }}</td>
                        <td>{{ $program['start_date'] }} - {{ $program['end_date'] }}</td>
                        <td>{{ $invitedPlayer['status'] }}</td>
                        <td></td>
                        <td>
                            <table>
                                <tr>
                                    <td class="pe-5">
                                        <a href="#" class="action edit">
                                            <img src="{{ asset('images/edit-icon.svg') }}" alt="Edit"
                                                width="20" height="20" />
                                        </a>
                                    </td>
                                    <td>
                                        <form id="removeInvitedPlayerForm-{{ $invitedPlayer['id'] }}"
                                            action="{{ route('admin.removeInvitedPlayerFromProgram', ['program' => $program['program_slug'], 'player' => $invitedPlayer['id']]) }}"
                                            method="POST"
                                            onsubmit="showConfirmation(event, 'removeInvitedPlayerForm-{{ $invitedPlayer['id'] }}')"
                                            class="inline-form mb-0">
                                            @csrf
                                            @method('DELETE')
                                            <button type="submit" class="action edit bg-transparent border-0 p-0">
                                                <img src="{{ asset('images/delete-icon.svg') }}" alt="Delete"
                                                    width="18" height="20" />
                                            </button>
                                        </form>
                                    </td>
                                </tr>
                            </table>
                        </td>
                    </tr>
                @endforeach --}}
            </table>
            <button class="cta playerModalButton d-none" id="add-player-btn{{ $program['program_slug'] }}"
                data-program-id={{ $program['program_slug'] }}
                style="font-size: 10px; line-height: 1; font-family: 'Poppins', sans-serif; margin-left: 50px; margin-bottom: 20px;">
                ADD player to program
            </button>
        @endif
    </div>
@endforeach
