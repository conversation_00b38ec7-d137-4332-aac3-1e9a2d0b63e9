<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class TeamCoach extends Model
{
    use HasFactory;
    protected $table = 'team_coach';

      protected $fillable = [
        'team_id',
          'coach_id',
          'is_primary'
    ];


      public function team()
    {
        return $this->belongsTo(Team::class);
    }

    public function coaches()
    {
        return $this->belongsToMany(User::class, 'team_coach')
                    ->withPivot('is_primary');
    }

    public function preferredCoach()
    {
        return $this->coaches()->where(function ($query) {

            $query->where('team_coach.is_primary', false)
                  ->orWhere(function ($subQuery) {
                      $subQuery->where('team_coach.is_primary', true);
                  });
        })->first();
    }

}
