<?php

namespace App\Http\Controllers;

use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Password;
use Illuminate\Support\Str;
use Illuminate\Auth\Events\PasswordReset;
use Illuminate\Support\Facades\Artisan;

class AuthController extends Controller
{
public function login(Request $request)
{
    $data = $request->validate([
        'email' => 'required|string|email',
        'password' => 'required|string',
    ]);


    $user = User::with('roles')->where('email', $data['email'])->first();

    if (!$user) {
        return redirect()->back()->withErrors(['email' => 'The provided email does not match our records.']);
    }


    if (!Hash::check($data['password'], $user->password)) {
        return redirect()->back()->withErrors(['password' => 'The provided password is incorrect.']);
    }

    // Login the user
    Auth::login($user);


    $roles = $user->roles->pluck('name');


    if ($roles->contains('guardian') && $roles->contains('coach')) {

        if ($user->current_role === 'guardian' || $user->current_role === null) {
            return redirect()->route('guardian.dashboard');
        } elseif ($user->current_role === 'coach') {
            return redirect()->route('coach.dashboard');
        }
    } elseif ($roles->contains('admin')) {
        return redirect()->route('admin.dashboard');
    } elseif ($roles->contains('coach')) {
        $user->update(['current_role' => 'coach']);
        return redirect()->route('coach.dashboard');
    } elseif ($roles->contains('guardian')) {
        $user->update(['current_role' => 'guardian']);
        return redirect()->route('guardian.dashboard');
    } elseif ($roles->contains('player')) {
        return redirect()->route('player.dashboard');
    }

    return redirect()->route('loginPage')->withErrors(['error' => 'Unauthorized role detected.']);
}








      public function logout(Request $request): \Illuminate\Http\RedirectResponse
      {

        // delete the data from session

        Auth::logout();


        $request->session()->invalidate();
        $request->session()->flush();
        $request->session()->regenerateToken();

          return redirect()->route('loginPage')->withHeaders([
        'Cache-Control' => 'no-cache, no-store, must-revalidate',
        'Pragma' => 'no-cache',
        'Expires' => '0',
    ]);
    }



      public function forgotPasswordPage(Request $request): \Illuminate\Contracts\View\View|\Illuminate\Contracts\View\Factory|\Illuminate\Foundation\Application
      {
       return view('auth.forgotPasswordPage');
   }


    public function forgotPassword(Request $request){
       $request->validate([
        'email'=>'required|email'
       ]);
       $status=Password::sendResetLink((
        $request->only('email')
       ));

       if ($status === Password::RESET_LINK_SENT) {
       return redirect()->route('forgotPasswordPage')->with([
            'success' => 'Reset link has been sent. Please check your mail.',
            'email' => $request->email
        ]);
    }
    return back()->withErrors(['email' => trans($status)]);
   }






    public function resetPassword (Request $request) {
    $request->validate([
        'token' => 'required',
        'email' => 'required|email',
        'password' => 'required|min:8|confirmed',
    ]);

    $status = Password::reset(
        $request->only('email', 'password', 'password_confirmation', 'token'),
        function (User $user, string $password) {
            $user->forceFill([
                'password' => Hash::make($password)
            ])->setRememberToken(Str::random(60));

            $user->save();

            event(new PasswordReset($user));
        }
    );

    return $status === Password::PASSWORD_RESET
                ? redirect()->route('loginPage')->with('success','Password updated Successfully')
                : back()->withErrors(['email' => [__($status)]]);
}
}
