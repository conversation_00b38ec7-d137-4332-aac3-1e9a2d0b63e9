<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Queue\SerializesModels;

class SendInvitationToCoach extends Mailable
{
    use Queueable, SerializesModels;

    public $coach;
    public $team;
    public $user;

    /**
     * Create a new message instance.
     *
     * @param  \App\Models\User  $coach
     * @param  \App\Models\Team  $team
     * @param  \App\Models\User  $user
     * @return void
     */
    public function __construct($coach, $team, $user)
    {
        $this->coach = $coach;
        $this->team = $team;
        $this->user = $user;
    }

    /**
     * Get the message envelope.
     */
    public function envelope(): Envelope
    {
        return new Envelope(
            subject: 'Invitation to Join Team',
        );
    }

    /**
     * Get the message content definition.
     */
    public function content(): Content
    {
        return new Content(
            view: 'mail.InviteCoach',
            with: [
                'coach' => $this->coach,
                'team' => $this->team,
                'user' => $this->user,
            ],
        );
    }

    /**
     * Get the attachments for the message.
     *
     * @return array<int, \Illuminate\Mail\Mailables\Attachment>
     */
    public function attachments(): array
    {
        return [];
    }
}

