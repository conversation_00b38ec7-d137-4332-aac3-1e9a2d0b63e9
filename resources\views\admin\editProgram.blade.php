@extends('layouts.app')
@section('title', 'Edit Program')
@section('content')
    <section class="page_title text-center pt-5">
        <h1 class="text-uppercase lh-1 mb-0">Edit Program</h1>
    </section>

    @if (session('success'))
        <div id="successMessageForSession">
            <span id="successText">{{ session('success') }}</span>
        </div>
    @endif

    @if (session('error'))
        <div id="errorMessageForSession">
            <span id="errorText">{{ session('error') }}</span>
        </div>
    @endif

    @if (
        $errors->hasAny([
            'early_bird_specials_date',
            'early_bird_from.*',
            'early_bird_to.*',
            'price_before.*',
            'price_on_or_after.*',
        ]))
        <div id="errorMessageForSession">
            If Early Bird Specials have been checked, you must fill in all the details correctly.
        </div>
    @endif

    <div class="backButtonforAddPrograms">
        <a class="cta"
            href="{{ url()->previous() !== url()->current() ? url()->previous() : route('admin.program.allPrograms') }}">
            Back
        </a>
    </div>



    <section class="sec form mb-5">
        <div class="container">
            <div class="heading align-items-center d-flex flex-column text-center mb-5">
                <span class="hero-bar d-inline-flex"></span>
            </div>
            <div class="row justify-content-center">
                <div class="col-xl-10 col-xxl-9">
                    <form class="row" action="{{ route('admin.program.update', $program->slug) }}" method="POST">
                        @csrf
                        @method('PUT')

                        <!-- Program Name -->
                        <div class="mb-4 col-md-6">
                            <label class="text-uppercase form-label" for="name">Program Name</label>
                            <input class="form-control @error('name') is-invalid @enderror" id="programName" type="text"
                                name="name" value="{{ old('name', $program->name) }}" />
                            @error('name')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <!-- Program Type -->
                        <div class="mb-4 col-md-6">
                            <label class="form-label text-uppercase" for="type">Program Type</label>
                            <div class="select-arrow position-relative">
                                <select class="form-control @error('type') is-invalid @enderror" id="type"
                                    name="type">
                                    <option value="" {{ old('type', $program->type) == '' ? 'selected' : '' }}>Select
                                        Program Type</option>
                                    <option value="Individual"
                                        {{ old('type', $program->type) == 'Individual' ? 'selected' : '' }}>Individual
                                    </option>
                                    <option value="Team" {{ old('type', $program->type) == 'Team' ? 'selected' : '' }}>
                                        Team</option>
                                    <option value="AAU" {{ old('type', $program->type) == 'AAU' ? 'selected' : '' }}>AAU
                                    </option>
                                    <option value="Tryout" {{ old('type', $program->type) == 'Tryout' ? 'selected' : '' }}>
                                        Tryout</option>
                                </select>
                                <span class="arrow"><i class="bi bi-chevron-down"></i></span>
                                @error('type')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>


                        <!-- Sub Program Name -->
                        <div class="mb-4 col-md-6">
                            <label class="text-uppercase form-label" for="sub_program">Sub Program Name</label>
                            <input class="form-control @error('sub_program') is-invalid @enderror" id="subProgramName"
                                name="sub_program" type="text"
                                value="{{ old('sub_program', $program->sub_program) }}" />
                            @error('sub_program')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <!-- Location -->
                        <div class="mb-4 col-md-6">
                            <label class="text-uppercase form-label" for="location">Location</label>
                            <input class="form-control @error('location') is-invalid @enderror" id="location"
                                name="location" type="text" value="{{ old('location', $program->location) }}" />
                            @error('location')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <!-- Sports -->
                        <div class="mb-4 col-md-6">
                            <label class="form-label text-uppercase" for="sport">Sport</label>
                            <div class="select-arrow position-relative">
                                <select class="form-control @error('sport') is-invalid @enderror" id="sports"
                                    name="sport">
                                    <option value="" {{ old('sport', $program->sport) === '' ? 'selected' : '' }}>
                                        Select</option>
                                    <option value="basketball"
                                        {{ old('sport', $program->sport) === 'basketball' ? 'selected' : '' }}>Basketball
                                    </option>
                                    <option value="volleyball"
                                        {{ old('sport', $program->sport) === 'volleyball' ? 'selected' : '' }}>Volleyball
                                    </option>
                                    <option value="pickleball"
                                        {{ old('sport', $program->sport) === 'pickleball' ? 'selected' : '' }}>Pickleball
                                    </option>
                                </select>
                                <span class="arrow"><i class="bi bi-chevron-down"></i></span>
                                @error('sport')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>

                        <!-- Gender -->
                        <div class="mb-4 col-md-6">
                            <label class="form-label text-uppercase" for="gender">Gender</label>
                            <div class="select-arrow position-relative">
                                <select class="form-control @error('gender') is-invalid @enderror" id="gender"
                                    name="gender">
                                    <option value="" {{ old('gender', $program->gender) == '' ? 'selected' : '' }}>
                                        Select</option>
                                    <option value="boys"
                                        {{ old('gender', $program->gender) == 'boys' ? 'selected' : '' }}>Boys</option>
                                    <option value="girls"
                                        {{ old('gender', $program->gender) == 'girls' ? 'selected' : '' }}>Girls</option>

                                    <option value="coed"
                                        {{ old('gender', $program->gender) == 'coed' ? 'selected' : '' }}>Co-ed</option>
                                </select>
                                <span class="arrow"><i class="bi bi-chevron-down"></i></span>
                                @error('gender')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>

                        <!-- Age Restriction -->
                        <div class="col-12 mt-4">
                            <div class="form-label text-uppercase">Age Restriction (optional)</div>
                            <div class="row">
                                <div class="mb-4 col-md-6">
                                    <label class="text-uppercase form-sub-label" for="age_restriction_from">Start
                                        Age</label>
                                    <input class="form-control @error('age_restriction_from') is-invalid @enderror"
                                        id="age_restriction_from" name="age_restriction_from" type="number"
                                        value="{{ old('age_restriction_from', $program->age_restriction_from) }}" />
                                    @error('age_restriction_from')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                                <div class="mb-4 col-md-6">
                                    <label class="text-uppercase form-sub-label" for="age_restriction_to">End Age</label>
                                    <input class="form-control @error('age_restriction_to') is-invalid @enderror"
                                        id="age_restriction_to" name="age_restriction_to" type="number"
                                        value="{{ old('age_restriction_to', $program->age_restriction_to) }}" />
                                    @error('age_restriction_to')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <!-- Grade -->
                        <div class="mb-4 col-md-6">
                            <label class="form-label text-uppercase" for="grade">Grade</label>
                            <div class="select-arrow position-relative">
                                <select class="form-control @error('grade') is-invalid @enderror" id="grade"
                                    name="grade">
                                    <option value="" {{ old('grade', $program->grade) == '' ? 'selected' : '' }}>
                                        Select
                                    </option>
                                    <option value="6th" {{ old('grade', $program->grade) == '6th' ? 'selected' : '' }}>
                                        6th
                                    </option>
                                    <option value="7th" {{ old('grade', $program->grade) == '7th' ? 'selected' : '' }}>
                                        7th
                                    </option>
                                    <option value="8th" {{ old('grade', $program->grade) == '8th' ? 'selected' : '' }}>
                                        8th
                                    </option>
                                    <option value="9th" {{ old('grade', $program->grade) == '9th' ? 'selected' : '' }}>
                                        9th
                                    </option>
                                    <option value="10th"
                                        {{ old('grade', $program->grade) == '10th' ? 'selected' : '' }}>
                                        10th
                                    </option>
                                    <option value="adult"
                                        {{ old('grade', $program->grade) == 'adult' ? 'selected' : '' }}>Adult
                                    </option>
                                </select>
                                <span class="arrow"><i class="bi bi-chevron-down"></i></span>
                                @error('grade')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>

                        <!-- Birth Date Cutoff -->
                        <div class="mb-4 col-md-6">
                            <label class="form-label text-uppercase" for="birth_date_cutoff">Birth Date Cutoff</label>
                            <input class="form-control @error('birth_date_cutoff') is-invalid @enderror"
                                id="birth_date_cutoff" name="birth_date_cutoff" type="date"
                                value="{{ old('birth_date_cutoff', $program->birth_date_cutoff) }}" />
                            @error('birth_date_cutoff')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <!-- Registration -->
                        <div class="col-12 mt-4">
                            <div class="form-label text-uppercase">Registration</div>
                            <div class="row">
                                <div class="mb-4 col-md-6">
                                    <label class="text-uppercase form-sub-label"
                                        for="registration_opening_date">Registration Start</label>
                                    <input class="form-control @error('registration_opening_date') is-invalid @enderror"
                                        id="registration_opening_date" name="registration_opening_date" type="date"
                                        value="{{ old('registration_opening_date', $program->registration_opening_date) }}" />
                                    @error('registration_opening_date')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                                <div class="mb-4 col-md-6">
                                    <label class="text-uppercase form-sub-label"
                                        for="registration_closing_date">Registration End</label>
                                    <input class="form-control @error('registration_closing_date') is-invalid @enderror"
                                        id="registration_closing_date" name="registration_closing_date" type="date"
                                        value="{{ old('registration_closing_date', $program->registration_closing_date) }}" />
                                    @error('registration_closing_date')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <!-- Start Date and End Date -->
                        <div class="col-12 mt-4">
                            <div class="row">
                                <div class="mb-4 col-md-6">
                                    <label class="form-label text-uppercase" for="start_date">Start Date</label>
                                    <input class="form-control @error('start_date') is-invalid @enderror" id="start_date"
                                        name="start_date" type="date"
                                        value="{{ old('start_date', $program->start_date) }}" />
                                    @error('start_date')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                                <div class="mb-4 col-md-6">
                                    <label class="form-label text-uppercase" for="end_date">End Date</label>
                                    <input class="form-control @error('end_date') is-invalid @enderror" id="end_date"
                                        name="end_date" type="date"
                                        value="{{ old('end_date', $program->end_date) }}" />
                                    @error('end_date')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <!-- Start Time and End Time -->
                        <div class="col-12 mt-4">
                            <div class="row">
                                <div class="mb-4 col-md-6">
                                    <label class="form-label text-uppercase" for="start_time">Start Time</label>
                                    <input class="form-control @error('start_time') is-invalid @enderror" id="start_time"
                                        name="start_time" type="time"
                                        value="{{ old('start_time', $program->start_time) }}" />
                                    @error('start_time')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                                <div class="mb-4 col-md-6">
                                    <label class="form-label text-uppercase" for="end_time">End Time</label>
                                    <input class="form-control @error('end_time') is-invalid @enderror" id="end_time"
                                        name="end_time" type="time"
                                        value="{{ old('end_time', $program->end_time) }}" />
                                    @error('end_time')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <!-- Frequency -->
                        <div class="col-12 mt-4">
                            <div class="form-label text-uppercase">Frequency</div>

                            <div class="form-sub-label text-uppercase mb-3">Daily</div>
                            <div class="row">
                                @foreach (['mon', 'tue', 'wed', 'thur', 'fri', 'sat', 'sun'] as $day)
                                    <div class="mb-4 col-md">
                                        <div class="form-check">
                                            <input class="form-check-input @error('frequency_days') is-invalid @enderror"
                                                id="{{ $day }}" name="frequency_days[]" type="checkbox"
                                                value="{{ $day }}"
                                                {{ is_array(old('frequency_days', $program->frequency_days)) && in_array($day, old('frequency_days', $program->frequency_days)) ? 'checked' : '' }} />
                                            <label class="form-check-label text-uppercase"
                                                for="{{ $day }}">{{ strtoupper($day) }}</label>
                                        </div>
                                    </div>
                                @endforeach
                            </div>

                            <div class="form-sub-label text-uppercase mb-3">Weekly</div>
                            <div class="row">
                                @foreach (['every-week', 'every-other-week', 'once-per-month'] as $week)
                                    <div class="mb-4 col-md">
                                        <div class="form-check">
                                            <input class="form-check-input @error('frequency') is-invalid @enderror"
                                                id="{{ $week }}" name="frequency" type="radio"
                                                value="{{ $week }}"
                                                {{ old('frequency', $program->frequency) === $week ? 'checked' : '' }} />
                                            <label class="form-check-label text-uppercase"
                                                for="{{ $week }}">{{ strtoupper(str_replace('-', ' ', $week)) }}</label>
                                        </div>
                                    </div>
                                @endforeach
                            </div>

                            @error('frequency')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror

                        </div>

                        <div class="col-md-6 mt-4">
                            <div class="form-label text-uppercase">Enrollment (optional)</div>
                            <div class="form-sub-label text-uppercase mb-3">Registration Limit</div>
                            <div class="row align-items-center">
                                <div class="mb-4 col-md-2">
                                    <label class="text-uppercase visually-hidden form-label"
                                        for="number_of_registers">Number of Registers</label>
                                    <input class="form-control @error('number_of_registers') is-invalid @enderror"
                                        id="number_of_registers" name="number_of_registers" type="number"
                                        value="{{ old('number_of_registers', $program->number_of_registers) }}" />
                                    @error('number_of_registers')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                                <div class="mb-4 col-md">
                                    <div class="form-check">
                                        <input type="hidden" name="enable_waitlist" value="0">
                                        <input class="form-check-input @error('enable_waitlist') is-invalid @enderror"
                                            id="enable-waitlist" name="enable_waitlist" type="checkbox" value="1"
                                            {{ old('enable_waitlist', $program->enable_waitlist) ? 'checked' : '' }} />
                                        <label class="form-check-label p text-uppercase" for="enable-waitlist">Enable
                                            Waitlist</label>
                                    </div>
                                    @error('enable_waitlist')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <!-- Cost -->
                        <div class="col-md-6 mt-4">
                            <div class="form-label text-uppercase">Cost</div>
                            <div class="form-sub-label text-uppercase mb-3">&nbsp;</div>
                            <div class="row align-items-center">
                                <div class="mb-4 col-md-4">
                                    <label class="text-uppercase visually-hidden form-label" for="cost">Cost</label>
                                    <input class="form-control @error('cost') is-invalid @enderror"
                                        id="cost"style="width: 100%;" name="cost" type="number" step="0.01"
                                        value="{{ old('cost', $program->cost) }}" />
                                    @error('cost')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                                <div class="mb-4 col-md-8">
                                    <div class="form-check col-md-">
                                        <input type="hidden" name="enable_early_bird_specials" value="0">
                                        <input class="form-check-input" id="enable_early_bird_specials" type="checkbox"
                                            value="1" name="enable_early_bird_specials"
                                            {{ old('enable_early_bird_specials', $program->enable_early_bird_specials) ? 'checked' : '' }} />
                                        <label class="form-check-label p text-uppercase" for="enable_early_bird_specials">
                                            Enable Early Bird Specials
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div id="early_bird_pricing_section" class="col-md-12 mt-4 mb-4 to_set_mb3"
                            style="{{ old('enable_early_bird_specials', $program->enable_early_bird_specials) ? '' : 'display: none;' }}">
                            @if ($earlyBirdPricing->isEmpty())
                                <div class="col-md-12 mt-4 mb-4 to_set_mb3">
                                    <div class="form-label text-uppercase">Early Bird Pricing</div>
                                    <div class="row align-items-center">
                                        <div class="mb-4 col-md">
                                            <label class="text-uppercase form-sub-label" for="cost">From</label>
                                            <input class="form-control" id="cost" type="number" step="0.01"
                                                name="early_bird_from[]" value="{{ old('early_bird_from.0') }}" />
                                        </div>
                                        <div class="mb-4 col-md">
                                            <label class="text-uppercase form-sub-label" for="to">to</label>
                                            <input class="form-control" id="to" type="number" step="0.01"
                                                name="early_bird_to[]" value="{{ old('early_bird_to.0') }}" />
                                            @error('early_bird_to.0')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                        <div class="mb-4 col-md-3">
                                            <label class="text-uppercase form-sub-label" for="priceBefore">Price
                                                Before</label>
                                            <input class="form-control" id="priceBefore" type="number" step="0.01"
                                                name="price_before[]" value="{{ old('price_before.0') }}" />
                                            @error('price_before.*')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                        <div class="mb-4 col-md-3">
                                            <label class="text-uppercase form-sub-label" for="date">Date</label>
                                            <input class="form-control" id="date" type="date"
                                                name="early_bird_specials_date"
                                                value="{{ old('early_bird_specials_date') }}" />
                                            @error('early_bird_specials_date')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                        <div class="mb-4 col-md-3">
                                            <label class="text-uppercase form-sub-label" for="priceOnorAfter">Price On or
                                                After</label>
                                            <input class="form-control" id="priceOnorAfter" type="number"
                                                step="0.01" name="price_on_or_after[]"
                                                value="{{ old('price_on_or_after.0') }}" />
                                            @error('price_on_or_after.*')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>
                                    <div class="row align-items-center">
                                        <div class="mb-4 col-md">
                                            <label class="text-uppercase visually-hidden form-label"
                                                for="cost">From</label>
                                            <input class="form-control" id="cost" type="number" step="0.01"
                                                name="early_bird_from[]" value="{{ old('early_bird_from.1') }}" />
                                        </div>
                                        <div class="mb-4 col-md">
                                            <label class="text-uppercase visually-hidden form-label"
                                                for="to">to</label>
                                            <input class="form-control" id="to" type="number" step="0.01"
                                                name="early_bird_to[]" value="{{ old('early_bird_to.1') }}" />
                                        </div>
                                        <div class="mb-4 col-md-3">
                                            <label class="text-uppercase visually-hidden form-label"
                                                for="priceBefore">Price
                                                Before</label>
                                            <input class="form-control" id="priceBefore" type="number" step="0.01"
                                                name="price_before[]" value="{{ old('price_before.1') }}" />
                                        </div>
                                        <div class="col-md-3"></div>
                                        <div class="mb-4 col-md-3">
                                            <label class="text-uppercase visually-hidden form-label" for="priceOnorAfter">
                                                Price On
                                                or After</label>
                                            <input class="form-control" id="price_on_or_after[]" type="number"
                                                step="0.01" @error('price_on_or_after.1') is-invalid @enderror
                                                name="price_on_or_after[]" value="{{ old('price_on_or_after.1') }}" />
                                        </div>
                                    </div>
                                    <div class="row align-items-center">
                                        <div class="mb-4 col-md">
                                            <label class="text-uppercase visually-hidden form-label"
                                                for="cost">From</label>
                                            <input class="form-control" id="cost" type="number" step="0.01"
                                                name="early_bird_from[]" value="{{ old('early_bird_from.2') }}" />
                                            @error('early_bird_to.*')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                        <div class="mb-4 col-md">
                                            <label class="text-uppercase visually-hidden form-label"
                                                for="to">to</label>
                                            <input class="form-control" id="to" type="number" step="0.01"
                                                name="early_bird_to[]" value="{{ old('early_bird_to.2') }}" />
                                        </div>
                                        <div class="mb-4 col-md-3">
                                            <label class="text-uppercase visually-hidden form-label"
                                                for="priceBefore">Price
                                                Before</label>
                                            <input class="form-control" id="priceBefore" type="number" step="0.01"
                                                name="price_before[]" value="{{ old('price_before.2') }}" />
                                        </div>
                                        <div class="col-md-3"></div>
                                        <div class="mb-4 col-md-3">
                                            <label class="text-uppercase visually-hidden form-label"
                                                for="priceOnorAfter">Price On
                                                or After</label>
                                            <input class="form-control" id="priceOnorAfter" type="number"
                                                step="0.01" name="price_on_or_after[]"
                                                value="{{ old('price_on_or_after.2') }}" />
                                        </div>
                                    </div>
                                    <div class="row align-items-center">
                                        <div class="mb-4 col-md">
                                            <label class="text-uppercase visually-hidden form-label"
                                                for="cost">From</label>
                                            <input class="form-control" id="cost" type="number" step="0.01"
                                                name="early_bird_from[]" value="{{ old('early_bird_from.3') }}" />
                                        </div>
                                        <div class="mb-4 col-md">
                                            <label class="text-uppercase visually-hidden form-label"
                                                for="to">to</label>
                                            <input class="form-control" id="to" type="number" step="0.01"
                                                name="early_bird_to[]" value="{{ old('early_bird_to.3') }}" />
                                        </div>
                                        <div class="mb-4 col-md-3">
                                            <label class="text-uppercase visually-hidden form-label"
                                                for="priceBefore">Price
                                                Before</label>
                                            <input class="form-control" id="priceBefore" type="number" step="0.01"
                                                name="price_before[]" value="{{ old('price_before.3') }}" />
                                        </div>
                                        <div class="col-md-3"></div>
                                        <div class="mb-4 col-md-3">
                                            <label class="text-uppercase visually-hidden form-label"
                                                for="priceOnorAfter">Price On
                                                or After</label>
                                            <input class="form-control" id="priceOnorAfter" type="number"
                                                step="0.01" name="price_on_or_after[]"
                                                value="{{ old('price_on_or_after.3') }}" />
                                        </div>
                                    </div>
                                </div>
                            @else
                                <!-- Early Bird Pricing -->
                                <div class="col-md-12 mt-4 mb-4 to_set_mb3">
                                    <div class="form-label text-uppercase">Early Bird Pricing</div>
                                    @for ($index = 0; $index < 4; $index++)
                                        <div class="row align-items-center">
                                            <div class="mb-4 col-md">
                                                @if ($index == 0)
                                                    <label class="text-uppercase form-sub-label"
                                                        for="early_bird_from_{{ $index }}">From</label>
                                                @endif
                                                <input class="form-control" id="early_bird_from_{{ $index }}"
                                                    type="number" step="0.01" name="early_bird_from[]"
                                                    value="{{ old('early_bird_from.' . $index, $earlyBirdPricing[$index]->from ?? '') }}" />
                                            </div>
                                            <div class="mb-4 col-md">
                                                @if ($index == 0)
                                                    <label class="text-uppercase form-sub-label"
                                                        for="early_bird_to_{{ $index }}">To</label>
                                                @endif
                                                <input class="form-control" id="early_bird_to_{{ $index }}"
                                                    type="number" step="0.01" name="early_bird_to[]"
                                                    value="{{ old('early_bird_to.' . $index, $earlyBirdPricing[$index]->to ?? '') }}" />
                                            </div>
                                            <div class="mb-4 col-md-3">
                                                @if ($index == 0)
                                                    <label class="text-uppercase form-sub-label"
                                                        for="price_before_{{ $index }}">Price Before</label>
                                                @endif
                                                <input class="form-control" id="price_before_{{ $index }}"
                                                    type="number" step="0.01" name="price_before[]"
                                                    value="{{ old('price_before.' . $index, $earlyBirdPricing[$index]->price_before ?? '') }}" />
                                            </div>
                                            @if ($index == 0)
                                                <div class="mb-4 col-md-3">
                                                    <label class="text-uppercase form-sub-label"
                                                        for="early_bird_specials_date">Date</label>
                                                    <input class="form-control" id="early_bird_specials_date"
                                                        type="date" name="early_bird_specials_date"
                                                        value="{{ old('early_bird_specials_date', $program->early_bird_specials_date) }}" />
                                                </div>
                                            @else
                                                <div class="mb-4 col-md-3"></div>
                                            @endif

                                            <div class="mb-4 col-md-3">
                                                @if ($index == 0)
                                                    <label class="text-uppercase form-sub-label"
                                                        for="price_on_or_after_{{ $index }}">Price On or
                                                        After</label>
                                                @endif
                                                <input class="form-control" id="price_on_or_after_{{ $index }}"
                                                    type="number" step="0.01" name="price_on_or_after[]"
                                                    value="{{ old('price_on_or_after.' . $index, $earlyBirdPricing[$index]->price_on_or_after ?? '') }}" />
                                            </div>
                                        </div>
                                    @endfor
                                </div>

                            @endif
                        </div>

                        <!-- Program Description -->
                        <div class="mb-4 col-md-12">
                            <label class="form-label text-uppercase" for="program_description">Program Description
                                <span class="red-text">*</span></label>
                            <textarea class="form-control @error('program_description') is-invalid @enderror" id="program_description"
                                name="program_description" style="height: 140px">
                            {{ old('program_description', $program->program_description) }}
                        </textarea>
                            @error('program_description')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <!-- Payment -->
                        <div class="col-12 mt-4">
                            <div class="form-label text-uppercase">Payment</div>
                            <div class="row">
                                <div class="mb-4 col-md">
                                    <div class="form-check col-md-">
                                        <input class="form-check-input" id="FullPaymentOnly" type="radio"
                                            value="full" name="payment"
                                            {{ old('payment', $program->payment) === 'full' ? 'checked' : '' }} />
                                        <label class="form-check-label p text-uppercase" for="FullPaymentOnly">Full
                                            Payment only</label>
                                    </div>
                                </div>
                                <div class="mb-4 col-md">
                                    <div class="form-check col-md-">
                                        <input class="form-check-input" id="recurringPayment" type="radio"
                                            value="recurring" name="payment"
                                            {{ old('payment', $program->payment) === 'recurring' ? 'checked' : '' }} />
                                        <label class="form-check-label p text-uppercase" for="RecurringPayment">Recurring
                                            Payment</label>
                                    </div>
                                </div>
                                <div class="mb-4 col-md">
                                    <div class="form-check col-md-">
                                        <input class="form-check-input" id="SplitPayment" type="radio" value="split"
                                            name="payment"
                                            {{ old('payment', $program->payment) === 'split' ? 'checked' : '' }} />
                                        <label class="form-check-label p text-uppercase" for="SplitPayment">Split
                                            Payment</label>
                                    </div>
                                </div>
                            </div>
                        </div>


                        <div class="mb-4" id="minimumMonthlyPaymentDiv"
                            style="{{ old('payment', $program->payment) === 'recurring' ? 'display:block;' : 'display:none;' }}">
                            <label class="text-uppercase form-label" for="minimumMonthlyPayment">
                                Minimum Monthly Payment
                            </label>
                            <input class="form-control @error('minimumMonthlyPayment') is-invalid @enderror"
                                id="minimumMonthlyPayment" name="minimumMonthlyPayment" type="number" step="0.01"
                                min="0" placeholder="Enter amount in dollars"
                                value="{{ old('minimumMonthlyPayment', $program->minimum_recurring_amount) }}" />
                            @error('minimumMonthlyPayment')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="mb-4" id="status">
                            <label class="form-label text-uppercase" for="status">Status</label>
                            <div class="select-arrow position-relative">
                                <select class="form-control @error('status') is-invalid @enderror" id="status"
                                    name="status">
                                    <option value="" {{ old('status', $program->status) == '' ? 'selected' : '' }}>
                                        Select
                                    </option>
                                    <option value="draft"
                                        {{ old('status', $program->status) == 'draft' ? 'selected' : '' }}>Draft
                                    </option>
                                    <option value="public"
                                        {{ old('status', $program->status) == 'public' ? 'selected' : '' }}>Public
                                    </option>
                                    <option value="private"
                                        {{ old('status', $program->status) == 'private' ? 'selected' : '' }}>Private
                                    </option>
                                </select>
                                <span class="arrow"><i class="bi bi-chevron-down"></i></span>
                                @error('status')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>


                        <div class="d-flex justify-content-between align-items-center mt-5">
                            <div class="col-md-6 d-flex justify-content-end mt-5 mr-1">
                                <button class="cta py-0 mr-1" style="margin-right:1rem">Update Program <span
                                        class="d-none ms-3 cta-response"></span></button>
                            </div>
                    </form>
                    <div class="col-md-6 d-flex mt-5">
                        <form action="{{ route('admin.program.destroy', ['program' => $program->slug]) }}"
                            method="POST">
                            @csrf
                            @method('DELETE')
                            <button type="submit" class="cta py-0">Delete Program</button>
                        </form>

                    </div>
                </div>
            </div>
        </div>
        </div>
        <script src="{{ asset('tinymce/tinymce.min.js') }}"></script>

        <script>
            document.addEventListener('DOMContentLoaded', function() {
                const earlyBirdCheckbox = document.getElementById('enable_early_bird_specials');
                const earlyBirdPricingSection = document.getElementById('early_bird_pricing_section');

                function toggleEarlyBirdPricing() {
                    if (earlyBirdCheckbox.checked) {
                        earlyBirdPricingSection.style.display = '';
                    } else {
                        earlyBirdPricingSection.style.display = 'none';
                    }
                }


                toggleEarlyBirdPricing();


                earlyBirdCheckbox.addEventListener('change', toggleEarlyBirdPricing);


                const recurringPaymentRadio = document.getElementById('recurringPayment');
                const oneTimePaymentRadio = document.getElementById('FullPaymentOnly');
                const splitPaymentRadio = document.getElementById('SplitPayment');
                const minimumMonthlyPaymentDiv = document.getElementById('minimumMonthlyPaymentDiv');

                recurringPaymentRadio.addEventListener('change', togglePaymentDiv);
                oneTimePaymentRadio.addEventListener('change', togglePaymentDiv);
                splitPaymentRadio.addEventListener('change', togglePaymentDiv);

                function togglePaymentDiv() {
                    if (recurringPaymentRadio.checked) {
                        minimumMonthlyPaymentDiv.style.display = 'block';
                    } else {
                        minimumMonthlyPaymentDiv.style.display = 'none';
                    }
                }

                showSessionSuccessMessage();
                showSessionErrorMessage();
            });
        </script>

    </section>
    <script>
        tinymce.init({
            selector: 'textarea#program_description',
            width: 1000,
            height: 300,
            plugins: [
                'advlist', 'autolink', 'link', 'image', 'charmap', 'preview', 'anchor',
                'searchreplace', 'wordcount', 'code', 'fullscreen', 'insertdatetime',
                'table', 'print'
            ],
            toolbar: 'undo redo | bold italic underline | alignleft aligncenter alignright alignjustify | bullist numlist | link image | print fullscreen',
            menubar: false,
            content_style: 'body { font-family: Helvetica, Arial, sans-serif; font-size: 16px; }'
        });
    </script>


@endsection
