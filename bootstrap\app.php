<?php

use App\Http\Middleware\AuthMiddleware;
use App\Http\Middleware\CheckReferrer;
use App\Http\Middleware\CheckRole;
use App\Http\Middleware\CheckSession;
use App\Http\Middleware\IsloggedIn;
use Illuminate\Foundation\Application;
use Illuminate\Foundation\Configuration\Exceptions;
use Illuminate\Foundation\Configuration\Middleware;

return Application::configure(basePath: dirname(__DIR__))
    ->withRouting(
        web: __DIR__.'/../routes/web.php',
        api: __DIR__.'/../routes/api.php',
        commands: __DIR__.'/../routes/console.php',
        health: '/up',
    )
    ->withMiddleware(function (Middleware $middleware) {
        $middleware->alias([
            'role.check'=>CheckRole::class,
        ]);
    })
    ->withMiddleware(function (Middleware $middleware) {
        $middleware->alias([
            'auth'=>AuthMiddleware::class,
        ]);
    })
    ->withMiddleware(function (Middleware $middleware) {
        $middleware->alias([
            'IsloggedIn'=>IsloggedIn::class,
        ]);
    })
    ->withMiddleware(function(Middleware $middleware){
        $middleware->alias([
            'CheckReferrer'=>CheckReferrer::class,
        ]);
    })

    ->withMiddleware(function(Middleware $middleware){
        $middleware->alias([
            'checkSession'=>CheckSession::class,
        ]);
    })

    ->withExceptions(function (Exceptions $exceptions) {
        //
    })->create();
