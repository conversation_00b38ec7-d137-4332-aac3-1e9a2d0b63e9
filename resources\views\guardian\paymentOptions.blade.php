@extends('layouts.app')

@section('title', 'payment')


@section('content')

    <section class="page_title text-center pt-5">
        <h1 class="text-uppercase lh-1 mb-0">Payment</h1>

        <div class="mt-3">
            <h2 class="text-uppercase" style="color: inherit;" id="balanceDisplay">
                <span style="color: #0b4499; font-size: 1.2em;">
                    ${{ $formattedBalanceDue }}
                </span>
            </h2>
            <p id="noPaymentMessage" class="hidden">
                You don't have any payments to make.
            </p>
        </div>
    </section>
    <section class="sec program-welcome">
        <div class="container">
            <div class="heading align-items-center d-flex flex-column text-center">
                <span class="hero-bar d-inline-flex mb-5"></span>
                <div class="d-flex flex-column-reverse">
                    <h3 class="text-uppercase mb-4">Payment Options:</h3>
                </div>
            </div>
            <div class="row justify-content-center">
                <div class="col-xl-10 payment-form">
                    <form class="form" action="" id="paymentForm">
                        <div class="row">
                            <div class="mb-4 col-md-auto">
                                <div class="form-check col-md-auto">
                                    <input class="form-check-input" id="fullAmount" type="radio" value="fullAmount"
                                        name="payment" />
                                    <input type="hidden" id="amountToBePaid" value="{{ $formattedBalanceDue }}">
                                    <input type="hidden" id="firstName" value="{{ $user->firstName }}">
                                    <input type="hidden" id="lastName" value="{{ $user->lastName }}">
                                    <label class="form-check-label p text-uppercase" for="fullAmount">I will be paying this
                                        amount in full</label>
                                </div>
                            </div>
                        </div>
                        <div class="row d-flex align-items-center">
                            <div class="mb-4 col-md-auto">
                                <div class="form-check col-md-auto">
                                    <input class="form-check-input" id="specificAmount" type="radio"
                                        value="specificAmount" name="payment" />
                                    <label class="form-check-label p text-uppercase" for="specificAmount">I will be paying a
                                        specific amount</label>
                                </div>
                            </div>
                            <div class="mb-4 col-md-auto">
                                <label class="text-uppercase visually-hidden form-label"
                                    for="specificMoney">specificMoney</label>
                                <input class="form-control" id="specificMoney" type="number" />
                            </div>
                        </div>
                        <div class="row d-flex align-items-center">
                            <div class="mb-4 col-md-auto">
                                <div class="form-check col-md-auto">
                                    <input class="form-check-input" id="differentInvoiceMail" type="radio"
                                        value="differentInvoiceMail" name="payment" />
                                    <label class="form-check-label p text-uppercase" for="differentInvoiceMail">Send invoice
                                        to different email address</label>
                                </div>
                            </div>
                            <div class="mb-4 col-md-auto">
                                <label class="text-uppercase visually-hidden form-label"
                                    for="differentMailInvoice">differentMailInvoice</label>
                                <input class="form-control" id="differentMailInvoice" type="email" />
                            </div>
                        </div>

                        <div class="row d-flex align-items-center">
                            <div class="mb-4 col-md-auto">
                                <div class="form-check col-md-auto">
                                    <input class="form-check-input" id="differentGuardiansRadio" type="radio"
                                        value="differentGuardiansRadio" name="payment" />
                                    <label class="form-check-label p text-uppercase" for="differentGuardiansRadio">
                                        Send invoice to different guardians
                                    </label>
                                </div>
                            </div>

                            <div class="mb-4 col-md-3">
                                <label class="form-label text-uppercase visually-hidden" for="differentGuardians">
                                    differentGuardians
                                </label>
                                <div class="select-arrow position-relative">
                                    <select class="form-control selectpicker" id="differentGuardians" multiple
                                        data-live-search="true">
                                        {{-- <option value="guardian1">Guardian 1</option>
                                        <option value="guardian2">Guardian 2</option>
                                        <option value="guardian3">Guardian 3</option> --}}
                                    </select>
                                </div>
                            </div>
                        </div>

                        <div class="row d-flex align-items-center">
                            <div class="mb-4 col-md-auto">
                                <div class="form-check col-md-auto">
                                    <input class="form-check-input" id="recurringPayments" type="radio"
                                        value="recurringPayments" name="payment" />
                                    <label class="form-check-label p text-uppercase" for="recurringPayments">Set up
                                        recurring payments</label>
                                </div>
                            </div>

                        </div>
                        <div class="row gx-md-5" id='recurringPaymentsInfo'>
                            <div class="col-md">
                                <div class="row mb-4 align-items-center">
                                    <label class="col-md-auto form-col-label text-uppercase" for="amount">Amount</label>
                                    <div class="col-md">
                                        <input class="col-md form-control w-100" id="recurringAmount" type="text" />
                                    </div>
                                </div>
                            </div>
                            <div class="col-md">
                                <div class="row mb-4 align-items-center">
                                    <label class="col-md-auto form-col-label text-uppercase" for="startDate">Start
                                        date</label>
                                    <div class="col-md">
                                        <input class="col-md form-control w-100" id="startDate" type="date" />
                                    </div>
                                </div>
                            </div>
                            <div class="col-md">
                                <div class="row mb-4 align-items-center">
                                    <label class="col-md-auto form-col-label text-uppercase" for="numberofpayments">Number
                                        of payments</label>
                                    <div class="col-md">
                                        <input class="col-md form-control w-100" id="numberOfPayments" type="number" />
                                    </div>
                                </div>
                            </div>
                        </div>
                </div>
            </div>
        </div>
    </section>
    <section class="sec partition-hr">
        <div class="container">
            <hr class="border-navy opacity-100 my-0" />
        </div>
    </section>
    <section class="sec form mb-5" id="cardDetails">
        <div class="container">
            <div class="row justify-content-center">
                <div class="col-xl-10 col-xxl-8">
                    <div class="row">
                        <div class="mb-4 col-md-6">
                            <label class="text-uppercase form-label" for="firstName">First Name</label>
                            <input class="form-control" id="firstName" type="text" required />
                        </div>
                        <div class="mb-4 col-md-6">
                            <label class="text-uppercase form-label" for="lastName">Last Name</label>
                            <input class="form-control" id="lastName" type="text" required />
                        </div>
                    </div>

                    <div class="row">
                        <div class="mb-4 col-md-6">
                            <label class="text-uppercase form-label" for="address">Address</label>
                            <input class="form-control" id="address" type="text" required />
                        </div>
                        <div class="mb-4 col-md-3">
                            <label class="form-label" for="state">State</label>
                            <select class="form-control" id="state" required>
                                <option value="">Select</option>
                                <option value="lorem">Lorem</option>
                                <option value="ipsum">Ipsum</option>
                                <option value="dolor">Dolor</option>
                            </select>
                        </div>
                        <div class="mb-4 col-md-3">
                            <label class="text-uppercase form-label">ZIP Code</label>
                            <div id="card-zip" class="form-control stripe-input"></div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="mb-4 col-md-2">
                            <label class="text-uppercase form-label" for="cardType">Card Type</label>
                            <input class="form-control" id="cardType" type="text" disabled />
                        </div>
                        <div class="mb-4 col-md-4">
                            <label class="text-uppercase form-label">Card Number</label>
                            <div id="card-number" class="form-control stripe-input"></div>
                        </div>
                        <div class="mb-4 col-md-3">
                            <label class="text-uppercase form-label">Exp. Date</label>
                            <div id="card-expiry" class="form-control stripe-input"></div>
                        </div>
                        <div class="mb-4 col-md-3">
                            <label class="text-uppercase form-label">CVC</label>
                            <div id="card-cvc" class="form-control stripe-input"></div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-12 d-flex justify-content-center mt-5">
                            <button class="cta py-0" id="paymentButton"> <span id="submitButtonText">Submit
                                </span>
                                <span id="loaderForPayment">
                                    <img id="loaderForPaymentImage" src="{{ asset('images/loader.svg') }}"
                                        alt="Loading..."
                                        style="display: none; width: 40px; height: 40px; margin-left: 10px;"></span></button>
                        </div>
                    </div>
                    </form>
                </div>
            </div>
        </div>
    </section>


    <script src="https://js.stripe.com/v3/"></script>
    <script src="https://cdn.jsdelivr.net/npm/card-validator@latest/dist/card-validator.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/choices.js/public/assets/scripts/choices.min.js"></script>

    <script>
        document.addEventListener('DOMContentLoaded', function() {


            const balanceDue = parseFloat(document.getElementById("amountToBePaid").value);
            const balanceDisplay = document.getElementById("balanceDisplay");
            const noPaymentMessage = document.getElementById("noPaymentMessage");
            const card = document.getElementById("cardDetails");
            const recurringPaymentsInput = document.getElementById('recurringPaymentsInfo');
            const formElements = document.querySelectorAll('input, select, button, textarea');

            if (balanceDue === 0) {
                balanceDisplay.classList.add("hidden");
                noPaymentMessage.classList.remove("hidden");
                noPaymentMessage.textContent = "You don't have any payments to make.";
                card.classList.add("hidden");
                recurringPaymentsInput.classList.add("hidden");

                formElements.forEach(element => {
                    element.disabled = true;
                });
            } else {
                balanceDisplay.classList.remove("hidden");
                noPaymentMessage.classList.add("hidden");
                card.classList.remove("hidden");
                recurringPaymentsInput.classList.remove("hidden");

                formElements.forEach(element => {
                    element.disabled = false;
                });
            }


            const stateSelect = document.getElementById('state');
            const xhr = new XMLHttpRequest();

            xhr.withCredentials = true;

            xhr.addEventListener('readystatechange', function() {
                if (this.readyState === this.DONE) {
                    if (this.status === 200) {
                        const states = JSON.parse(this.responseText);

                        stateSelect.innerHTML = '<option value="">Select</option>';

                        states.forEach(state => {
                            const option = document.createElement('option');
                            option.value = state
                                .name;
                            option.textContent = state
                                .name;
                            stateSelect.appendChild(option);
                        });
                    } else {
                        console.error('Error fetching states:', this.status, this.statusText);
                    }
                }
            });

            xhr.open('GET', 'https://us-states.p.rapidapi.com/basic');
            xhr.setRequestHeader('x-rapidapi-key', '**************************************************');
            xhr.setRequestHeader('x-rapidapi-host', 'us-states.p.rapidapi.com');
            xhr.send();


            const fullAmountRadio = document.getElementById('fullAmount');
            const specificAmountRadio = document.getElementById('specificAmount');
            const recurringPaymentRadio = document.getElementById('recurringPayments');
            const specificMoneyInput = document.getElementById('specificMoney');
            const invoiceToOtherMails = document.getElementById('differentInvoiceMail');
            const differentGuardians = document.getElementById("differentGuardiansRadio");

            const fullAmountToBePaid = document.getElementById('amountToBePaid').value;

            const recurringPaymentsInfo = document.getElementById("recurringPaymentsInfo");

            const amount = document.getElementById('recurringAmount');
            const numPayments = document.getElementById('numberOfPayments');
            numPayments.setAttribute('disabled', true);


            amount.addEventListener('input', () => {
                const recurringAmount = parseFloat(amount.value);

                if (recurringAmount > 0) {
                    const numberOfPayments = Math.ceil(fullAmountToBePaid / recurringAmount)
                    numPayments.value = numberOfPayments;
                } else {
                    numPayments.value = "";
                }
            });




            var stripe = Stripe('{{ env('STRIPE_KEY') }}');
            const elements = stripe.elements();

            const cardNumber = elements.create('cardNumber', {
                placeholder: 'Card Number',
                style: {
                    base: {
                        fontSize: '16px',
                        color: '#32325d'
                    }
                },
            });

            const cardExpiry = elements.create('cardExpiry');
            const cardCvc = elements.create('cardCvc');
            const cardZip = elements.create('postalCode');

            cardNumber.mount('#card-number');
            cardExpiry.mount('#card-expiry');
            cardCvc.mount('#card-cvc');
            cardZip.mount('#card-zip');

            const cardTypeInput = document.getElementById('cardType');
            cardNumber.on('change', function(event) {
                cardTypeInput.value = event.brand ? capitalize(event.brand) : '';
                if (event.error) console.error(event.error.message);
            });

            function capitalize(str) {
                return str.charAt(0).toUpperCase() + str.slice(1);
            }

            const form = document.getElementById('paymentForm');
            const csrfToken = document.querySelector('meta[name="csrf-token"]').getAttribute('content');


            form.addEventListener('submit', async function(event) {
                event.preventDefault();
                const selectedAmount = getSelectedAmount();

                if (!selectedAmount) {
                    alert('Please select a valid payment option.');
                    return;
                }

                showLoaderForPayment();

                try {

                    const firstName = document.getElementById('firstName').value;
                    const lastName = document.getElementById('lastName').value;
                    const cardHolderName = `${firstName} ${lastName}`.trim();

                    const address = document.getElementById('address').value;
                    const state = document.getElementById('state').value;
                    let totalAmount = document.getElementById('amountToBePaid').value;
                    if (selectedAmount.type === 'recurring') {
                        const paymentMethodResponse = await stripe.createPaymentMethod({
                            type: 'card',
                            card: cardNumber,
                            billing_details: {
                                name: cardHolderName
                                    .value,
                                address: {
                                    postal_code: cardZip.value,
                                },
                            },
                        });

                        if (paymentMethodResponse.error) {
                            console.error('Error creating payment method:', paymentMethodResponse
                                .error);
                            alert('Error creating payment method: ' + paymentMethodResponse.error
                                .message);
                            hideLoaderForPayment();
                            return;
                        }

                        const paymentMethodId = paymentMethodResponse.paymentMethod.id;

                        const response = await fetch(route('paymentOptions.setup-recurring-payment'), {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                                'X-CSRF-TOKEN': csrfToken,
                            },
                            body: JSON.stringify({
                                amount: selectedAmount.amount,
                                paymentDate: selectedAmount.startDate,
                                numberOfPayments: selectedAmount.numPayments,
                                paymentMethodId: paymentMethodId,
                                totalAmount: totalAmount,
                                cardHolder: cardHolderName,
                                address: address,
                                state: state,
                                payment_type: selectedAmount.type

                            }),
                        });

                        const result = await response.json();

                        if (result.success === false) {
                            console.error('Recurring payment setup error:', result.message);
                            showGlobalError('Error setting up recurring payments: ' + result.message);
                            return;
                        } else {

                            const updateResponse = await fetch(route(
                                'recurringPayments.statusForPaymentOptions'), {
                                method: 'POST',
                                headers: {
                                    'Content-Type': 'application/json',
                                    'X-CSRF-TOKEN': csrfToken,
                                },

                                body: JSON.stringify({
                                    subscriptionId: result
                                        .subscriptionId,
                                    status: 'active',
                                    amount: selectedAmount.amount,
                                    paymentDate: selectedAmount.startDate,
                                    numberOfPayments: selectedAmount.numPayments,
                                    paymentMethodId: paymentMethodId,
                                    totalAmount: totalAmount,
                                    cardHolder: cardHolderName,
                                    address: address,
                                    state: state,
                                    createdAt: new Date()
                                        .toISOString(),
                                }),
                            });

                            const StatusResult = await updateResponse.json();
                            if (!updateResponse.ok) {
                                showGlobalError(result.error || 'Failed to update payment status.')
                            } else {
                                window.location.href = route('payment.success');
                            }
                        }
                    } else {

                        selectedAmount.type = selectedAmount.type === "specific" ?
                            "split" :
                            selectedAmount.type === "one-time" ?
                            "full" :
                            selectedAmount.type;

                        // Handle one-time payment
                        const response = await fetch(route('payment.processForPaymentOptions'), {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                                'X-CSRF-TOKEN': csrfToken,
                            },
                            body: JSON.stringify({
                                amount: selectedAmount.amount,
                                payment_type: selectedAmount.type,
                            }),
                        });

                        const {
                            clientSecret,
                            paymentId,
                            error: processError
                        } = await response.json();

                        if (processError) {
                            console.error('Payment processing error:', processError);
                            alert('Payment processing error: ' + processError);
                            hideLoaderForPayment();
                            return;
                        }

                        // First update payment status on the backend
                        const updatePaymentSuccess = await updatePaymentStatus(clientSecret,
                            paymentId,
                            selectedAmount.amount);

                        if (!updatePaymentSuccess) {
                            console.error('Payment update failed.');
                            alert('Error updating payment status. Please try again.');
                            hideLoaderForPayment();
                            return;
                        }

                        // Proceed with Stripe card payment confirmation only after updating payment status
                        const {
                            error
                        } = await stripe.confirmCardPayment(clientSecret, {
                            payment_method: {
                                card: cardNumber,
                                billing_details: {
                                    address: {
                                        postal_code: cardZip.value,
                                    },
                                },
                            },
                        });

                        if (error) {
                            console.error('Payment failed:', error.message);
                            alert('Payment failed: ' + error.message);
                        } else {
                            // Handle successful payment confirmation
                            window.location.href = route('payment.success');
                        }
                    }

                } catch (err) {
                    console.error('Error during payment process:', err);
                    showGlobalError('An error occurred while processing the payment.', err);
                } finally {
                    hideLoaderForPayment();
                }
            });



            async function updatePaymentStatus(clientSecret, paymentId, selectedAmount) {
                const fullAmountToBePaid = document.getElementById('amountToBePaid').value;
                const data = {
                    paymentIntentId: clientSecret,
                    paymentId: paymentId,
                    amount: selectedAmount,
                    totalAmountToBePaid: fullAmountToBePaid,
                };

                try {
                    const response = await fetch(route('payment.updateForPaymentOptions'), {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'X-CSRF-TOKEN': csrfToken,
                        },
                        body: JSON.stringify(data),
                    });

                    const result = await response.json();
                    if (!response.ok) {
                        throw new Error(result.error || 'Failed to update payment status.');
                    }

                    console.log('Payment status updated successfully:', result);
                    return true;


                } catch (error) {
                    console.error('Error updating payment status:', error);
                    alert('Failed to update payment status: ' + error.message);
                    return false;
                }
            }


            function showLoaderForPayment() {
                document.getElementById("submitButtonText").style.display = "none";
                document.getElementById("loaderForPaymentImage").style.display =
                    "inline-block";
                const button = document.getElementById("paymentButton");
                button.classList.add("buttonLoader");
            }

            function hideLoaderForPayment() {
                document.getElementById("submitButtonText").style.display = "inline";
                document.getElementById("loaderForPaymentImage").style.display =
                    "none";
                const button = document.getElementById("paymentButton");
                button.classList.remove("buttonLoader");
            }

            function getSelectedAmount() {
                const fullAmountRadio = document.getElementById('fullAmount');
                const specificAmountRadio = document.getElementById('specificAmount');
                const recurringPaymentRadio = document.getElementById('recurringPayments');
                const specificMoneyInput = document.getElementById('specificMoney');

                const fullAmountToBePaid = document.getElementById('amountToBePaid').value;




                if (fullAmountRadio.checked) return {
                    type: 'one-time',
                    amount: fullAmountToBePaid
                };
                if (specificAmountRadio.checked) return {
                    type: 'specific',
                    amount: specificMoneyInput.value
                };
                if (recurringPaymentRadio.checked) {
                    const amount = document.getElementById('recurringAmount').value;
                    const startDate = document.getElementById('startDate').value;
                    const numPayments = document.getElementById('numberOfPayments').value;

                    return {
                        type: 'recurring',
                        amount,
                        startDate,
                        numPayments
                    };
                }

                return null;
            }


            const differentGuardiansRadio = document.getElementById('differentGuardiansRadio');
            const differentGuardiansSelect = document.getElementById('differentGuardians');
            const choices = new Choices(differentGuardiansSelect, {
                removeItemButton: true,
                placeholderValue: 'Select Guardians',
                searchEnabled: true,
            });

            differentGuardiansRadio.addEventListener('change', () => {
                if (differentGuardiansRadio.checked) fetchGuardians();
            });

            function fetchGuardians() {
                fetch(route('guardian.guardiansForPayment'))
                    .then((response) => {
                        if (!response.ok) throw new Error('Network response was not ok');
                        return response.json();
                    })
                    .then((data) => {
                        populateGuardians(data.guardians || []);
                    })
                    .catch((error) => console.error('Error fetching guardians:', error));
            }

            function populateGuardians(guardians) {
                choices.clearStore();
                guardians.forEach((guardian) => {
                    choices.setValue([{
                        value: guardian.id,
                        label: `${guardian.firstName} ${guardian.lastName}`,
                        selected: false,
                        disabled: false,
                    }]);
                });
            }
        });
    </script>
@endsection
