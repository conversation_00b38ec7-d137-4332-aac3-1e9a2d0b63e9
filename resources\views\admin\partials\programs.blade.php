 <div class="table-program table-responsive">
     <table class="table table-hover" width="100%">
         <tbody>
             <tr>
                 <th class="py-4">Name</th>
                 <th class="py-4">Sport</th>
                 <th class="py-4">Gender</th>
                 <th class="py-4">Age</th>
                 <th class="py-4">Dates</th>
                 <th class="py-4">Days</th>
                 <th class="py-4">Enrollment</th>
                 <th class="py-4">Cost</th>
                 <th class="py-4">Payment</th>
                 <th class="py-4" width="20"></th>
                 <th class="py-4" width="20"></th>
             </tr>
             @foreach ($programs as $program)
                 <tr>
                     <td class="py-4 text-capitalize" valign="middle">{{ $program->name }}</td>
                     <td class="py-4 text-capitalize" valign="middle">{{ $program->sport }}</td>

                     <td class="py-4" valign="middle">
                         @if (strtolower($program->gender) == 'coed')
                             Co-ed
                         @else
                             {{ ucfirst($program->gender) }}
                         @endif
                     </td>
                     <td class="py-4" valign="middle">
                         {{ $program->age_restriction_from }}-{{ $program->age_restriction_to }}</td>
                     <td class="py-4" valign="middle">{{ $program->start_date }}-
                         {{ $program->end_date }}</td>
                     <td class="py-4 text-capitalize" valign="middle">
                         {{ implode(', ', $program->frequency_days) }}<br>{{ $program->frequency }}
                     </td>
                     <td class="py-4" valign="middle">{{ $program->number_of_registers }}</td>
                     <td class="py-4" valign="middle">${{ number_format($program->cost, 2) }}</td>
                     <td class="py-4" valign="middle">{{ ucfirst($program->payment) }}</td>
                     <td class="py-4" valign="middle">
                         <span class="action edit">
                             <a href="{{ route('admin.program.edit', $program->id) }}">
                                 <img src="{{ asset('images/edit-icon.svg') }}" alt="" width="20"
                                     height="20" />
                             </a>
                         </span>
                     </td>
                     <td class="py-4" valign="middle">
                         <form id="removeProgram-{{ $program->slug }}"
                             action="{{ route('admin.program.destroy', $program->slug) }}" method="POST"
                             onsubmit="showConfirmation(event, 'removeProgram-{{ $program->slug }}')"
                             style="display: inline;">
                             @csrf
                             @method('DELETE')
                             <button type="submit"
                                 style="border: none; background: none; padding: 0; cursor: pointer;">
                                 <span class="action delete">
                                     <img src="{{ asset('images/delete-icon.svg') }}" alt="" width="18"
                                         height="20" />
                                 </span>
                             </button>
                         </form>
                     </td>
                 </tr>
             @endforeach
         </tbody>
     </table>
     <div class="mt-4 d-flex justify-content-center">
         {{ $programs->onEachSide(1)->links('pagination::bootstrap-5') }}
     </div>
 </div>
 </div>
