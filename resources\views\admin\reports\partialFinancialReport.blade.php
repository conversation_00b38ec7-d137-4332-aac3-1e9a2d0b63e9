<table class="table table-hover" width="100%" id="payments-table">
    <thead>
        <tr>
            <th class="py-4">Name</th>
            <th class="py-4">Player Name</th>
            <th class="py-4">Program Name</th>
            <th class="py-4">Town</th>
            <th class="py-4">Paid Amount</th>
            <th class="py-4">Date</th>
            <th class="py-4"></th>
            <th class="py-4" width="20"></th>
            <th class="py-4" width="20"></th>
        </tr>
    </thead>
    <tbody>

        @if ($allPayments && count($allPayments) > 0)
            @foreach ($allPayments as $payment)
                <tr>

                    <td class="py-4" valign="middle">
                        {{ optional($payment->guardian)->firstName }} {{ optional($payment->guardian)->lastName }}
                    </td>

                    <td class="py-4" valign="middle">
                        @foreach ($payment->players as $player)
                            {{ $player->firstName }} {{ $player->lastName }} <br>
                        @endforeach
                    </td>
                    <td class="py-4" valign="middle">
                        @foreach ($payment->programs as $program)
                            {{ $program->name }} <br>
                        @endforeach
                    </td>

                    <td class="py-4" valign="middle">{{ $payment->town ?? 'N/A' }}</td>
                    <td class="py-4" valign="middle"> ${{ $payment->paid_amount ?? 'N/A' }}
                    </td>
                    <td class="py-4" valign="middle">{{ $payment->created_at ?? 'N/A' }}
                    </td>


                    <td class="py-4" valign="middle">
                        <a href="" class="action edit" id="edit-admin" data-admin-id="{{ $payment->id }}">
                            <img src="{{ asset('images/edit-icon.svg') }}" alt="Edit" width="20"
                                height="20" />
                        </a>
                    </td>

                    <td class="py-4" valign="middle">
                        <form id="deleteUser-{{ $payment->id }}"
                            onsubmit="showConfirmation(event, 'deleteUser-{{ $payment->id }}')" action="#"
                            method="POST" class="inline-form mb-0">
                            @csrf
                            @method('DELETE')
                            <button type="submit" class="action edit bg-transparent border-0 p-0">
                                <img src="{{ asset('images/delete-icon.svg') }}" alt="Delete" width="18"
                                    height="20" />
                            </button>
                        </form>
                    </td>
                </tr>
            @endforeach
        @else
            <tr>
                <td colspan="8" class="py-4 text-center">No payment records found.</td>
            </tr>
        @endif
    </tbody>
</table>
<div class="mt-4 d-flex justify-content-center pagination container-pagination">
    {{ $allPayments->onEachSide(1)->links('pagination::bootstrap-5') }}
</div>
