<?php

namespace App\Livewire\Admin;

use App\Models\Program;
use App\Models\ProgramRegistration;
use App\Models\PlayerInvitation;
use App\Models\AdminInvitesPlayerForProgram;
use App\Models\Team;
use App\Models\User;
use App\Events\PlayerInvitedByAdmin;
use Livewire\Component;
use Livewire\WithPagination;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;

class TryoutPrograms extends Component
{
    use WithPagination;

    public $search = '';
    public $filter = '';
    public $perPage = 10;
    public $showPostTryouts = false;
    public $selectedProgramId = null;
    public $selectedTeamId = null;
    public $inviteModalOpen = false;
    public $selectedPlayerId = null;
    public $assignedBalance = 0.00;

    // Regular program invitation properties
    public $expandedPrograms = [];
    public $availablePlayers = [];
    public $loadingPlayers = false;

    protected $searchableFields = ['name', 'sport', 'grade', 'gender'];
    protected $filterableFields = ['sport' => 'sport', 'gender' => 'gender', 'grade' => 'grade'];
    protected $paginationTheme = 'bootstrap';
    protected $defaultOrderBy = 'created_at';

    protected $rules = [
        'assignedBalance' => 'nullable|numeric|min:0|max:9999.99',
    ];

    public function updatedSearch()
    {
        $this->resetPage();
    }

    public function updatedPerPage()
    {
        $this->resetPage();
    }

    public function render()
    {
        $programs = $this->buildQuery()->paginate($this->perPage);
        $postTryoutPrograms = $this->getPostTryoutPrograms();

        return view('livewire.admin.tryout-programs', compact('programs', 'postTryoutPrograms'));
    }

    protected function buildQuery()
    {
        return Program::query()
            ->where('type', 'Tryout')
            ->where('end_date', '>=', Carbon::today()) // Only show active/future tryout programs
            ->when($this->hasSearchTerm(), fn($query) => $this->applySearch($query))
            ->when($this->hasValidFilter(), fn($query) => $this->applyOrdering($query))
            ->unless($this->hasValidFilter(), fn($query) => $query->orderBy($this->defaultOrderBy));
    }

    protected function applySearch($query)
    {
        $searchTerm = $this->search;

        if ($this->hasValidFilter()) {
            return $this->applySpecificFieldSearch($query, $searchTerm);
        }

        return $this->applyGlobalSearch($query, $searchTerm);
    }

    protected function applySpecificFieldSearch($query, $searchTerm)
    {
        $field = $this->getFilterField();

        if ($field) {
            $query->where($field, 'like', "%{$searchTerm}%");
        } else {
            $this->applyDefaultSearch($query, $searchTerm);
        }

        return $query;
    }

    protected function applyGlobalSearch($query, $searchTerm)
    {
        return $query->where(function ($q) use ($searchTerm) {
            foreach ($this->searchableFields as $field) {
                $q->orWhere($field, 'like', "%{$searchTerm}%");
            }
        });
    }

    protected function applyDefaultSearch($query, $searchTerm)
    {
        return $query->where(function ($q) use ($searchTerm) {
            $q->where('name', 'like', "%{$searchTerm}")
                ->orWhere('grade', 'like', "%{$searchTerm}%")
                ->orWhere('sport', 'like', "%{$searchTerm}%")
                ->orWhere('gender', 'like', "%{$searchTerm}%");
        });
    }

    protected function applyOrdering($query)
    {
        $field = $this->getFilterField() ?: $this->defaultOrderBy;

        // Fixed: Changed 'orderyBy' to 'orderBy'
        return $query->orderBy($field);
    }

    protected function getFilterField()
    {
        return $this->filterableFields[$this->filter] ?? null;
    }

    protected function hasSearchTerm()
    {
        return !empty($this->search);
    }

    protected function hasValidFilter()
    {
        return !empty($this->filter) && array_key_exists($this->filter, $this->filterableFields);
    }

    public function getAvailableFilters()
    {
        return array_keys($this->filterableFields);
    }

    public function getSearchableFields()
    {
        return $this->searchableFields;
    }

    public function addSearchableField($field)
    {
        if (!in_array($field, $this->searchableFields)) {
            $this->searchableFields[] = $field;
        }

        return $this;
    }

    public function addFilter($label, $field)
    {
        $this->filterableFields[$label] = $field;
    }

    public function clearFilters()
    {
        $this->search = '';
        $this->filter = '';
        $this->resetPage();
    }

    /**
     * Toggle post-tryout programs view
     */
    public function togglePostTryouts()
    {
        $this->showPostTryouts = !$this->showPostTryouts;
    }

    /**
     * Get post-tryout programs (programs where end date has passed)
     */
    public function getPostTryoutPrograms()
    {
        return Program::where('type', 'Tryout')
            ->where('end_date', '<', Carbon::today())
            ->with(['registrations.player'])
            ->orderBy('end_date', 'desc')
            ->get();
    }

    /**
     * Get registered players for a specific program with their invitation status
     */
    public function getPlayersForProgram($programId)
    {
        return ProgramRegistration::where('program_id', $programId)
            ->with([
                'player',
                'player.teamInvitations' => function ($query) use ($programId) {
                    $query->where('program_id', $programId)->with('team');
                }
            ])
            ->get()
            ->map(function ($registration) {
                $player = $registration->player;
                $latestInvitation = $player->teamInvitations->first();

                // Determine invitation status - check both status fields
                $invitationStatus = 'not_invited';
                if ($latestInvitation) {
                    $invitationStatus = $latestInvitation->status ?? $latestInvitation->invitation_status ?? 'pending';
                }

                return [
                    'id' => $player->id,
                    'name' => $player->firstName . ' ' . $player->lastName,
                    'email' => $player->email,
                    'invitation_status' => $invitationStatus,
                    'invited_team' => $latestInvitation && $latestInvitation->team ? $latestInvitation->team->name : null,
                    'invitation_id' => $latestInvitation ? $latestInvitation->id : null,
                ];
            });
    }

    /**
     * Show invite modal for a player
     */
    public function showInviteModal($playerId, $programId)
    {
        $this->selectedPlayerId = $playerId;
        $this->selectedProgramId = $programId;
        $this->inviteModalOpen = true;
    }

    /**
     * Close invite modal
     */
    public function closeInviteModal()
    {
        $this->inviteModalOpen = false;
        $this->selectedPlayerId = null;
        $this->selectedProgramId = null;
        $this->selectedTeamId = null;
        $this->assignedBalance = 0.00;
    }

    /**
     * Invite player to team
     */
    public function invitePlayerToTeam()
    {
        // Validate the assigned balance
        $this->validate([
            'assignedBalance' => 'nullable|numeric|min:0|max:9999.99',
        ]);

        if (!$this->selectedPlayerId || !$this->selectedTeamId || !$this->selectedProgramId) {
            session()->flash('error', 'Please select a team and player.');
            return;
        }

        try {
            // Check if player already has an invitation for this program
            $existingInvitation = PlayerInvitation::where('user_id', $this->selectedPlayerId)
                ->where('program_id', $this->selectedProgramId)
                ->first();

            if ($existingInvitation) {
                session()->flash('error', 'Player already has an invitation for this program.');
                return;
            }

            $team = Team::findOrFail($this->selectedTeamId);

            // Get the preferred coach (primary if available, otherwise any coach)
            $preferredCoach = $team->preferredCoach();

            if (!$preferredCoach) {
                session()->flash('error', 'No coach found for this team. Please assign a coach to the team first.');
                return;
            }

            // Create new invitation
            PlayerInvitation::create([
                'user_id' => $this->selectedPlayerId,
                'team_id' => $this->selectedTeamId,
                'program_id' => $this->selectedProgramId,
                'coach_id' => $preferredCoach->id,
                'invited_by' => auth()->id(),
                'status' => 'pending',
                'invitation_status' => 'pending',
                'invited_at' => now(),
                'balance_due' => $this->assignedBalance ?? 0.00,
                'balance_assigned' => $this->assignedBalance ?? 0.00,
            ]);

            session()->flash('success', 'Player invited to team successfully.');
            $this->closeInviteModal();
        } catch (\Exception $e) {
            session()->flash('error', 'Failed to invite player: ' . $e->getMessage());
        }
    }

    /**
     * Move accepted player to team
     */
    public function movePlayerToTeam($playerId, $invitationId)
    {
        try {
            DB::beginTransaction();

            $invitation = PlayerInvitation::findOrFail($invitationId);

            // Check both status fields for accepted status
            $isAccepted = ($invitation->status === 'accepted') || ($invitation->invitation_status === 'accepted');

            if (!$isAccepted) {
                session()->flash('error', 'Player has not accepted the invitation yet.');
                return;
            }

            // Add player to team
            $team = Team::findOrFail($invitation->team_id);
            $player = User::findOrFail($playerId);

            // Check if player is already in the team
            if ($team->players()->where('player_id', $playerId)->exists()) {
                session()->flash('error', 'Player is already in this team.');
                return;
            }

            // Add player to team
            $team->players()->attach($playerId);

            // Update invitation status to completed
            $invitation->update([
                'status' => 'completed',
                'invitation_status' => 'completed'
            ]);

            DB::commit();

            session()->flash('success', 'Player successfully moved to team.');
        } catch (\Exception $e) {
            DB::rollback();
            session()->flash('error', 'Failed to move player to team: ' . $e->getMessage());
        }
    }

    /**
     * Get available teams for invitations
     */
    public function getAvailableTeams()
    {
        return Team::orderBy('name')->get();
    }

    /**
     * Toggle expanded state for a regular program
     */
    public function toggleProgramExpansion($programId)
    {
        if (in_array($programId, $this->expandedPrograms)) {
            $this->expandedPrograms = array_diff($this->expandedPrograms, [$programId]);
        } else {
            $this->expandedPrograms[] = $programId;
        }
    }

    /**
     * Get registered players for a regular program with their invitation status
     */
    public function getRegisteredPlayersForRegularProgram($programId)
    {
        return ProgramRegistration::where('program_id', $programId)
            ->with(['player'])
            ->get()
            ->map(function ($registration) use ($programId) {
                $player = $registration->player;

                // Check if player has been invited to this program
                $invitation = AdminInvitesPlayerForProgram::where('user_id', $player->id)
                    ->where('program_id', $programId)
                    ->first();

                return [
                    'id' => $player->id,
                    'name' => $player->firstName . ' ' . $player->lastName,
                    'email' => $player->email,
                    'invitation_status' => $invitation ? $invitation->status : 'not_invited',
                    'invitation_id' => $invitation ? $invitation->id : null,
                ];
            });
    }

    /**
     * Load players for regular program invitation modal
     */
    public function loadPlayersForInvitation($programId)
    {
        try {
            Log::info('Starting loadPlayersForInvitation for program: ' . $programId);

            $this->selectedProgramId = $programId;
            $this->selectedPlayerId = null;
            $this->loadingPlayers = true;

            // Very simple test - try to get users with player role, fallback to all users
            $players = User::whereHas('roles', function ($query) {
                $query->where('name', 'player');
            })
                ->select('id', 'firstName', 'lastName', 'email')
                ->limit(5) // Start with just 5 players for testing
                ->get();

            // If no players found with role, try getting any users for testing
            if ($players->count() === 0) {
                Log::info('No players found with role, trying all users');
                $players = User::select('id', 'firstName', 'lastName', 'email')
                    ->limit(5)
                    ->get();
            }

            Log::info('Raw players query returned: ' . $players->count() . ' players');

            $this->availablePlayers = [];
            foreach ($players as $player) {
                $this->availablePlayers[] = [
                    'id' => $player->id,
                    'name' => $player->firstName . ' ' . $player->lastName,
                    'email' => $player->email,
                ];
            }

            Log::info('Processed ' . count($this->availablePlayers) . ' players into array');
        } catch (\Exception $e) {
            Log::error('Exception in loadPlayersForInvitation: ' . $e->getMessage());
            Log::error('Stack trace: ' . $e->getTraceAsString());
            $this->availablePlayers = [];
            // Don't flash error to session as it might interfere with modal
        } finally {
            $this->loadingPlayers = false;
            Log::info('loadPlayersForInvitation completed, loadingPlayers = false');
        }
    }

    /**
     * Reset regular program invitation modal data
     */
    public function resetRegularInviteModal()
    {
        $this->selectedProgramId = null;
        $this->selectedPlayerId = null;
        $this->availablePlayers = [];
        $this->loadingPlayers = false;
    }

    /**
     * Simple test method to debug
     */
    public function testMethod($programId)
    {
        Log::info('Test method called with program: ' . $programId);
        $this->availablePlayers = [
            ['id' => 1, 'name' => 'Test Player 1', 'email' => '<EMAIL>'],
            ['id' => 2, 'name' => 'Test Player 2', 'email' => '<EMAIL>'],
        ];
        $this->loadingPlayers = false;
        Log::info('Test method completed');
    }

    /**
     * Load available players for invitation to regular program
     */
    public function loadAvailablePlayers($programId)
    {
        try {
            // Get all players using role relationship
            $allPlayers = User::whereHas('roles', function ($query) {
                $query->where('name', 'player');
            })
                ->select('id', 'firstName', 'lastName', 'email')
                ->get();

            // Get already registered players
            $registeredPlayerIds = ProgramRegistration::where('program_id', $programId)
                ->pluck('player_id')
                ->toArray();

            // Get already invited players
            $invitedPlayerIds = AdminInvitesPlayerForProgram::where('program_id', $programId)
                ->pluck('user_id')
                ->toArray();

            // Filter out registered and invited players
            $this->availablePlayers = $allPlayers->filter(function ($player) use ($registeredPlayerIds, $invitedPlayerIds) {
                return !in_array($player->id, $registeredPlayerIds) && !in_array($player->id, $invitedPlayerIds);
            })->map(function ($player) {
                return [
                    'id' => $player->id,
                    'name' => $player->firstName . ' ' . $player->lastName,
                    'email' => $player->email,
                ];
            })->values()->toArray();
        } catch (\Exception $e) {
            \Log::error('Error loading available players: ' . $e->getMessage());
            $this->availablePlayers = [];
            throw $e;
        }
    }

    /**
     * Invite player to regular program
     */
    public function invitePlayerToRegularProgram()
    {
        if (!$this->selectedPlayerId || !$this->selectedProgramId) {
            session()->flash('error', 'Please select a player and program.');
            return;
        }

        try {
            // Check if player already has an invitation for this program
            $existingInvitation = AdminInvitesPlayerForProgram::where('user_id', $this->selectedPlayerId)
                ->where('program_id', $this->selectedProgramId)
                ->first();

            if ($existingInvitation) {
                session()->flash('error', 'Player already has an invitation for this program.');
                return;
            }

            $program = Program::findOrFail($this->selectedProgramId);
            $player = User::findOrFail($this->selectedPlayerId);

            // Create new invitation
            AdminInvitesPlayerForProgram::create([
                'user_id' => $this->selectedPlayerId,
                'program_id' => $this->selectedProgramId,
                'invited_by' => auth()->id(),
                'invited_at' => now(),
                'status' => 'pending',
            ]);

            // Send invitation email
            $guardian = User::where('id', $player->primary_parent_id)->first();
            if ($guardian) {
                event(new PlayerInvitedByAdmin($guardian, $player, $program));
            }

            session()->flash('success', 'Player invited to program successfully.');
            $this->resetRegularInviteModal();

            // Dispatch browser event to close modal
            $this->dispatch('close-regular-invite-modal');
        } catch (\Exception $e) {
            session()->flash('error', 'Failed to invite player: ' . $e->getMessage());
        }
    }
}
