<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Program List</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
</head>

<!-- fdfad -->

<body class="bg-gray-100 min-h-screen p-8">

    <form id="filterForm" method="GET" action="/programs">
        <div class="absolute top-4 left-4">
            <select id="gender" name="gender"
                class="ml-2 px-4 py-2 border border-gray-300 rounded-lg shadow-sm focus:ring-blue-500 focus:border-blue-500"
                onchange="submitForm()">
                <option value="">Gender</option>
                <option value="boys" {{ request('gender') == 'boys' ? 'selected' : '' }}>
                    Boys</option>
                <option value="girls" {{ request('gender') == 'girls' ? 'selected' : '' }}>
                    Girls</option>
            </select>
        </div>
    </form>

    <div class="container mx-auto bg-white p-6 rounded-lg shadow-lg mt-12">
        <h1 class="text-3xl font-bold mb-6 text-center text-blue-900">All Programs</h1>

        @if (session('success'))
            <div class="bg-green-500 text-white p-4 rounded mb-4">
                {{ session('success') }}
            </div>
        @endif

        <table class="min-w-full bg-white border border-gray-200 rounded-lg overflow-hidden">
            <thead>
                <tr class="bg-gray-200 text-gray-600 text-sm uppercase font-semibold">
                    <th class="px-6 py-3 text-left">Name</th>
                    <th class="px-6 py-3 text-left">Sub Program</th>
                    <th class="px-6 py-3 text-left">Start Date</th>
                    <th class="px-6 py-3 text-left">Day of the Week</th>
                    <th class="px-6 py-3 text-left">Individual Price</th>
                    <th class="px-6 py-3 text-left">Location</th>
                </tr>
            </thead>
            <tbody class="text-gray-700 text-sm">
                @foreach ($programs as $program)
                    <tr class="border-b border-gray-200 hover:bg-gray-100">
                        <td class="px-6 py-4">{{ $program->name }}</td>
                        <td class="px-6 py-4">{{ $program->sub_program }}</td>
                        <td class="px-6 py-4">{{ $program->start_date }}</td>
                        <td class="px-6 py-4">
                            {{ implode(', ', $program->frequency_days) }} <br>
                            {{ $program->frequency }}</td>
                        <td class="px-6 py-4">${{ number_format($program->cost, 2) }}</td>
                        <td class="px-6 py-4">{{ $program->location }}</td>
                        <td class="px-6 py-4">
                            <a href="{{ route('program.show', ['program' => $program->slug]) }}"
                                class="text-blue-600 hover:text-blue-800">
                                Sign up
                            </a>
                        </td>
                    </tr>
                @endforeach
            </tbody>
        </table>

        <div class="mt-6">
            {{ $programs->links('pagination::tailwind') }}
        </div>
    </div>
    <script>
        function submitForm() {
            document.getElementById('filterForm').submit();
        }
    </script>
</body>

</html>
