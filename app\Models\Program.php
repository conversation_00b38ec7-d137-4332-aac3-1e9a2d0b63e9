<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Str;

class Program extends Model
{
    use HasFactory;

  protected $table = 'programs';
   protected $casts = [
        'frequency_days' => 'array',
    ];
    protected $fillable = [
        'name',
        'slug',
        'sub_program',
        'location',
        'sport',
        'gender',
        'age_restriction_from',
        'age_restriction_to',
        'grade',
        'birth_date_cutoff',
        'registration_opening_date',
        'registration_closing_date',
        'start_date',
        'end_date',
        'start_time',
        'end_time',
        'frequency',
        'frequency_days',
        'number_of_registers',
        'enable_waitlist',
        'cost',
        'enable_early_bird_specials',
        'payment',
        'program_description',
        'season',
        'type',
        'status',
        'early_bird_specials_date',
        'minimum_recurring_amount'
    ];


        public static function boot()
    {
        parent::boot();

        static::creating(function ($program) {

            $slug = Str::slug($program->name);


            $count = Program::where('slug', $slug)->count();
            if ($count > 0) {
                $slug = $slug . '-' . ($count + 1);
            }

            $program->slug = $slug;
        });
    }

    public function earlyBirdPricing()
    {
        return $this->hasMany(EarlyBirdPricing::class, 'program_id');
    }


    public function players()
{
    return $this->hasMany(PlayerProgram::class, 'program_id');
}


public function scopeSport(Builder $query, string $sport): Builder
{
    return $query->where('sport', '=', $sport);
}


public function scopeAgeGroup(Builder $query, string $ageGroup): Builder
{
    $ageRanges = [
        'adult' => [19, 100],
        'u18' => [17, 18],
        'u16' => [15, 16],
        'u14' => [13, 14],
        'u12' => [11, 12],
        'u10' => [9, 10],
        'u8' => [7, 8],
    ];


    $startAge = $ageRanges[$ageGroup][0] ?? null;
    $endAge = $ageRanges[$ageGroup][1] ?? null;


    if ($startAge !== null && $endAge !== null) {
        return $query->where('age_restriction_from', '>=', $startAge)
                     ->where('age_restriction_to', '<=', $endAge);
    }


    if ($startAge !== null && $endAge === null) {
        return $query->where('age_restriction_from', '>=', $startAge);
    }

    return $query;
}


private function getAgeRestrictions($ageGroup)
{
    switch ($ageGroup) {
        case 'adult':
            return [18, null];
        case 'highSchool':
            return [14, 18];
        case 'u14':
            return [12, 14];
        case 'u12':
            return [10, 12];
        case 'u10':
            return [8, 10];
        case 'u8':
            return [6, 8];
        default:
            return null;
    }
}

public function teamPrograms()
{
    return $this->hasMany(TeamProgram::class);
}

 public function teams()
    {
        return $this->belongsToMany(Team::class, 'team_program', 'program_id', 'team_id');
    }


 public function registrations()
    {
        return $this->hasMany(ProgramRegistration::class, 'program_id');
    }



}
