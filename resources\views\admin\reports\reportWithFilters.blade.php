@extends('layouts.app')
@section('title', 'Admin')
@section('content')
    <section class="page_title text-center pt-5">
        <h1 class="text-uppercase lh-1 mb-0">Report</h1>
    </section>
    <section class="sec admin-welcome">
        <div class="container">
            <div class="heading align-items-center d-flex flex-column text-center">
                <span class="hero-bar d-inline-flex mb-5"></span>
            </div>
            <div class="container">
                @if (isset($playerDataInRange) && $playerDataInRange->isNotEmpty() && isset($player))
                    <div class="tables_heading table-program table-responsive">
                        <table class="table custom-table user-management-table">
                            <thead>
                                <tr>
                                    <th style="width:40px" class="py-4">&nbsp;</th>
                                    <th style="width:200px" class="py-4">First Name</th>
                                    <th style="width:200px" class="py-4">Last Name</th>
                                    <th style="width:200px" class="py-4">Gender</th>
                                    <th style="width:200px" class="py-4">Age</th>
                                    <th style="width:200px" class="py-4">Email</th>
                                    <th style="width:200px">&nbsp;</th>
                                </tr>
                            </thead>
                        </table>
                    </div>
                    <div class="tables_collapse_heading table-heading-grey">
                        <table class="table table-hover">
                            <tr>
                                <td style="width:40px">
                                    <div class="icon"><i class="bi bi-caret-up-fill toggle-arrow"
                                            style="color:#0B4499; cursor: pointer;" data-toggle="{{ $player->id ?? 'unknown' }}"></i>
                                    </div>
                                </td>
                                <td style="width:200px" class="text-custom">
                                    {{ $player->firstName ?? 'N/A' }}
                                </td>
                                <td style="width:200px" class="text-custom">
                                    {{ $player->lastName ?? 'N/A' }}
                                </td>
                                <td style="width:200px" class="text-custom">
                                    {{ $player->gender ?? 'N/A' }}
                                </td>
                                <td style="width:200px" class="text-custom">
                                    {{ $player->age ?? 'N/A' }}
                                </td>
                                <td style="width:200px" class="text-custom">
                                    {{ $player->email ?? 'N/A' }}
                                </td>
                                <td style="width:200px">
                                    <table class="user-management-actions-table">
                                        <tr>
                                            <td class="text-center pe-3">
                                                <i class="bi bi-filetype-xls export-xls-icon"
                                                    style="color: #0B4499; font-size: 1.5rem;"></i>
                                            </td>
                                            <td class="text-center pe-3">
                                                <i class="bi bi-printer-fill print-icon"
                                                    style="color: #0B4499; font-size: 1.5rem;"></i>
                                            </td>
                                            <td class="text-center pe-3">
                                                <a href="" class="action edit" id="edit-admin">
                                                    <img src="{{ asset('images/edit-icon.svg') }}" alt="Edit"
                                                        width="20" height="20" />
                                                </a>
                                            </td>
                                            <td class="text-center">
                                                <form id="" action="#" method="POST" style="display: inline;">
                                                    @csrf
                                                    @method('DELETE')
                                                    <button type="submit"
                                                        style="border: none; background: none; padding: 0; cursor: pointer;">
                                                        <span class="action delete">
                                                            <img src="{{ asset('images/delete-icon.svg') }}" alt="Delete"
                                                                width="20" height="20" />
                                                        </span>
                                                    </button>
                                                </form>
                                            </td>
                                        </tr>
                                    </table>
                                </td>
                            </tr>
                        </table>
                    </div>

                    <div class="table_collapse_table table-program table-responsive table-grey">
                        <table class="table table-hover user-management-table team-table d-none" width="100%"
                            id="program-table{{ $player->id ?? 'unknown' }}">
                            <thead>
                                <tr>
                                    <th style="width:40px">&nbsp;</th>
                                    <th style="width:200px">Program</th>
                                    <th style="width:200px">Start Date</th>
                                    <th style="width:200px">End Date</th>
                                    <th style="width:200px"></th>
                                    <th style="width:200px"></th>
                                    <th style="width:200px"></th>
                                    <th style="width:200px">&nbsp;</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach ($playerDataInRange as $program)
                                    <tr>
                                        <td>&nbsp;</td>
                                        <td>{{ $program->name }}</td>
                                        <td>{{ $program->start_date }}</td>
                                        <td>{{ $program->end_date }}</td>
                                        <td></td>
                                        <td></td>
                                        <td></td>
                                        <td>
                                            <table>
                                                <tr>
                                                    <td class="pe-5">
                                                        <a href="#" class="action edit">
                                                            <img src="{{ asset('images/edit-icon.svg') }}" alt="Edit"
                                                                width="20" height="20" />
                                                        </a>
                                                    </td>
                                                    <td>
                                                        <form id="removeTeamFromProgram" action="" method="POST"
                                                            style="display: inline;">
                                                            @csrf
                                                            @method('DELETE')
                                                            <button type="submit" class="btn btn-link p-0 border-0"
                                                                style="background: none;">
                                                                <img src="{{ asset('images/delete-icon.svg') }}"
                                                                    alt="Delete" width="20" height="20" />
                                                            </button>
                                                        </form>
                                                    </td>
                                                </tr>
                                            </table>
                                        </td>
                                    </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                @elseif (isset($playerDataInRange) && $playerDataInRange->isEmpty())
                    <div class="no-data-found"
                        style="text-align: center; font-size: 18px; color: #FF0000; margin-top: 20px;">
                        No data found
                    </div>
                @endif


                @if (isset($programsInRange) && $programsInRange->isNotEmpty())
                    <div class="tables_heading table-program table-responsive">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th style="width:40px" class="py-4">&nbsp;</th>
                                    <th style="width:200px" class="py-4">Program Name</th>
                                    <th style="width:200px" class="py-4">Sport</th>
                                    <th style="width:200px" class="py-4">Start Date</th>
                                    <th style="width:200px" class="py-4">End Date</th>
                                    <th style="width:200px" class="py-4">Frequency</th>
                                    <th style="width:200px" class="py-4">Payment</th>
                                    <th style="width:200px" class="py-4">Players Enrolled</th>
                                    <th style="width:200px">&nbsp;</th>
                                </tr>
                            </thead>
                        </table>
                    </div>

                    @foreach ($programsInRange as $program)
                        <div class="tables_collapse_heading table-heading-grey">
                            <table class="table table-hover">
                                <tr>
                                    <td width="40">
                                        <div class="icon">
                                            <i class="bi bi-caret-up-fill toggle-arrow"
                                                style="color:#0B4499; cursor: pointer;"
                                                data-toggle="{{ $program->id }}"></i>
                                        </div>
                                    </td>
                                    <td style="width:200px" class="text-custom">
                                        {{ $program->name }}
                                    </td>
                                    <td style="width:200px" class="text-custom">
                                        {{ $program->sport }}
                                    </td>
                                    <td style="width:200px" class="text-custom">
                                        {{ $program->start_date }}
                                    </td>
                                    <td style="width:200px" class="text-custom">
                                        {{ $program->end_date }}
                                    </td>
                                    <td style="width:200px" class="text-custom">
                                        {{ $program->frequency }}
                                    </td>
                                    <td style="width:200px" class="text-custom">
                                        {{ $program->payment }}
                                    </td>
                                    <td style="width:200px" class="text-custom">
                                        {{ $program->registration_count }}
                                    </td>
                                    <td style="width:200px">
                                        <table>
                                            <tr>
                                                <td class="text-center pe-3">
                                                    <i class="bi bi-filetype-xls export-xls-icon"
                                                        style="color: #0B4499; font-size: 1.5rem;"></i>
                                                </td>
                                                <td class="text-center pe-3">
                                                    <i class="bi bi-printer-fill print-icon"
                                                        style="color: #0B4499; font-size: 1.5rem;"></i>
                                                </td>
                                                <td class="text-center pe-3">
                                                    <a href="" class="action edit" id="edit-admin">
                                                        <img src="{{ asset('images/edit-icon.svg') }}" alt="Edit"
                                                            width="20" height="20" />
                                                    </a>
                                                </td>
                                                <td class="text-center">
                                                    <form id="" action="#" method="POST"
                                                        style="display: inline;">
                                                        @csrf
                                                        @method('DELETE')
                                                        <button type="submit"
                                                            style="border: none; background: none; padding: 0; cursor: pointer;">
                                                            <span class="action delete">
                                                                <img src="{{ asset('images/delete-icon.svg') }}"
                                                                    alt="Delete" width="20" height="20" />
                                                            </span>
                                                        </button>
                                                    </form>
                                                </td>
                                            </tr>
                                        </table>
                                    </td>
                                </tr>
                            </table>
                        </div>

                        <div class="table_collapse_table table-program table-responsive table-grey">
                            <table class="table table-hover team-table d-none" width="100%"
                                id="program-table{{ $program->id }}">
                                <thead>
                                    <tr>
                                        <th width="40">&nbsp;</th>
                                        <th style="width:200px">Player First Name</th>
                                        <th style="width:200px">Player Last Name</th>
                                        <th style="width:200px">Age</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @if ($program->registrations->isNotEmpty())
                                        @foreach ($program->registrations as $registration)
                                            @if($registration->player)
                                            <tr>
                                                <td>&nbsp;</td>
                                                <td>{{ $registration->player->firstName ?? 'N/A' }}</td>
                                                <td>{{ $registration->player->lastName ?? 'N/A' }}</td>
                                                <td>{{ $registration->player->age ?? 'N/A' }}</td>
                                            </tr>
                                            @endif
                                        @endforeach
                                    @else
                                        <tr>
                                            <td colspan="4" style="text-align: center; color: red;">
                                                No players enrolled for this program.
                                            </td>
                                        </tr>
                                    @endif
                                </tbody>
                            </table>
                        </div>
                    @endforeach
                @elseif(isset($programsInRange) && $programsInRange->isEmpty())
                    <div class="no-data-found"
                        style="text-align: center; font-size: 18px; color: #FF0000; margin-top: 20px;">
                        No data found
                    </div>
                @endif


                @if (isset($playersByTownWithAge) && $playersByTownWithAge->isNotEmpty())
                    <div class="tables_heading table-program table-responsive">
                        <table class="table custom-table user-management-table">
                            <thead>
                                <tr>
                                    <th width="40" class="py-4">&nbsp;</th>
                                    <th style="width:200px" class="py-4">First Name</th>
                                    <th style="width:200px" class="py-4">Last Name</th>
                                    <th style="width:200px" class="py-4">Age</th>
                                    <th style="width:200px" class="py-4">Gender</th>
                                    <th style="width:200px" class="py-4">Email</th>
                                    <th style="width:200px" class="py-4">Town</th>
                                    <th style="width:200px">&nbsp;</th>
                                </tr>
                            </thead>
                        </table>
                    </div>

                    @foreach ($playersByTownWithAge as $player)
                        <div class="tables_collapse_heading table-heading-grey">
                            <table class="table table-hover user-management-table">
                                <tr>
                                    <td width="40">
                                        <div class="icon">
                                            <i class="bi bi-caret-up-fill toggle-arrow"
                                                style="color:#0B4499; cursor: pointer;"
                                                data-toggle="{{ $player->id }}"></i>
                                        </div>
                                    </td>
                                    <td style="width:200px" class="text-custom">
                                        {{ $player->firstName }}
                                    </td>
                                    <td style="width:200px" class="text-custom">
                                        {{ $player->lastName }}
                                    </td>
                                    <td style="width:200px" class="text-custom">
                                        {{ $player->age }}
                                    </td>
                                    <td style="width:200px" class="text-custom">
                                        {{ $player->gender }}
                                    </td>
                                    <td style="width:200px" class="text-custom">
                                        {{ $player->email ?? 'N/A' }}
                                    </td>
                                    <td style="width:200px" class="text-custom">
                                        {{ $player->town }}
                                    </td>
                                    <td style="width:200px">
                                        <table>
                                            <tr>
                                                <td class="text-center pe-3">
                                                    <i class="bi bi-filetype-xls export-xls-icon"
                                                        style="color: #0B4499; font-size: 1.5rem;"></i>
                                                </td>
                                                <td class="text-center pe-3">
                                                    <i class="bi bi-printer-fill print-icon"
                                                        style="color: #0B4499; font-size: 1.5rem;"></i>
                                                </td>
                                                <td class="text-center pe-3">
                                                    <a href="" class="action edit" id="edit-admin">
                                                        <img src="{{ asset('images/edit-icon.svg') }}" alt="Edit"
                                                            width="20" height="20" />
                                                    </a>
                                                </td>
                                                <td class="text-center">
                                                    <form id="" action="#" method="POST"
                                                        style="display: inline;">
                                                        @csrf
                                                        @method('DELETE')
                                                        <button type="submit"
                                                            style="border: none; background: none; padding: 0; cursor: pointer;">
                                                            <span class="action delete">
                                                                <img src="{{ asset('images/delete-icon.svg') }}"
                                                                    alt="Delete" width="20" height="20" />
                                                            </span>
                                                        </button>
                                                    </form>
                                                </td>
                                            </tr>
                                        </table>
                                    </td>
                                </tr>
                            </table>
                        </div>

                        <div class="table_collapse_table table-program table-responsive table-grey">
                            <table class="table table-hover team-table d-none" width="100%"
                                id="program-table{{ $player->id }}">
                                <thead>
                                    <tr>
                                        <th width="40">&nbsp;</th>
                                        <th style="width:200px">Name</th>
                                        <th style="width:200px">Sport</th>
                                        <th style="width:200px">Start Date</th>
                                        <th style="width:200px">End Date</th>
                                        <th style="width:200px">Frequency</th>
                                        <th style="width:200px">Payment</th>
                                    </tr>
                                </thead>

                                <tbody>
                                    @if ($player->programs->isNotEmpty())
                                        @foreach ($player->programs as $program)
                                            <tr>
                                                <td>&nbsp;</td>
                                                <td>{{ $program->name }}</td>
                                                <td>{{ $program->sport }}</td>
                                                <td>{{ $program->start_date }}</td>
                                                <td>{{ $program->end_date }}</td>
                                                <td>{{ $program->frequency }}</td>
                                                <td>{{ $program->payment }}</td>
                                            </tr>
                                        @endforeach
                                    @else
                                        <tr>
                                            <td colspan="7" style="text-align: center; color: red;">
                                                No programs enrolled for this player.
                                            </td>
                                        </tr>
                                    @endif
                                </tbody>
                            </table>
                        </div>
                    @endforeach
                @elseif(isset($programsInRange) && $programsInRange->isEmpty())
                    <div class="no-data-found"
                        style="text-align: center; font-size: 18px; color: #FF0000; margin-top: 20px;">
                        No data found
                    </div>
                @endif



                @if (isset($filteredData) && $filteredData->isNotEmpty())
                    <div class="tables_heading table-program table-responsive">
                        <table class="table custom-table user-management-table">
                            <thead>
                                <tr>
                                    <th width="40" class="py-4">&nbsp;</th>
                                    <th style="width:200px" class="py-4">First Name</th>
                                    <th style="width:200px" class="py-4">Last Name</th>
                                    <th style="width:200px" class="py-4">Age</th>
                                    <th style="width:200px" class="py-4">Gender</th>
                                    <th style="width:200px" class="py-4">Email</th>
                                    <th style="width:200px" class="py-4">Town</th>
                                    <th style="width:200px">&nbsp;</th>
                                </tr>
                            </thead>
                        </table>
                    </div>

                    @foreach ($filteredData as $registration)
                        @if($registration->player)
                        <div class="tables_collapse_heading table-heading-grey">
                            <table class="table table-hover user-management-table">
                                <tr>
                                    <td width="40">
                                        <div class="icon">
                                            <i class="bi bi-caret-up-fill toggle-arrow"
                                                style="color:#0B4499; cursor: pointer;"
                                                data-toggle="{{ $registration->player->id }}"></i>
                                        </div>
                                    </td>
                                    <td style="width:200px" class="text-custom">
                                        {{ $registration->player->firstName ?? 'N/A' }}
                                    </td>
                                    <td style="width:200px" class="text-custom">
                                        {{ $registration->player->lastName ?? 'N/A' }}
                                    </td>
                                    <td style="width:200px" class="text-custom">
                                        {{ $registration->player->age ?? 'N/A' }}
                                    </td>
                                    <td style="width:200px" class="text-custom">
                                        {{ $registration->player->gender ?? 'N/A' }}
                                    </td>
                                    <td style="width:200px" class="text-custom">
                                        {{ $registration->player->email ?? 'N/A' }}
                                    </td>
                                    <td style="width:200px" class="text-custom">
                                        {{ $registration->player->town ?? 'N/A' }}
                                    </td>
                                    <td style="width:200px">
                                        <table>
                                            <tr>
                                                <td class="text-center pe-3">
                                                    <i class="bi bi-filetype-xls export-xls-icon"
                                                        style="color: #0B4499; font-size: 1.5rem;"></i>
                                                </td>
                                                <td class="text-center pe-3">
                                                    <i class="bi bi-printer-fill print-icon"
                                                        style="color: #0B4499; font-size: 1.5rem;"></i>
                                                </td>
                                                <td class="text-center pe-3">
                                                    <a href="" class="action edit" id="edit-admin">
                                                        <img src="{{ asset('images/edit-icon.svg') }}" alt="Edit"
                                                            width="20" height="20" />
                                                    </a>
                                                </td>
                                                <td class="text-center">
                                                    <form id="" action="#" method="POST"
                                                        style="display: inline;">
                                                        @csrf
                                                        @method('DELETE')
                                                        <button type="submit"
                                                            style="border: none; background: none; padding: 0; cursor: pointer;">
                                                            <span class="action delete">
                                                                <img src="{{ asset('images/delete-icon.svg') }}"
                                                                    alt="Delete" width="20" height="20" />
                                                            </span>
                                                        </button>
                                                    </form>
                                                </td>
                                            </tr>
                                        </table>
                                    </td>
                                </tr>
                            </table>
                        </div>

                        <div class="table_collapse_table table-program table-responsive table-grey">
                            <table class="table table-hover team-table d-none" width="100%"
                                id="program-table{{ $registration->player->id }}">
                                <thead>
                                    <tr>
                                        <th width="40">&nbsp;</th>
                                        <th style="width:200px">Name</th>
                                        <th style="width:200px">Sport</th>
                                        <th style="width:200px">Start Date</th>
                                        <th style="width:200px">End Date</th>
                                        <th style="width:200px">Frequency</th>
                                        <th style="width:200px">Payment</th>
                                    </tr>
                                </thead>

                                <tbody>
                                    @if ($registration->program)
                                        <tr>
                                            <td>&nbsp;</td>
                                            <td>{{ $registration->program->name }}</td>
                                            <td>{{ $registration->program->sport }}</td>
                                            <td>{{ $registration->program->start_date }}</td>
                                            <td>{{ $registration->program->end_date }}</td>
                                            <td>{{ $registration->program->frequency }}</td>
                                            <td>{{ $registration->program->payment }}</td>
                                        </tr>
                                    @else
                                        <tr>
                                            <td colspan="7" style="text-align: center; color: red;">
                                                No program data available.
                                            </td>
                                        </tr>
                                    @endif
                                </tbody>
                            </table>
                        </div>
                        @endif
                    @endforeach
                @elseif(isset($filteredData) && $filteredData->isEmpty())
                    <div class="no-data-found"
                        style="text-align: center; font-size: 18px; color: #FF0000; margin-top: 20px;">
                        No players found for this program in the specified town.
                    </div>
                @endif



                @if (isset($playersByTownWithGender) && $playersByTownWithGender->isNotEmpty())
                    <div class="tables_heading table-program table-responsive">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th width="40" class="py-4">&nbsp;</th>
                                    <th style="width:200px" class="py-4">First Name</th>
                                    <th style="width:200px" class="py-4">Last Name</th>
                                    <th style="width:200px" class="py-4">Age</th>
                                    <th style="width:200px" class="py-4">Gender</th>
                                    <th style="width:200px" class="py-4">Email</th>
                                    <th style="width:200px" class="py-4">Town</th>
                                    <th style="width:200px">&nbsp;</th>
                                </tr>
                            </thead>
                        </table>
                    </div>

                    @foreach ($playersByTownWithGender as $player)
                        <div class="tables_collapse_heading table-heading-grey">
                            <table class="table table-hover">
                                <tr>
                                    <td width="40">
                                        <div class="icon">
                                            <i class="bi bi-caret-up-fill toggle-arrow"
                                                style="color:#0B4499; cursor: pointer;"
                                                data-toggle="{{ $player->id }}"></i>
                                        </div>
                                    </td>
                                    <td style="width:200px" class="text-custom">
                                        {{ $player->firstName }}
                                    </td>
                                    <td style="width:200px" class="text-custom">
                                        {{ $player->lastName }}
                                    </td>
                                    <td style="width:200px" class="text-custom">
                                        {{ $player->age }}
                                    </td>
                                    <td style="width:200px" class="text-custom">
                                        {{ $player->gender }}
                                    </td>
                                    <td style="width:200px" class="text-custom">
                                        {{ $player->email ?? 'N/A' }}
                                    </td>
                                    <td style="width:200px" class="text-custom">
                                        {{ $player->town }}
                                    </td>

                                    <td style="width:200px">
                                        <table>
                                            <tr>
                                                <td class="text-center pe-3">
                                                    <i class="bi bi-filetype-xls export-xls-icon"
                                                        style="color: #0B4499; font-size: 1.5rem;"></i>
                                                </td>
                                                <td class="text-center pe-3">
                                                    <i class="bi bi-printer-fill print-icon"
                                                        style="color: #0B4499; font-size: 1.5rem;"></i>
                                                </td>
                                                <td class="text-center pe-3">
                                                    <a href="" class="action edit" id="edit-admin">
                                                        <img src="{{ asset('images/edit-icon.svg') }}" alt="Edit"
                                                            width="20" height="20" />
                                                    </a>
                                                </td>
                                                <td class="text-center">
                                                    <form id="" action="#" method="POST"
                                                        style="display: inline;">
                                                        @csrf
                                                        @method('DELETE')
                                                        <button type="submit"
                                                            style="border: none; background: none; padding: 0; cursor: pointer;">
                                                            <span class="action delete">
                                                                <img src="{{ asset('images/delete-icon.svg') }}"
                                                                    alt="Delete" width="20" height="20" />
                                                            </span>
                                                        </button>
                                                    </form>
                                                </td>
                                            </tr>
                                        </table>
                                    </td>
                                </tr>
                            </table>
                        </div>

                        <div class="table_collapse_table table-program table-responsive table-grey">
                            <table class="table table-hover team-table d-none" width="100%"
                                id="program-table{{ $player->id }}">
                                <thead>
                                    <tr>
                                        <th width="40">&nbsp;</th>
                                        <th style="width:200px">Name</th>
                                        <th style="width:200px">Sport</th>
                                        <th style="width:200px">Start Date</th>
                                        <th style="width:200px">End Date</th>
                                        <th style="width:200px">Frequency</th>
                                        <th style="width:200px">Payment</th>
                                    </tr>
                                </thead>

                                <tbody>
                                    @if ($player->programs->isNotEmpty())
                                        @foreach ($player->programs as $program)
                                            <tr>
                                                <td>&nbsp;</td>
                                                <td>{{ $program->name }}</td>
                                                <td>{{ $program->sport }}</td>
                                                <td>{{ $program->start_date }}</td>
                                                <td>{{ $program->end_date }}</td>
                                                <td>{{ $program->frequency }}</td>
                                                <td>{{ $program->payment }}</td>
                                            </tr>
                                        @endforeach
                                    @else
                                        <tr>
                                            <td colspan="7" style="text-align: center; color: red;">
                                                No programs enrolled for this player.
                                            </td>
                                        </tr>
                                    @endif
                                </tbody>
                            </table>
                        </div>
                    @endforeach
                @elseif(isset($playersByTownWithGender) && $playersByTownWithGender->isEmpty())
                    <div class="no-data-found"
                        style="text-align: center; font-size: 18px; color: #FF0000; margin-top: 20px;">
                        No players found for this gender in the specified town.
                    </div>
                @endif


                @if (isset($playersWithPrograms) && $playersWithPrograms->isNotEmpty())
                    <div class="tables_heading table-program table-responsive">
                        <table class="table custom-table user-management-table">
                            <thead>
                                <tr>
                                    <th width="40" class="py-4">&nbsp;</th>
                                    <th style="width:200px" class="py-4">First Name</th>
                                    <th style="width:200px" class="py-4">Last Name</th>
                                    <th style="width:200px" class="py-4">Age</th>
                                    <th style="width:200px" class="py-4">Gender</th>
                                    <th style="width:200px" class="py-4">Email</th>
                                    <th style="width:200px" class="py-4">Town</th>

                                    <th style="width:200px">&nbsp;</th>
                                </tr>
                            </thead>
                        </table>
                    </div>

                    @foreach ($playersWithPrograms as $player)
                        <div class="tables_collapse_heading table-heading-grey">
                            <table class="table table-hover user-management-table">
                                <tr>
                                    <td width="40">
                                        <div class="icon">
                                            <i class="bi bi-caret-up-fill toggle-arrow"
                                                style="color:#0B4499; cursor: pointer;"
                                                data-toggle="{{ $player->id }}"></i>
                                        </div>
                                    </td>
                                    <td style="width:200px" class="text-custom">
                                        {{ $player->firstName }}
                                    </td>
                                    <td style="width:200px" class="text-custom">
                                        {{ $player->lastName }}
                                    </td>
                                    <td style="width:200px" class="text-custom">
                                        {{ $player->age }}
                                    </td>
                                    <td style="width:200px" class="text-custom">
                                        {{ $player->gender }}
                                    </td>
                                    <td style="width:200px" class="text-custom">
                                        {{ $player->email ?? 'N/A' }}
                                    </td>
                                    <td style="width:200px" class="text-custom">
                                        {{ $player->town }}
                                    </td>

                                    <td style="width:200px">
                                        <table>
                                            <tr>
                                                <td class="text-center pe-3">
                                                    <i class="bi bi-filetype-xls export-xls-icon"
                                                        style="color: #0B4499; font-size: 1.5rem;"></i>
                                                </td>
                                                <td class="text-center pe-3">
                                                    <i class="bi bi-printer-fill print-icon"
                                                        style="color: #0B4499; font-size: 1.5rem;"></i>
                                                </td>
                                                <td class="text-center pe-3">
                                                    <a href="" class="action edit" id="edit-admin">
                                                        <img src="{{ asset('images/edit-icon.svg') }}" alt="Edit"
                                                            width="20" height="20" />
                                                    </a>
                                                </td>
                                                <td class="text-center">
                                                    <form id="" action="#" method="POST"
                                                        style="display: inline;">
                                                        @csrf
                                                        @method('DELETE')
                                                        <button type="submit"
                                                            style="border: none; background: none; padding: 0; cursor: pointer;">
                                                            <span class="action delete">
                                                                <img src="{{ asset('images/delete-icon.svg') }}"
                                                                    alt="Delete" width="20" height="20" />
                                                            </span>
                                                        </button>
                                                    </form>
                                                </td>
                                            </tr>
                                        </table>
                                    </td>
                                </tr>
                            </table>
                        </div>

                        <div class="table_collapse_table table-program table-responsive table-grey">
                            <table class="table table-hover team-table d-none" width="100%"
                                id="program-table{{ $player->id }}">
                                <thead>
                                    <tr>
                                        <th width="40">&nbsp;</th>
                                        <th style="width:200px">Name</th>
                                        <th style="width:200px">Sport</th>
                                        <th style="width:200px">Start Date</th>
                                        <th style="width:200px">End Date</th>
                                        <th style="width:200px">Frequency</th>
                                        <th style="width:200px">Payment</th>
                                    </tr>
                                </thead>

                                <tbody>
                                    @if ($player->programs->isNotEmpty())
                                        @foreach ($player->programs as $program)
                                            <tr>
                                                <td>&nbsp;</td>
                                                <td>{{ $program->name }}</td>
                                                <td>{{ $program->sport }}</td>
                                                <td>{{ $program->start_date }}</td>
                                                <td>{{ $program->end_date }}</td>
                                                <td>{{ $program->frequency }}</td>
                                                <td>{{ $program->payment }}</td>
                                            </tr>
                                        @endforeach
                                    @else
                                        <tr>
                                            <td colspan="7" style="text-align: center; color: red;">
                                                No programs enrolled for this player.
                                            </td>
                                        </tr>
                                    @endif
                                </tbody>
                            </table>
                        </div>
                    @endforeach
                @elseif(isset($playersWithPrograms) && $playersWithPrograms->isEmpty())
                    <div class="no-data-found"
                        style="text-align: center; font-size: 18px; color: #FF0000; margin-top: 20px;">
                        No players found in this town enrolled in the selected sport.
                    </div>
                @endif


                @if (isset($playerWithSport) && $playerWithSport->isNotEmpty())
                    <div class="tables_heading table-program table-responsive">
                        <table class="table custom-table user-management-table">
                            <thead>
                                <tr>
                                    <th width="40" class="py-4">&nbsp;</th>
                                    <th style="width:200px" class="py-4">First Name</th>
                                    <th style="width:200px" class="py-4">Last Name</th>
                                    <th style="width:200px" class="py-4">Age</th>
                                    <th style="width:200px" class="py-4">Gender</th>
                                    <th style="width:200px" class="py-4">Email</th>
                                    <th style="width:200px" class="py-4">Town</th>

                                    <th style="width:200px">&nbsp;</th>
                                </tr>
                            </thead>
                        </table>
                    </div>

                    <div class="tables_collapse_heading table-heading-grey">
                        <table class="table table-hover user-management-table">
                            <tr>
                                <td width="40">
                                    <div class="icon">
                                        <i class="bi bi-caret-up-fill toggle-arrow"
                                            style="color:#0B4499; cursor: pointer;"
                                            data-toggle="{{ $player->id }}"></i>
                                    </div>
                                </td>
                                <td style="width:200px" class="text-custom">
                                    {{ $player->firstName }}
                                </td>
                                <td style="width:200px" class="text-custom">
                                    {{ $player->lastName }}
                                </td>
                                <td style="width:200px" class="text-custom">
                                    {{ $player->age }}
                                </td>
                                <td style="width:200px" class="text-custom">
                                    {{ $player->gender }}
                                </td>
                                <td style="width:200px" class="text-custom">
                                    {{ $player->email ?? 'N/A' }}
                                </td>
                                <td style="width:200px" class="text-custom">
                                    {{ $player->town }}
                                </td>


                                <td style="width:200px">
                                    <table>
                                        <tr>
                                            <td class="text-center pe-3">
                                                <i class="bi bi-filetype-xls export-xls-icon"
                                                    style="color: #0B4499; font-size: 1.5rem;"></i>
                                            </td>
                                            <td class="text-center pe-3">
                                                <i class="bi bi-printer-fill print-icon"
                                                    style="color: #0B4499; font-size: 1.5rem;"></i>
                                            </td>
                                            <td class="text-center pe-3">
                                                <a href="" class="action edit" id="edit-admin">
                                                    <img src="{{ asset('images/edit-icon.svg') }}" alt="Edit"
                                                        width="20" height="20" />
                                                </a>
                                            </td>
                                            <td class="text-center">
                                                <form id="" action="#" method="POST"
                                                    style="display: inline;">
                                                    @csrf
                                                    @method('DELETE')
                                                    <button type="submit"
                                                        style="border: none; background: none; padding: 0; cursor: pointer;">
                                                        <span class="action delete">
                                                            <img src="{{ asset('images/delete-icon.svg') }}"
                                                                alt="Delete" width="20" height="20" />
                                                        </span>
                                                    </button>
                                                </form>
                                            </td>
                                        </tr>
                                    </table>
                                </td>
                            </tr>
                        </table>
                    </div>

                    <div class="table_collapse_table table-program table-responsive table-grey">
                        <table class="table table-hover team-table d-none" width="100%"
                            id="program-table{{ $player->id }}">
                            <thead>
                                <tr>
                                    <th width="40">&nbsp;</th>
                                    <th style="width:200px">Name</th>
                                    <th style="width:200px">Sport</th>
                                    <th style="width:200px">Start Date</th>
                                    <th style="width:200px">End Date</th>
                                    <th style="width:200px">Frequency</th>
                                    <th style="width:200px">Payment</th>
                                </tr>
                            </thead>

                            <tbody>
                                @if ($playerWithSport->isNotEmpty())
                                    @foreach ($playerWithSport as $registration)
                                        <tr>
                                            <td>&nbsp;</td>
                                            <td>{{ $registration->program->name ?? 'N/A' }}</td>
                                            <td>{{ $registration->program->sport ?? 'N/A' }}</td>
                                            <td>{{ $registration->program->start_date ?? 'N/A' }}</td>
                                            <td>{{ $registration->program->end_date ?? 'N/A' }}</td>
                                            <td>{{ $registration->program->frequency ?? 'N/A' }}</td>
                                            <td>{{ $registration->program->payment ?? 'N/A' }}</td>
                                        </tr>
                                    @endforeach
                                @else
                                    <tr>
                                        <td colspan="6" class="text-center">No programs found for this player.</td>
                                    </tr>
                                @endif
                            </tbody>
                        </table>
                    </div>
                @elseif(isset($playerWithSport) && $playerWithSport->isEmpty())
                    <div class="no-data-found"
                        style="text-align: center; font-size: 18px; color: #FF0000; margin-top: 20px;">
                        Player Not Enrolled in specific Sport.
                    </div>
                @endif



                @if (isset($playersWithDateAndAge) && $playersWithDateAndAge->isNotEmpty())
                    <div class="tables_heading table-program table-responsive">
                        <table class="table custom-table user-management-table">
                            <thead>
                                <tr>
                                    <th width="40" class="py-4">&nbsp;</th>
                                    <th style="width:200px" class="py-4">First Name</th>
                                    <th style="width:200px" class="py-4">Last Name</th>
                                    <th style="width:200px" class="py-4">Age</th>
                                    <th style="width:200px" class="py-4">Gender</th>
                                    <th style="width:200px" class="py-4">Email</th>
                                    <th style="width:200px" class="py-4">Town</th>
                                    <th style="width:200px">&nbsp;</th>
                                    <th style="width:200px">&nbsp;</th>
                                    <th style="width:200px">&nbsp;</th>
                                </tr>
                            </thead>
                        </table>
                    </div>

                    @foreach ($playersWithDateAndAge as $player)
                        <div class="tables_collapse_heading table-heading-grey">
                            <table class="table table-hover user-management-table">
                                <tr>
                                    <td width="40">
                                        <div class="icon">
                                            <i class="bi bi-caret-up-fill toggle-arrow"
                                                style="color:#0B4499; cursor: pointer;"
                                                data-toggle="{{ $player->id }}"></i>
                                        </div>
                                    </td>
                                    <td style="width:200px" class="text-custom">
                                        {{ $player->firstName }}
                                    </td>
                                    <td style="width:200px" class="text-custom">
                                        {{ $player->lastName }}
                                    </td>
                                    <td style="width:200px" class="text-custom">
                                        {{ $player->age }}
                                    </td>
                                    <td style="width:200px" class="text-custom">
                                        {{ $player->gender }}
                                    </td>
                                    <td style="width:200px" class="text-custom">
                                        {{ $player->email ?? 'N/A' }}
                                    </td>
                                    <td style="width:200px" class="text-custom">
                                        {{ $player->town }}
                                    </td>


                                    <td style="width:200px">
                                        <table>
                                            <tr>
                                                <td class="text-center pe-3">
                                                    <i class="bi bi-filetype-xls export-xls-icon"
                                                        style="color: #0B4499; font-size: 1.5rem;"></i>
                                                </td>
                                                <td class="text-center pe-3">
                                                    <i class="bi bi-printer-fill print-icon"
                                                        style="color: #0B4499; font-size: 1.5rem;"></i>
                                                </td>
                                                <td class="text-center pe-3">
                                                    <a href="" class="action edit" id="edit-admin">
                                                        <img src="{{ asset('images/edit-icon.svg') }}" alt="Edit"
                                                            width="20" height="20" />
                                                    </a>
                                                </td>
                                                <td class="text-center">
                                                    <form action="#" method="POST" style="display: inline;">
                                                        @csrf
                                                        @method('DELETE')
                                                        <button type="submit"
                                                            style="border: none; background: none; padding: 0; cursor: pointer;">
                                                            <span class="action delete">
                                                                <img src="{{ asset('images/delete-icon.svg') }}"
                                                                    alt="Delete" width="20" height="20" />
                                                            </span>
                                                        </button>
                                                    </form>
                                                </td>
                                            </tr>
                                        </table>
                                    </td>
                                </tr>
                            </table>
                        </div>

                        <div class="table_collapse_table table-program table-responsive table-grey">
                            <table class="table table-hover team-table d-none" width="100%"
                                id="program-table{{ $player->id }}">
                                <thead>
                                    <tr>
                                        <th width="40">&nbsp;</th>
                                        <th style="width:200px">Name</th>
                                        <th style="width:200px">Sport</th>
                                        <th style="width:200px">Start Date</th>
                                        <th style="width:200px">End Date</th>
                                        <th style="width:200px">Frequency</th>
                                        <th style="width:200px">Payment</th>
                                    </tr>
                                </thead>

                                <tbody>
                                    @if ($player->registrations->isNotEmpty())
                                        @foreach ($player->registrations as $registration)
                                            @if($registration->program)
                                            <tr>
                                                <td width="40">&nbsp;</td>
                                                <td>{{ $registration->program->name ?? 'N/A' }}</td>
                                                <td>{{ $registration->program->sport ?? 'N/A' }}</td>
                                                <td>{{ $registration->program->start_date ?? 'N/A' }}</td>
                                                <td>{{ $registration->program->end_date ?? 'N/A' }}</td>
                                                <td>{{ $registration->program->frequency ?? 'N/A' }}</td>
                                                <td>{{ $registration->program->payment ?? 'N/A' }}</td>
                                            </tr>
                                            @endif
                                        @endforeach
                                    @else
                                        <tr>
                                            <td colspan="7" class="text-center">No programs found for this player.</td>
                                        </tr>
                                    @endif
                                </tbody>
                            </table>
                        </div>
                    @endforeach

                    <div class="pagination">
                        {{ $playersWithDateAndAge->onEachSide(1)->links('pagination::bootstrap-5') }}
                    </div>
                @elseif(isset($playersWithDateAndAge) && $playersWithDateAndAge->isEmpty())
                    <div class="no-data-found"
                        style="text-align: center; font-size: 18px; color: #FF0000; margin-top: 20px;">
                        No Player with Given Age registered on Given Dates
                    </div>
                @endif


                @if (isset($playersByGenderWithDate) && $playersByGenderWithDate->isNotEmpty())
                    <div class="tables_heading table-program table-responsive">
                        <table class="table custom-table user-management-table">
                            <thead>
                                <tr>
                                    <th width="40" class="py-4">&nbsp;</th>
                                    <th style="width:200px" class="py-4">First Name</th>
                                    <th style="width:200px" class="py-4">Last Name</th>
                                    <th style="width:200px" class="py-4">Age</th>
                                    <th style="width:200px" class="py-4">Gender</th>
                                    <th style="width:200px" class="py-4">Email</th>
                                    <th style="width:200px" class="py-4">Town</th>
                                    <th style="width:200px">&nbsp;</th>
                                    <th style="width:200px">&nbsp;</th>
                                    <th style="width:200px">&nbsp;</th>
                                </tr>
                            </thead>
                        </table>
                    </div>

                    @foreach ($playersByGenderWithDate as $player)
                        <div class="tables_collapse_heading table-heading-grey">
                            <table class="table table-hover user-management-table">
                                <tr>
                                    <td width="40">
                                        <div class="icon">
                                            <i class="bi bi-caret-up-fill toggle-arrow"
                                                style="color:#0B4499; cursor: pointer;"
                                                data-toggle="{{ $player->id }}"></i>
                                        </div>
                                    </td>
                                    <td style="width:200px" class="text-custom">
                                        {{ $player->firstName }}
                                    </td>
                                    <td style="width:200px" class="text-custom">
                                        {{ $player->lastName }}
                                    </td>
                                    <td style="width:200px" class="text-custom">
                                        {{ $player->age }}
                                    </td>
                                    <td style="width:200px" class="text-custom">
                                        {{ $player->gender }}
                                    </td>
                                    <td style="width:200px" class="text-custom">
                                        {{ $player->email ?? 'N/A' }}
                                    </td>
                                    <td style="width:200px" class="text-custom">
                                        {{ $player->town }}
                                    </td>

                                    <td style="width:200px">
                                        <table>
                                            <tr>
                                                <td class="text-center pe-3">
                                                    <i class="bi bi-filetype-xls export-xls-icon"
                                                        style="color: #0B4499; font-size: 1.5rem;"></i>
                                                </td>
                                                <td class="text-center pe-3">
                                                    <i class="bi bi-printer-fill print-icon"
                                                        style="color: #0B4499; font-size: 1.5rem;"></i>
                                                </td>
                                                <td class="text-center pe-3">
                                                    <a href="" class="action edit" id="edit-admin">
                                                        <img src="{{ asset('images/edit-icon.svg') }}" alt="Edit"
                                                            width="20" height="20" />
                                                    </a>
                                                </td>
                                                <td class="text-center">
                                                    <form action="#" method="POST" style="display: inline;">
                                                        @csrf
                                                        @method('DELETE')
                                                        <button type="submit"
                                                            style="border: none; background: none; padding: 0; cursor: pointer;">
                                                            <span class="action delete">
                                                                <img src="{{ asset('images/delete-icon.svg') }}"
                                                                    alt="Delete" width="20" height="20" />
                                                            </span>
                                                        </button>
                                                    </form>
                                                </td>
                                            </tr>
                                        </table>
                                    </td>
                                </tr>
                            </table>
                        </div>

                        <div class="table_collapse_table table-program table-responsive table-grey">
                            <table class="table table-hover team-table d-none" width="100%"
                                id="program-table{{ $player->id }}">
                                <thead>
                                    <tr>
                                        <th width="40">&nbsp;</th>
                                        <th style="width:200px">Name</th>
                                        <th style="width:200px">Sport</th>
                                        <th style="width:200px">Start Date</th>
                                        <th style="width:200px">End Date</th>
                                        <th style="width:200px">Frequency</th>
                                        <th style="width:200px">Payment</th>
                                    </tr>
                                </thead>

                                <tbody>
                                    @if ($player->registrations->isNotEmpty())
                                        @foreach ($player->registrations as $registration)
                                            @if($registration->program)
                                            <tr>
                                                <td width="40">&nbsp;</td>
                                                <td>{{ $registration->program->name ?? 'N/A' }}</td>
                                                <td>{{ $registration->program->sport ?? 'N/A' }}</td>
                                                <td>{{ $registration->program->start_date ?? 'N/A' }}</td>
                                                <td>{{ $registration->program->end_date ?? 'N/A' }}</td>
                                                <td>{{ $registration->program->frequency ?? 'N/A' }}</td>
                                                <td>{{ $registration->program->payment ?? 'N/A' }}</td>
                                            </tr>
                                            @endif
                                        @endforeach
                                    @else
                                        <tr>
                                            <td colspan="7" class="text-center">No programs found for this player.</td>
                                        </tr>
                                    @endif
                                </tbody>
                            </table>
                        </div>
                    @endforeach

                    <div class="pagination">
                        {{ $playersByGenderWithDate->onEachSide(1)->links('pagination::bootstrap-5') }}
                    </div>
                @elseif(isset($playersByGenderWithDate) && $playersByGenderWithDate->isEmpty())
                    <div class="no-data-found"
                        style="text-align: center; font-size: 18px; color: #FF0000; margin-top: 20px;">
                        No Player with Given Age registered on Given Dates
                    </div>
                @endif



                @if (isset($playersWithProgramsAndGender) && $playersWithProgramsAndGender->isNotEmpty())
                    <div class="tables_heading table-program table-responsive">
                        <table class="table custom-table user-management-table">
                            <thead>
                                <tr>
                                    <th width="40" class="py-4">&nbsp;</th>
                                    <th style="width:200px" class="py-4">First Name</th>
                                    <th style="width:200px" class="py-4">Last Name</th>
                                    <th style="width:200px" class="py-4">Age</th>
                                    <th style="width:200px" class="py-4">Gender</th>
                                    <th style="width:200px" class="py-4">Email</th>
                                    <th style="width:200px" class="py-4">Town</th>
                                    <th style="width:200px">&nbsp;</th>
                                    <th style="width:200px">&nbsp;</th>
                                    <th style="width:200px">&nbsp;</th>
                                </tr>
                            </thead>
                        </table>
                    </div>

                    @foreach ($playersWithProgramsAndGender as $player)
                        <div class="tables_collapse_heading table-heading-grey">
                            <table class="table table-hover user-management-table">
                                <tr>
                                    <td width="40">
                                        <div class="icon">
                                            <i class="bi bi-caret-up-fill toggle-arrow"
                                                style="color:#0B4499; cursor: pointer;"
                                                data-toggle="{{ $player->id }}"></i>
                                        </div>
                                    </td>
                                    <td style="width:200px" class="text-custom">
                                        {{ $player->firstName }}
                                    </td>
                                    <td style="width:200px" class="text-custom">
                                        {{ $player->lastName }}
                                    </td>
                                    <td style="width:200px" class="text-custom">
                                        {{ $player->age }}
                                    </td>
                                    <td style="width:200px" class="text-custom">
                                        {{ $player->gender }}
                                    </td>
                                    <td style="width:200px" class="text-custom">
                                        {{ $player->email ?? 'N/A' }}
                                    </td>
                                    <td style="width:200px" class="text-custom">
                                        {{ $player->town }}
                                    </td>

                                    <td style="width:200px">
                                        <table>
                                            <tr>
                                                <td class="text-center pe-3">
                                                    <i class="bi bi-filetype-xls export-xls-icon"
                                                        style="color: #0B4499; font-size: 1.5rem;"></i>
                                                </td>
                                                <td class="text-center pe-3">
                                                    <i class="bi bi-printer-fill print-icon"
                                                        style="color: #0B4499; font-size: 1.5rem;"></i>
                                                </td>
                                                <td class="text-center pe-3">
                                                    <a href="" class="action edit" id="edit-admin">
                                                        <img src="{{ asset('images/edit-icon.svg') }}" alt="Edit"
                                                            width="20" height="20" />
                                                    </a>
                                                </td>
                                                <td class="text-center">
                                                    <form action="#" method="POST" style="display: inline;">
                                                        @csrf
                                                        @method('DELETE')
                                                        <button type="submit"
                                                            style="border: none; background: none; padding: 0; cursor: pointer;">
                                                            <span class="action delete">
                                                                <img src="{{ asset('images/delete-icon.svg') }}"
                                                                    alt="Delete" width="20" height="20" />
                                                            </span>
                                                        </button>
                                                    </form>
                                                </td>
                                            </tr>
                                        </table>
                                    </td>
                                </tr>
                            </table>
                        </div>

                        <div class="table_collapse_table table-program table-responsive table-grey">
                            <table class="table table-hover team-table d-none" width="100%"
                                id="program-table{{ $player->id }}">
                                <thead>
                                    <tr>
                                        <th width="40">&nbsp;</th>
                                        <th style="width:200px">Name</th>
                                        <th style="width:200px">Sport</th>
                                        <th style="width:200px">Start Date</th>
                                        <th style="width:200px">End Date</th>
                                        <th style="width:200px">Frequency</th>
                                        <th style="width:200px">Payment</th>
                                    </tr>
                                </thead>

                                <tbody>
                                    @if ($player->registrations->isNotEmpty())
                                        @foreach ($player->registrations as $registration)
                                            @if($registration->program)
                                            <tr>
                                                <td width="40">&nbsp;</td>
                                                <td>{{ $registration->program->name ?? 'N/A' }}</td>
                                                <td>{{ $registration->program->sport ?? 'N/A' }}</td>
                                                <td>{{ $registration->program->start_date ?? 'N/A' }}</td>
                                                <td>{{ $registration->program->end_date ?? 'N/A' }}</td>
                                                <td>{{ $registration->program->frequency ?? 'N/A' }}</td>
                                                <td>{{ $registration->program->payment ?? 'N/A' }}</td>
                                            </tr>
                                            @endif
                                        @endforeach
                                    @else
                                        <tr>
                                            <td colspan="7" class="text-center">No programs found for this player.</td>
                                        </tr>
                                    @endif
                                </tbody>
                            </table>
                        </div>
                    @endforeach
                @elseif(isset($playersWithProgramsAndGender) && $playersWithProgramsAndGender->isEmpty())
                    <div class="no-data-found"
                        style="text-align: center; font-size: 18px; color: #FF0000; margin-top: 20px;">
                        No Player with Given Age registered on Given Dates
                    </div>
                @endif



                @if (isset($playersByGenderAndAge) && $playersByGenderAndAge->isNotEmpty())
                    <div class="tables_heading table-program table-responsive">
                        <table class="table custom-table user-management-table">
                            <thead>
                                <tr>
                                    <th width="40" class="py-4">&nbsp;</th>
                                    <th style="width:200px" class="py-4">First Name</th>
                                    <th style="width:200px" class="py-4">Last Name</th>
                                    <th style="width:200px" class="py-4">Age</th>
                                    <th style="width:200px" class="py-4">Gender</th>
                                    <th style="width:200px" class="py-4">Email</th>
                                    <th style="width:200px" class="py-4">Town</th>
                                    <th style="width:200px">&nbsp;</th>
                                    <th style="width:200px">&nbsp;</th>
                                    <th style="width:200px">&nbsp;</th>
                                </tr>
                            </thead>
                        </table>
                    </div>

                    @foreach ($playersByGenderAndAge as $player)
                        <div class="tables_collapse_heading table-heading-grey">
                            <table class="table table-hover user-management-table">
                                <tr>
                                    <td width="40">
                                        <div class="icon">
                                            <i class="bi bi-caret-up-fill toggle-arrow"
                                                style="color:#0B4499; cursor: pointer;"
                                                data-toggle="{{ $player->id }}"></i>
                                        </div>
                                    </td>
                                    <td style="width:200px" class="text-custom">
                                        {{ $player->firstName }}
                                    </td>
                                    <td style="width:200px" class="text-custom">
                                        {{ $player->lastName }}
                                    </td>
                                    <td style="width:200px" class="text-custom">
                                        {{ $player->age }}
                                    </td>
                                    <td style="width:200px" class="text-custom">
                                        {{ $player->gender }}
                                    </td>
                                    <td style="width:200px" class="text-custom">
                                        {{ $player->email ?? 'N/A' }}
                                    </td>
                                    <td style="width:200px" class="text-custom">
                                        {{ $player->town }}
                                    </td>

                                    <td style="width:200px">
                                        <table>
                                            <tr>
                                                <td class="text-center pe-3">
                                                    <i class="bi bi-filetype-xls export-xls-icon"
                                                        style="color: #0B4499; font-size: 1.5rem;"></i>
                                                </td>
                                                <td class="text-center pe-3">
                                                    <i class="bi bi-printer-fill print-icon"
                                                        style="color: #0B4499; font-size: 1.5rem;"></i>
                                                </td>
                                                <td class="text-center pe-3">
                                                    <a href="" class="action edit" id="edit-admin">
                                                        <img src="{{ asset('images/edit-icon.svg') }}" alt="Edit"
                                                            width="20" height="20" />
                                                    </a>
                                                </td>
                                                <td class="text-center">
                                                    <form action="#" method="POST" style="display: inline;">
                                                        @csrf
                                                        @method('DELETE')
                                                        <button type="submit"
                                                            style="border: none; background: none; padding: 0; cursor: pointer;">
                                                            <span class="action delete">
                                                                <img src="{{ asset('images/delete-icon.svg') }}"
                                                                    alt="Delete" width="20" height="20" />
                                                            </span>
                                                        </button>
                                                    </form>
                                                </td>
                                            </tr>
                                        </table>
                                    </td>
                                </tr>
                            </table>
                        </div>

                        <div class="table_collapse_table table-program table-responsive table-grey">
                            <table class="table table-hover team-table d-none" width="100%"
                                id="program-table{{ $player->id }}">
                                <thead>
                                    <tr>
                                        <th width="40">&nbsp;</th>
                                        <th style="width:200px">Name</th>
                                        <th style="width:200px">Sport</th>
                                        <th style="width:200px">Start Date</th>
                                        <th style="width:200px">End Date</th>
                                        <th style="width:200px">Frequency</th>
                                        <th style="width:200px">Payment</th>
                                    </tr>
                                </thead>

                                <tbody>
                                    @if ($player->registrations->isNotEmpty())
                                        @foreach ($player->registrations as $registration)
                                            <tr>
                                                <td width="40">&nbsp;</td>
                                                <td>{{ $registration->program->name }}</td>
                                                <td>{{ $registration->program->sport ?? 'N/A' }}</td>
                                                <td>{{ $registration->program->start_date ?? 'N/A' }}</td>
                                                <td>{{ $registration->program->end_date ?? 'N/A' }}</td>
                                                <td>{{ $registration->program->frequency }}</td>
                                                <td>{{ $registration->program->payment }}</td>
                                            </tr>
                                        @endforeach
                                    @else
                                        <tr>
                                            <td colspan="7" class="text-center">No programs found for this player.</td>
                                        </tr>
                                    @endif
                                </tbody>
                            </table>
                        </div>
                    @endforeach

                    <div class="pagination">
                        {{ $playersByGenderAndAge->onEachSide(1)->links('pagination::bootstrap-5') }}
                    </div>
                @elseif(isset($playersByGenderAndAge) && $playersByGenderAndAge->isEmpty())
                    <div class="no-data-found"
                        style="text-align: center; font-size: 18px; color: #FF0000; margin-top: 20px;">
                        No Player with Given Gender And Age
                    </div>
                @endif



                @if (isset($playersWithSportAndGender) && $playersWithSportAndGender->isNotEmpty())
                    <div class="tables_heading table-program table-responsive">
                        <table class="table custom-table user-management-table">
                            <thead>
                                <tr>
                                    <th width="40" class="py-4">&nbsp;</th>
                                    <th style="width:200px" class="py-4">First Name</th>
                                    <th style="width:200px" class="py-4">Last Name</th>
                                    <th style="width:200px" class="py-4">Age</th>
                                    <th style="width:200px" class="py-4">Gender</th>
                                    <th style="width:200px" class="py-4">Email</th>
                                    <th style="width:200px" class="py-4">Town</th>
                                    <th style="width:200px">&nbsp;</th>
                                </tr>
                            </thead>
                        </table>
                    </div>

                    @foreach ($playersWithSportAndGender as $player)
                        <div class="tables_collapse_heading table-heading-grey">
                            <table class="table table-hover user-management-table">
                                <tr>
                                    <td width="40">
                                        <div class="icon">
                                            <i class="bi bi-caret-up-fill toggle-arrow"
                                                style="color:#0B4499; cursor: pointer;"
                                                data-toggle="{{ $player->id }}"></i>
                                        </div>
                                    </td>
                                    <td style="width:200px" class="text-custom">
                                        {{ $player->firstName }}
                                    </td>
                                    <td style="width:200px" class="text-custom">
                                        {{ $player->lastName }}
                                    </td>
                                    <td style="width:200px" class="text-custom">
                                        {{ $player->age }}
                                    </td>
                                    <td style="width:200px" class="text-custom">
                                        {{ $player->gender }}
                                    </td>
                                    <td style="width:200px" class="text-custom">
                                        {{ $player->email ?? 'N/A' }}
                                    </td>
                                    <td style="width:200px" class="text-custom">
                                        {{ $player->town }}
                                    </td>

                                    <td style="width:200px">
                                        <table>
                                            <tr>
                                                <td class="text-center pe-3">
                                                    <i class="bi bi-filetype-xls export-xls-icon"
                                                        style="color: #0B4499; font-size: 1.5rem;"></i>
                                                </td>
                                                <td class="text-center pe-3">
                                                    <i class="bi bi-printer-fill print-icon"
                                                        style="color: #0B4499; font-size: 1.5rem;"></i>
                                                </td>
                                                <td class="text-center pe-3">
                                                    <a href="" class="action edit" id="edit-admin">
                                                        <img src="{{ asset('images/edit-icon.svg') }}" alt="Edit"
                                                            width="20" height="20" />
                                                    </a>
                                                </td>
                                                <td class="text-center">
                                                    <form action="#" method="POST" style="display: inline;">
                                                        @csrf
                                                        @method('DELETE')
                                                        <button type="submit"
                                                            style="border: none; background: none; padding: 0; cursor: pointer;">
                                                            <span class="action delete">
                                                                <img src="{{ asset('images/delete-icon.svg') }}"
                                                                    alt="Delete" width="20" height="20" />
                                                            </span>
                                                        </button>
                                                    </form>
                                                </td>
                                            </tr>
                                        </table>
                                    </td>
                                </tr>
                            </table>
                        </div>

                        <div class="table_collapse_table table-program table-responsive table-grey">
                            <table class="table table-hover team-table d-none" width="100%"
                                id="program-table{{ $player->id }}">
                                <thead>
                                    <tr>
                                        <th width="40">&nbsp;</th>
                                        <th style="width:200px">Name</th>
                                        <th style="width:200px">Sport</th>
                                        <th style="width:200px">Start Date</th>
                                        <th style="width:200px">End Date</th>
                                        <th style="width:200px">Frequency</th>
                                        <th style="width:200px">Payment</th>
                                    </tr>
                                </thead>

                                <tbody>
                                    @if ($player->registrations->isNotEmpty())
                                        @foreach ($player->registrations as $registration)
                                            <tr>
                                                <td width="40">&nbsp;</td>
                                                <td>{{ $registration->program->name }}</td>
                                                <td>{{ $registration->program->sport ?? 'N/A' }}</td>
                                                <td>{{ $registration->program->start_date ?? 'N/A' }}</td>
                                                <td>{{ $registration->program->end_date ?? 'N/A' }}</td>
                                                <td>{{ $registration->program->frequency }}</td>
                                                <td>{{ $registration->program->payment }}</td>
                                            </tr>
                                        @endforeach
                                    @else
                                        <tr>
                                            <td colspan="7" class="text-center">No programs found for this player.
                                            </td>
                                        </tr>
                                    @endif
                                </tbody>
                            </table>
                        </div>
                    @endforeach

                    <div class="pagination">
                        {{ $playersWithSportAndGender->onEachSide(1)->links('pagination::bootstrap-5') }}
                    </div>
                @elseif(isset($playersWithSportAndGender) && $playersWithSportAndGender->isEmpty())
                    <div class="no-data-found"
                        style="text-align: center; font-size: 18px; color: #FF0000; margin-top: 20px;">
                        No Player with Given Sport And Gender.
                    </div>
                @endif



























            </div>
        </div>
    </section>
@endsection

<script>
    document.addEventListener("DOMContentLoaded", function() {
        // Handle browser back button issues
        if (window.performance && window.performance.navigation.type === window.performance.navigation.TYPE_BACK_FORWARD) {
            // User came here via back/forward button
            window.location.reload();
        }

        // Prevent caching of this page
        window.addEventListener('beforeunload', function() {
            // Clear any cached data
            if ('caches' in window) {
                caches.keys().then(function(names) {
                    names.forEach(function(name) {
                        caches.delete(name);
                    });
                });
            }
        });

        // Handle page visibility changes (when user switches tabs and comes back)
        document.addEventListener('visibilitychange', function() {
            if (!document.hidden) {
                // Page became visible again, check if we need to refresh
                const lastActivity = sessionStorage.getItem('lastFilterActivity');
                const now = Date.now();
                if (lastActivity && (now - parseInt(lastActivity)) > 300000) { // 5 minutes
                    window.location.reload();
                }
            }
        });

        // Store activity timestamp
        sessionStorage.setItem('lastFilterActivity', Date.now());
    });

    document.addEventListener("click", function(event) {
        if (event.target.classList.contains("toggle-arrow")) {
            const arrow = event.target;
            const userId = arrow.getAttribute("data-toggle");

            const programTable = document.getElementById(`program-table${userId}`);

            if (programTable) {
                programTable.classList.toggle("d-none");

                if (programTable.classList.contains("d-none")) {
                    arrow.classList.remove("bi-caret-down-fill");
                    arrow.classList.add("bi-caret-up-fill");
                } else {
                    arrow.classList.remove("bi-caret-up-fill");
                    arrow.classList.add("bi-caret-down-fill");
                }
            } else {
                console.warn(`No table found for user ID: ${userId}`);
            }
        }
    });

    // Add error handling for any JavaScript errors
    window.addEventListener('error', function(e) {
        console.error('JavaScript error occurred:', e.error);
        // Optionally redirect to reports page if critical error
        if (e.error && e.error.message && e.error.message.includes('filter')) {
            setTimeout(function() {
                window.location.href = '{{ route("admin.reports") }}';
            }, 2000);
        }
    });
</script>
