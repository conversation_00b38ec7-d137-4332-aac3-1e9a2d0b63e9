<div>
    <div class="container">
        <div class="heading text-center mb-5">
            <h2 class="text-uppercase fs-6 mb-0">All Users</h2>
        </div>

        {{-- Livewire Filter Form --}}
        <div class="form row table-filter justify-content-center">
            <div class="col-md-auto">
                <div class="filter-option">
                    <div class="mb-4">
                        <label class="form-label text-uppercase" for="filter">Filter By</label>
                        <div class="select-arrow position-relative">
                            <select class="form-control" id="filter" wire:model.live="filter">
                                <option value="">All</option>
                                @foreach ($this->getAvailableFilters() as $filterOption)
                                    <option value="{{ $filterOption }}">{{ $filterOption }}</option>
                                @endforeach
                            </select>
                            <span class="arrow"><i class="bi bi-chevron-down"></i></span>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-auto">
                <div class="filter-option">
                    <div class="mb-4">
                        <label class="form-label text-uppercase" for="search">Search</label>
                        <div class="flex items-center">
                            <input class="form-control" id="search" type="text"
                                wire:model.live.debounce.500ms="search" placeholder="Search users..." />
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-auto">
                <div class="filter-option">
                    <div class="mb-4">
                        <label class="form-label text-uppercase" for="perPage">Per Page</label>
                        <div class="select-arrow position-relative">
                            <select class="form-control" id="perPage" wire:model.live="perPage">
                                <option value="5">5</option>
                                <option value="10">10</option>
                                <option value="25">25</option>
                                <option value="50">50</option>
                            </select>
                            <span class="arrow"><i class="bi bi-chevron-down"></i></span>
                        </div>
                    </div>
                </div>
            </div>
        </div>


        <div class="container">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <button id="sendEmailButton" class="cta"> Send Email</button>
                </div>
                <div>
                    @if (!$mergeMode)
                        <button wire:click="toggleMergeMode" class="cta">
                            <i class="bi bi-arrow-down-up"></i> Enable Merge Mode
                        </button>
                    @else
                        <div class="d-flex align-items-center gap-2">
                            <span class="badge bg-primary">{{ $this->getSelectedPlayersCount() }} selected</span>
                            <button wire:click="mergeSelectedPlayers"
                                wire:confirm="Are you sure you want to merge the selected players? The first selected player will be kept as the target, and all others will be merged into it. This action cannot be undone."
                                class="cta" @if ($this->getSelectedPlayersCount() < 2) disabled @endif>
                                <i class="bi bi-arrow-down-up"></i> Merge Selected
                            </button>

                            <button wire:click="cancelMergeMode" class="btn btn-secondary">Cancel</button>
                        </div>
                    @endif
                </div>
            </div>
        </div>

        @if ($mergeMode)
            <div class="container mt-3">
                <div class="alert alert-warning" role="alert">
                    <strong>Merge Mode Active:</strong> Select players to merge. The first selected player will be kept
                    as
                    the target, and all others will be merged into it. This action cannot be undone.
                </div>
            </div>
        @endif

        {{-- Users Table --}}
        <div class="program-table">
            <div class="table-program table-responsive" wire:loading.class="table-updating">
                <table class="table table-hover" width="100%" id="user-table">
                    <thead>
                        <tr>
                            @if ($mergeMode)
                                <th class="py-4" width="30">
                                    <input type="checkbox" class="form-check-input" wire:click="selectAllPlayers"
                                        title="Select all players on this page">
                                </th>
                            @endif
                            <th class="py-4">First Name</th>
                            <th class="py-4">Last Name</th>
                            <th class="py-4">User Type</th>
                            <th class="py-4">Email</th>
                            <th class="py-4">Town</th>
                            <th class="py-4">Age</th>
                            <th class="py-4">Grade</th>
                            <th class="py-4" width="20">Edit</th>
                            <th class="py-4" width="20">Delete</th>
                        </tr>
                    </thead>
                    <tbody>
                        @forelse ($users as $user)
                            <tr wire:key="user-{{ $user->id }}"
                                @if ($mergeMode && $user->hasRole('player') && $this->isPlayerSelected($user->id)) class="table-warning" @endif>
                                @if ($mergeMode)
                                    <td class="py-4" valign="middle">
                                        @if ($user->hasRole('player'))
                                            <input type="checkbox" class="form-check-input"
                                                wire:click="togglePlayerSelection({{ $user->id }})"
                                                @if ($this->isPlayerSelected($user->id)) checked @endif>
                                        @endif
                                    </td>
                                @endif
                                <td class="py-4" valign="middle">{{ $user->firstName }}</td>
                                <td class="py-4" valign="middle">{{ $user->lastName }}</td>
                                <td class="py-4" valign="middle">
                                    @foreach ($user->roles as $role)
                                        {{ $role->name }}@if (!$loop->last)
                                            ,
                                        @endif
                                    @endforeach
                                </td>
                                <td class="py-4" valign="middle">{{ $user->email ?? 'N/A' }}</td>
                                <td class="py-4" valign="middle">{{ $user->town ?? 'N/A' }}</td>
                                <td class="py-4" valign="middle">{{ $user->age ?? 'N/A' }}</td>
                                <td class="py-4" valign="middle">{{ $user->grade ?? 'N/A' }}</td>

                                @if ($user->hasRole('admin'))
                                    <td class="py-4" valign="middle">
                                        <a href="" class="action edit" id="edit-admin"
                                            data-admin-id="{{ $user->id }}">
                                            <img src="{{ asset('images/edit-icon.svg') }}" alt="Edit"
                                                width="20" height="20" />
                                        </a>
                                    </td>
                                @elseif($user->hasRole('coach'))
                                    <td class="py-4" valign="middle">
                                        <a href="" class="action edit" id="edit-coach"
                                            data-coach-id="{{ $user->id }}">
                                            <img src="{{ asset('images/edit-icon.svg') }}" alt="Edit"
                                                width="20" height="20" />
                                        </a>
                                    </td>
                                @elseif($user->hasRole('guardian'))
                                    <td class="py-4" valign="middle">
                                        <a href="" id="edit-guardian" data-guardian-id="{{ $user->id }}"
                                            class="action edit">
                                            <img src="{{ asset('images/edit-icon.svg') }}" alt="Edit"
                                                width="20" height="20" />
                                        </a>
                                    </td>
                                @elseif($user->hasRole('player'))
                                    <td class="py-4" valign="middle">
                                        <a href="" class="action edit" id="edit-player"
                                            data-player-id="{{ $user->id }}">
                                            <img src="{{ asset('images/edit-icon.svg') }}" alt="Edit"
                                                width="20" height="20" />
                                        </a>
                                    </td>
                                @endif

                                <td class="py-4" valign="middle">
                                    <form id="deleteUser-{{ $user->id }}"
                                        onsubmit="showConfirmation(event, 'deleteUser-{{ $user->id }}')"
                                        action="{{ route('admin.destroy', ['user' => $user->id]) }}" method="POST"
                                        class="inline-form mb-0">
                                        @csrf
                                        @method('DELETE')
                                        <button type="submit" class="action edit bg-transparent border-0 p-0">
                                            <img src="{{ asset('images/delete-icon.svg') }}" alt="Delete"
                                                width="18" height="20" />
                                        </button>
                                    </form>
                                </td>
                            </tr>
                            @empty
                                <tr>
                                    <td colspan="{{ $mergeMode ? '10' : '9' }}" class="text-center py-4">
                                        @if ($search || $filter)
                                            <p class="mb-0">No users found matching your search criteria.</p>
                                            <button class="btn btn-link p-0" wire:click="clearFilters">Clear
                                                filters</button>
                                        @else
                                            <p class="mb-0">No users found.</p>
                                        @endif
                                    </td>
                                </tr>
                            @endforelse
                        </tbody>
                    </table>

                    {{-- Custom Livewire Pagination --}}
                    <div class="mt-4 d-flex justify-content-center">
                        {{ $users->onEachSide(1)->links('custom-pagination') }}
                    </div>
                </div>
            </div>
        </div>






        <script>
            document.addEventListener('DOMContentLoaded', function() {
                const emailModal = document.getElementById('email-modal');
                const emailForm = document.getElementById('email-form');
                const sendEmailButton = document.getElementById('send-email-button');
                const sendEmailModalButton = document.getElementById('sendEmailButton');
                const recipientCountElement = document.getElementById('recipient-count');
                const filterDescriptionElement = document.getElementById('filter-description');
                let emailModalInstance = null;

                // Handle send email button click
                sendEmailModalButton.addEventListener('click', function() {
                    if (!emailModalInstance) {
                        emailModalInstance = new bootstrap.Modal(emailModal);
                    }

                    // Update recipient count when modal opens
                    updateRecipientInfo();
                    emailModalInstance.show();
                });

                // Handle form submission
                emailForm.addEventListener('submit', function(e) {
                    e.preventDefault();

                    const subject = document.getElementById('emailSubject').value.trim();
                    const message = document.getElementById('emailMessage').value.trim();

                    if (!subject || !message) {
                        showError('Please fill in both subject and message fields.');
                        return;
                    }

                    // Disable button and show loading state
                    sendEmailButton.disabled = true;
                    sendEmailButton.textContent = 'Sending...';

                    // Call Livewire method
                    @this.call('sendBulkEmail', subject, message).then(() => {
                        // Reset form and close modal
                        emailForm.reset();
                        emailModalInstance.hide();
                        clearMessages();
                    }).catch((error) => {
                        showError('Failed to send email. Please try again.');
                    }).finally(() => {
                        // Re-enable button
                        sendEmailButton.disabled = false;
                        sendEmailButton.textContent = 'Send Email';
                    });
                });

                function updateRecipientInfo() {
                    // Update recipient count via Livewire
                    @this.call('getEmailRecipientCount').then((count) => {
                        recipientCountElement.textContent = count;
                    });

                    // Update filter description
                    const search = @this.get('search');
                    const filter = @this.get('filter');
                    let description = '';

                    if (search || filter) {
                        const parts = [];
                        if (search) parts.push(`matching search '${search}'`);
                        if (filter) parts.push(`filtered by ${filter}`);
                        description = `(${parts.join(' and ')})`;
                    } else {
                        description = '(all users)';
                    }

                    filterDescriptionElement.textContent = description;
                }

                function showError(message) {
                    const errorDiv = document.getElementById('emailErrorMessage');
                    errorDiv.textContent = message;
                    errorDiv.classList.remove('d-none');

                    // Hide success message if visible
                    document.getElementById('emailSuccessMessage').classList.add('d-none');
                }

                function clearMessages() {
                    document.getElementById('emailErrorMessage').classList.add('d-none');
                    document.getElementById('emailSuccessMessage').classList.add('d-none');
                }

                // Clear messages when modal is hidden
                emailModal.addEventListener('hidden.bs.modal', function() {
                    clearMessages();
                    emailForm.reset();
                });
            });
        </script>
    </div>
