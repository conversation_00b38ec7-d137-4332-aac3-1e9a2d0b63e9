<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ProgramRegistration extends Model
{
    use HasFactory;
     protected $fillable = ['user_id', 'program_id', 'amount', 'player_id', 'team_id', 'is_paid','pending_amount'];
      protected $table = 'program_registrations';


      public function program()
{
    return $this->belongsTo(Program::class, 'program_id');
}

   public function player()
    {
        return $this->belongsTo(User::class, 'player_id'); // player_id is the foreign key in program_registrations table
    }




}
