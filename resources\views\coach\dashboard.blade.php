@extends('layouts.app')

@section('title', 'dashboard')

@section('content')

    <section class="page_title text-center pt-5">
        <h1 class="text-uppercase lh-1 mb-0">Welcome {{ $user->firstName }} {{ $user->lastName }}</h1>
        <form id="roleSwitchForm">
            <button class="cta mt-4" id="switchRoleToGuardian" data-coach-switch-id={{ $user->id }}>Switch to Guardian
                Mode</button>
        </form>
    </section>
    <section class="sec program-welcome">
        <div class="container">
            <div class="heading align-items-center d-flex flex-column text-center mb-5"><span
                    class="hero-bar d-inline-flex mb-5"></span></div>
            <div class="row player-meta justify-content-center">
                <div class="heading text-center">
                    <h2 class="fs-6 text-uppercase mb-3">Information</h2>
                </div>
                <div class="col-md-6">
                    <div class="player-meta__box d-flex flex-column text-center align-items-center justify-content-center">
                        <p>
                            {{ $user->firstName }} {{ $user->lastName }}<br />

                            {{ $user->email }}
                        </p>
                        <div class="edit d-flex" data-coach-id="{{ $user->id }}" id="editButton"><img
                                src="{{ asset('images/edit-icon.svg') }}" alt="" width="20" height="20"
                                id="editIcon-{{ $user->id }}"
                                data-loader-coach-url="{{ asset('images/ripple-loading.svg') }}" />
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="player-meta__box d-flex flex-column text-center align-items-center justify-content-center">
                        <p>
                            {{ $user->town }}<br />
                            {{ $user->teamName }}
                        </p>
                        <div class="edit d-flex" data-coach-team-id="{{ $user->id }}"><img
                                src="{{ asset('images/edit-icon.svg') }}" alt="" width="20" height="20"
                                id= "editTeam-{{ $user->id }}"
                                data-loader-coach-team-url="{{ asset('images/ripple-loading.svg') }}" />
                        </div>
                    </div>
                </div>
            </div>
            <div class="row cta-row mt-5">
                <div class="col-12 col-sm-6 col-md-4 d-flex justify-content-center align-items-center mb-3 mb-md-0">
                    <button class="cta w-100" id="addNewTeamButton">Add a New Team</button>
                </div>
                <div class="col-12 col-sm-6 col-md-4 d-flex justify-content-center align-items-center mb-3 mb-md-0">
                    <a class="cta w-100 text-center" href="{{ route('coach.manageTeams') }}">Manage your teams</a>
                </div>
                <div class="col-12 col-sm-6 col-md-4 d-flex justify-content-center align-items-center">
                    <button class="cta w-100" data-team-id={{ $team->id }} id="inviteCoachButton">Invite a
                        Coach</button>
                </div>
            </div>


        </div>
    </section>
    <section class="sec partition-hr">
        <div class="container">
            <hr class="border-navy opacity-100 my-0" />
        </div>
    </section>
    <section class="sec program-table">

        <div class="container">
            <div class="heading d-flex">
                <h2 class="text-uppercase fs-6 mb-0 ml-4" style="color:#0b4499">{{ $team->name }}</h2>
                <h2 class="text-uppercase fs-6 mb-0" style="margin-left:20rem !important;">Build Your Roster:</h2>

                <div class="cta-table">
                    <button class="cta" id="manageTeam">Manage your team</button>
                </div>
            </div>

            @if (!is_null($currentProgram))
                <!-- Only show if currentProgram is not null -->
                <div class="table-program table-responsive" style="margin-top:3rem;">
                    <table class="table table-hover" width="100%">
                        <tbody>
                            <tr>
                                <th>First Name</th>
                                <th>Last Name</th>
                                <th>Email</th>
                                <th>Invite Status</th>
                                <th>Balance Due</th>
                            </tr>
                            @foreach ($notifications as $notification)
                                <tr>
                                    <td class="py-4" valign="middle">{{ $notification->player->firstName ?? 'N/A' }}</td>
                                    <td class="py-4" valign="middle">{{ $notification->player->lastName ?? 'N/A' }}</td>
                                    <td class="py-4" valign="middle">{{ $notification->player->email ?? 'N/A' }}</td>
                                    <td class="py-4" valign="middle">{{ $notification->invitation_status }}</td>
                                    <td class="py-4" valign="middle">${{ $notification->balance_due ?? '0.00' }}</td>
                                </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>

                <div class="cta-row text-center mt-4">
                    <a class="cta" id="invitePlayerButton">Invite A Player</a>
                </div>
            @else
                <div class="cta-row text-center mt-4 text-uppercase fs-6 mb-0"
                    style="font: 14px / 1 'Montserrat', sans-serif; color: #0b4499; margin-top:3rem !important;">
                    Join a program to build your roster
                </div>


            @endif
        </div>


        @if ($notifications->count() > 0)
            <div class="mt-4 d-flex justify-content-center">
                {{ $notifications->onEachSide(1)->links('pagination::bootstrap-5') }}
            </div>
        @endif
        <!--search player -->

        <div class="modal fade" id="invitePlayerModal" tabindex="-1" aria-labelledby="invitePlayerLabel" aria-hidden="true"
            data-bs-backdrop="static">
            <div class="modal-dialog modal-dialog-centered" style="max-width: 1160px">
                <div class="modal-content">
                    <div class="modal-body p-5">
                        <span class="modal-close" data-bs-dismiss="modal" id="closeModalButton">&#x2715;</span>
                        <form class="form mb-5" id="invitePlayerForm">
                            <div id="inviteSuccessMessage" class="alert alert-success d-none" role="alert"></div>
                            <div id="inviteErrorMessage" class="alert alert-danger d-none" role="alert"></div>
                            @csrf
                            <div class="row">
                                <div class="mb-4 col-md-4">
                                    <label class="text-uppercase form-label" for="firstName">First Name</label>
                                    <input class="form-control" id="invitePlayerFirstName" type="text" />
                                    <div id="firstNameError" class="text-danger"></div>
                                </div>
                                <div class="mb-4 col-md-4">
                                    <label class="text-uppercase form-label" for="lastName">Last Name</label>
                                    <input class="form-control" id="invitePlayerLastName" type="text" />
                                    <div id="lastNameError" class="text-danger"></div>
                                </div>
                                <div class="mb-4 col-md-4">
                                    <label class="text-uppercase form-label" for="email">Email</label>
                                    <input class="form-control" id="inviteEmail" type="email" />
                                    <div id="emailError" class="text-danger"></div>
                                </div>
                                <div class="col-12 mt-4 text-center">
                                    <input type="hidden" value="{{ @$team->id }}" id="CurrentTeam">
                                    <button class="cta" id="searchButton">Search</button>
                                </div>
                            </div>
                        </form>
                        {{-- <button class="cta" id="inviteGuardianModalButton">Invite a guardian</button> --}}
                        <div class="program-table" id="searchResults">

                        </div>

                        <button id="loadMoreBtn" class="submit-btn" style="display:none">Load More</button>
                        <div class="program-table">
                            <div class="heading text-center mb-5">
                                <h2 class="text-uppercase fs-6 mb-0">Past Players</h2>

                            </div>
                            <div class="table-program table-responsive">
                                <table class="table table-hover" width="100%">
                                    <tbody>
                                        <tr>
                                            <th class="py-0"></th>
                                            <th class="py-0"></th>
                                            <th class="py-0"></th>
                                            <th class="py-0" width="100"></th>
                                        </tr>

                                        @if ($pastPlayersData->isEmpty())
                                            <tr>
                                                <td colspan="4" class="text-center">UNABLE TO FIND PAST PLAYERS</td>
                                            </tr>
                                        @else
                                            @foreach ($pastPlayersData as $player)
                                                <tr>
                                                    <td class="py-4" valign="middle">
                                                        {{ $player['firstName'] }}
                                                    </td>
                                                    <td class="py-4" valign="middle">{{ $player['lastName'] }}</td>
                                                    <td class="py-4" valign="middle">
                                                        @if ($player['invitationStatus'] === 'pending' || $player['invitationStatus'] === 'accepted')
                                                            {{ $player['email'] ?? 'N/A' }}<br>
                                                            {{ $player['guardianEmail'] ?? 'N/A' }}
                                                        @else
                                                            @if ($player['email'] || $player['guardianEmail'])
                                                                @if ($player['email'])
                                                                    <input type="checkbox" name="emails[]"
                                                                        value="{{ $player['email'] }}"
                                                                        id="email-{{ $player['id'] }}">
                                                                    <label
                                                                        for="email-{{ $player['id'] }}">{{ $player['email'] ?? 'N/A' }}</label><br>
                                                                @endif
                                                                @if ($player['guardianEmail'])
                                                                    <input type="checkbox" name="emails[]"
                                                                        value="{{ $player['guardianEmail'] }}"
                                                                        id="guardianEmail-{{ $player['id'] }}">
                                                                    <label
                                                                        for="guardianEmail-{{ $player['id'] }}">{{ $player['guardianEmail'] ?? 'N/A' }}</label>
                                                                @endif
                                                            @else
                                                                N/A
                                                            @endif
                                                        @endif
                                                    </td>
                                                    <td class="py-4" valign="middle">
                                                        @if ($player['invitationStatus'] === 'accepted')
                                                            <p class="text-success">Accepted</p>
                                                        @elseif ($player['invitationStatus'] === 'pending')
                                                            <p class="text-warning">Pending</p>
                                                        @else
                                                            <form data-player-id="{{ $player['id'] }}"
                                                                class="invite-form-past">
                                                                @csrf
                                                                <input type="hidden" name="team_id"
                                                                    value={{ $team->id }}>
                                                                <button type="submit" class="cta hover-dark"
                                                                    id="sendInviteButtonfp-{{ $player['id'] }}">Invite</button>
                                                                <img id="submitLoaderfp-{{ $player['id'] }}"
                                                                    src="{{ asset('images/loading.svg') }}"
                                                                    alt="Loading..."
                                                                    style="display: none; width: 40px; height: 40px; margin-left: 10px;">
                                                            </form>
                                                        @endif
                                                    </td>
                                                </tr>
                                            @endforeach
                                        @endif
                                    </tbody>
                                </table>



                            </div>
                        </div>
                    </div>

                </div>
            </div>
        </div>
    </section>
    <section class="sec partition-hr">
        <div class="container">
            <hr class="border-navy opacity-100 my-0" />
        </div>
    </section>









    <section class="sec program-table pb-5 mb-5">
        <div class="container">
            <div class="heading text-center mb-5">
                <h2 class="text-uppercase fs-6 mb-0">Current/Active Programs:</h2>
            </div>
            <div class="table-program table-responsive">
                <table class="table table-hover" width="100%">
                    <tbody>
                        <tr>
                            <th>Name</th>
                            <th>Sub Program</th>
                            <th>Start Date</th>
                            <th>Day of the Week</th>
                            <th>Individual Price</th>
                            <th width="100">Location</th>
                            {{-- <th width="100"></th> --}}
                        </tr>
                        @forelse ($currentActivePrograms as $teamProgram)
                            <tr>
                                <td class="py-4 text-uppercase" valign="middle">
                                    <a href="{{ route('program.show', ['program' => $teamProgram->slug]) }}">
                                        {{ $teamProgram->name }}
                                    </a>
                                </td>
                                <td class="py-4 text-uppercase" valign="middle">{{ $teamProgram->sub_program }}</td>
                                <td clas="py-4" valign="middle">{{ $teamProgram->start_date }}</td>
                                <td class="py-4 text-uppercase" valign="middle">
                                    @if (is_array($teamProgram->frequency_days))
                                        {{ implode(', ', $teamProgram->frequency_days) }}
                                    @else
                                        {{ $teamProgram->frequency_days }}
                                    @endif
                                </td>
                                <td class="py-4" valign="middle">${{ $teamProgram->cost }}</td>
                                <td class="py-4" valign="middle">{{ $teamProgram->location }}</td>
                            </tr>
                        @empty
                            <tr>
                                <td class="py-4 text-center text-uppercase fs-6 mb-0 cta-row  mt-4" colspan="6">
                                    No Active Programs
                                </td>
                            </tr>
                        @endforelse

                    </tbody>
                </table>
            </div>
        </div>

    </section>

    <section class="sec partition-hr">
        <div class="container">
            <hr class="border-navy opacity-100 my-0" />
        </div>
    </section>










    <section class="sec program-table pb-5 mb-5">
        <div class="container">
            <div class="heading text-center mb-5">
                <h2 class="text-uppercase fs-6 mb-0">past teams and registrations:</h2>
            </div>
            <div class="table-program table-responsive">
                <table class="table table-hover" width="100%">
                    <tbody>
                        <tr>
                            <th>Name</th>
                            <th>Sub Program</th>
                            <th>Start Date</th>
                            <th>Day of the Week</th>
                            <th>Individual Price</th>
                            <th width="100">Location</th>
                            {{-- <th width="100"></th> --}}
                        </tr>
                        @forelse ($pastPrograms as $teamProgram)
                            <tr>
                                <td class="py-4 text-uppercase" valign="middle">
                                    <a
                                        href="{{ route('program.show', ['program' => Crypt::encrypt($teamProgram->program->id)]) }}">
                                        {{ $teamProgram->program->name }}
                                    </a>
                                </td>
                                <td class="py-4 text-uppercase" valign="middle">{{ $teamProgram->program->sub_program }}
                                </td>
                                <td clas="py-4" valign="middle">{{ $teamProgram->program->start_date }}</td>
                                <td class="py-4 text-uppercase" valign="middle">
                                    @if (is_array($teamProgram->program->frequency_days))
                                        {{ implode(', ', $teamProgram->program->frequency_days) }}
                                    @else
                                        {{ $teamProgram->program->frequency_days }}
                                    @endif
                                </td>
                                <td class="py-4" valign="middle">${{ $teamProgram->program->cost }}</td>
                                <td class="py-4" valign="middle">{{ $teamProgram->program->location }}</td>
                            </tr>
                        @empty
                            <tr>
                                <td class="py-4 text-center text-uppercase fs-6 mb-0 cta-row  mt-4" colspan="6">
                                    No Programs Joined in past
                                </td>
                            </tr>
                        @endforelse

                    </tbody>
                </table>
            </div>
        </div>

    </section>

    <!--coach edit modal -->

    <div class="modal fade" id="coachEdit" tabindex="-1" aria-labelledby="coachEditLabel" aria-hidden="true"
        data-bs-backdrop="static">
        <div class="modal-dialog modal-dialog-centered" style="max-width:640px;">
            <div class="modal-content">
                <div class="modal-body p-5">
                    <span class="modal-close" data-bs-dismiss="modal">&#x2715;</span>

                    <form id="editCoachData" class="form row">

                        @csrf
                        <input type="hidden" name="coach_id" id="coachIdInput">
                        <div id="editCoachSuccessMessage" class="alert alert-success d-none" role="alert"></div>
                        <div id="editCoachErrorMessage" class="alert alert-danger d-none" role="alert"></div>
                        <div class="mb-4 col-md-12">
                            <label class="form-label text-uppercase" for="addFirstName">First Name</label>
                            <input class="form-control" id="editCoachFirstName" type="text" name="firstName" />
                            <div class="invalid-feedback" id="addFirstNameError"></div>
                        </div>
                        <div class="mb-4 col-md-12">
                            <label class="form-label text-uppercase" for="addLastName">Last Name</label>
                            <input class="form-control" id="editCoachLastName" type="text" name="lastName" />
                            <div class="invalid-feedback" id="addLastNameError"></div>
                        </div>
                        <div class="mb-4 col-md-12">
                            <label class="form-label text-uppercase" for="editEmail">Email</label>
                            <input class="form-control" id="editCoachEmail" type="text" name="email" />
                            <div class="invalid-feedback" id="addEmailError"></div>
                        </div>
                        <div class="col-md-12 d-flex align-items-center justify-content-end" style="min-height: 40px">
                            <button class="cta" id="editCoachButton">Save Changes</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>


    <!--team or town edit -->


    <div class="modal fade" id="teamEdit" tabindex="-1" aria-labelledby="teamEditLabel" aria-hidden="true"
        data-bs-backdrop="static">
        <div class="modal-dialog modal-dialog-centered" style="max-width:640px;">
            <div class="modal-content">
                <div class="modal-body p-5">
                    <span class="modal-close" data-bs-dismiss="modal">&#x2715;</span>
                    <form class="form row" id="editCoachTeamData">
                        @csrf
                        <input type="hidden" name="user_id" id="coachTeamIdInput">
                        <div id="editCoachTeamSuccessMessage" class="alert alert-success d-none" role="alert">
                        </div>
                        <div id="editCoachTeamErrorMessage" class="alert alert-danger d-none" role="alert"></div>
                        <div class="mb-4 col-md-12">
                            <label class="form-label text-uppercase" for="addFirstName">Team Name</label>
                            <input class="form-control" id="editCoachTeam" type="text" name="team_name" />
                            <div class="invalid-feedback" id="addTeamError"></div>
                        </div>
                        <div class="mb-4 col-md-12">
                            <label class="form-label text-uppercase" for="addLastName"> Town/Program Name</label>
                            <input class="form-control" id="editCoachTown" type="text" name="town_name" />
                            <div class="invalid-feedback" id="addProgramError"></div>
                        </div>
                        <div class="col-md-12 d-flex align-items-center justify-content-end" style="min-height: 40px">
                            <button class="cta" id="editCoachTeamButton">Save Changes</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Payment Modal -->
    <div class="modal fade" id="paymentModal" tabindex="-1" aria-labelledby="paymentModalLabel" aria-hidden="true"
        data-bs-backdrop="static">
        <div class="modal-dialog modal-dialog-centered" style="max-width:640px;">
            <div class="modal-content">
                <div class="modal-body p-5 form">
                    <span class="modal-close" data-bs-dismiss="modal">&#x2715;</span>
                    <div id="paymentMessage" class="alert d-none" role="alert"></div>
                    <div class="mb-4">
                        <label class="form-label text-uppercase" for="paymentAmount">Enter Payment Amount</label>
                        <input type="number" class="form-control" id="paymentAmount" required>
                        <div class="invalid-feedback" id="paymentAmountError"></div>
                    </div>
                    <div class="d-flex align-items-center justify-content-end" style="min-height: 40px">
                        <button type="button" class="cta" id="submitPaymentButton">Submit Payment</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- adding new Team modal -->

    <div class="modal fade" id="teamModal" tabindex="-1" aria-labelledby="teamModalLabel" aria-hidden="true"
        data-bs-backdrop="static">
        <div class="modal-dialog modal-dialog-centered" style="max-width:640px;">
            <div class="modal-content">
                <div class="modal-body p-5 form">
                    <span class="modal-close" data-bs-dismiss="modal">&#x2715;</span>
                    <div id="teamMessage" class="alert d-none" role="alert"></div>
                    <div class="mb-4">
                        <label class="form-label text-uppercase" for="newTeamName">Enter Team Name</label>
                        <input type="text" class="form-control" id="newTeamName" name="newTeamName" required>
                        <div class="invalid-feedback" id="teamNameError"></div>
                    </div>
                    <div class="d-flex align-items-center justify-content-end" style="min-height: 40px">
                        <button type="button" class="cta" id="createTeamButton">Create New Team</button>
                    </div>
                </div>
            </div>
        </div>
    </div>


    <!--manage team -->

    <div class="modal fade" id="teamSelectionModal" tabindex="-1" aria-labelledby="teamSelectionModalLabel"
        aria-hidden="true" data-bs-backdrop="static">
        <div class="modal-dialog modal-dialog-centered" style="max-width:640px;">
            <div class="modal-content">
                <div class="modal-body p-5 form">
                    <div class="col-md-12 mb-4">
                        <h5 class="text-uppercase text-center" id="headingChange"style="color:#062E69">Select The team
                        </h5>
                    </div>
                    <span class="modal-close" data-bs-dismiss="modal">&#x2715;</span>
                    <div id="teamSelectionMessage" class="alert d-none" role="alert"></div>
                    <div id="teamListContainer" class="mb-4">
                        <!-- Teams will be loaded here dynamically -->
                    </div>
                    <div class="d-flex align-items-center justify-content-end" style="min-height: 40px">
                        <button type="button" class="cta" id="manageSelectedTeamButton">Manage Selected
                            Team</button>
                    </div>
                </div>
            </div>
        </div>
    </div>


    <!-- search and invite a coach -->
    <section>
        <div class="modal fade" id="inviteCoachModal" tabindex="-1" aria-labelledby="inviteCoachLabel"
            aria-hidden="true" data-bs-backdrop="static">
            <div class="modal-dialog modal-dialog-centered" style="max-width: 1160px">
                <div class="modal-content">
                    <div class="modal-body p-5">
                        <span class="modal-close" data-bs-dismiss="modal">&#x2715;</span>
                        <form class="form mb-5" id="invitePlayerForm">
                            <div id="inviteCoachSuccessMessage" class="alert alert-success d-none" role="alert"></div>
                            <div id="inviteCoachErrorMessage" class="alert alert-danger d-none" role="alert"></div>
                            @csrf
                            <div class="row">
                                <div class="mb-4 col-md-3">
                                    <label class="text-uppercase form-label" for="firstName">First Name</label>
                                    <input class="form-control" id="inviteCoachFirstName" type="text" />
                                    <div id="coachFirstNameError" class="text-danger"></div>
                                </div>
                                <div class="mb-4 col-md-3">
                                    <label class="text-uppercase form-label" for="lastName">Last Name</label>
                                    <input class="form-control" id="inviteCoachLastName" type="text" />
                                    <div id="coachLastNameError" class="text-danger"></div>
                                </div>
                                <div class="mb-4 col-md-3">
                                    <label class="text-uppercase form-label" for="email">Email</label>
                                    <input class="form-control" id="inviteCoachEmail" type="email" />
                                    <div id="coachEmailError" class="text-danger"></div>
                                </div>
                                <div class="mb-4 col-md-3">
                                    <label class="text-uppercase form-label" for="town">Town</label>
                                    <input class="form-control" id="inviteCoachTown" type="text" />
                                    <div id="coachTownError" class="text-danger"></div>
                                </div>
                            </div>
                            <div class="col-12 text-center mt-4">
                                <input type="hidden" value="{{ @$team->id }}" id="CurrentCoachTeam">
                                @if ($currentProgram && $currentProgram->program)
                                    <input type="hidden" value="{{ $currentProgram->program->id }}"
                                        class="currentProgram" name="program_id">
                                @endif


                                <button class="cta" id="searchCoachButton"> <span id="searchButtonText">
                                        Search</span>
                                    <span id="loaderForSearch">
                                        <img id="loaderForSearchCoach" src="{{ asset('images/loader.svg') }}"
                                            alt="Loading..."
                                            style="display: none; width: 40px; height: 40px; margin-left: 10px;"></span></button>
                            </div>
                        </form>

                        <div class="program-table" id="searchResultsForCoach">

                        </div>

                        <button id="loadMoreBtnForCoach" class="submit-btn" style="display:none">Load More</button>


                        <!-- Modal body content for past coaches table -->
                        {{-- <div class="program-table">
                            <div class="heading text-center mb-5">
                                <h2 class="text-uppercase fs-6 mb-0">Past Coaches</h2>
                            </div>
                            <div class="table-program table-responsive">
                                <table class="table table-hover" width="100%">
                                    <thead>
                                        <tr>
                                            <th class="py-0"></th>
                                            <th class="py-0"></th>
                                            <th class="py-0"></th>
                                            <th class="py-0"></th>
                                        </tr>
                                    </thead>
                                    <tbody id="pastCoachesTableBody">
                                        <!-- Table rows will be inserted here -->
                                    </tbody>
                                </table>
                            </div>
                        </div> --}}
                    </div>
                </div>
            </div>
        </div>



        <!--invite a guardian -->


        <div class="modal fade" id="guardianInviteModal" tabindex="-1" aria-labelledby="guardianInviteModalLabel"
            aria-hidden="true" data-bs-backdrop="static">
            <div class="modal-dialog modal-dialog-centered" style="max-width:640px;">
                <div class="modal-content">
                    <div class="modal-body p-5 form">
                        <span class="modal-close" data-bs-dismiss="modal">&#x2715;</span>
                        <div class="alert alert-success d-none" role="alert" id="guardianInviteSuccess">

                        </div>
                        <div class="alert alert-danger d-none" role="alert" id="guardianInviteError">

                        </div>
                        <div class="alert alert-warning d-none" role="alert" id="guardianInviteMissing">

                        </div>
                        <form id="inviteGuardianForm">
                            @csrf
                            <div class="mb-4">
                                <label class="form-label text-uppercase" for="guardianInviteEmail">Guardian/Parent
                                    email</label>
                                <input type="email" class="form-control" id="guardianInviteEmail"
                                    name="guardianInviteEmail" required>
                                <div class="invalid-feedback" id="guardianInviteEmailError"></div>
                            </div>
                            <div class="mb-4">
                                <label class="form-label text-uppercase" for="guardianInviteName">Guardian/Parent
                                    Name</label>
                                <input type="text" class="form-control" id="guardianInviteName"
                                    name="guardianInviteName" required>
                                <div class="invalid-feedback" id="guardianInviteNameError"></div>
                            </div>

                            <div class="mb-4">
                                <label class="form-label text-uppercase" for="guardianInviteChildName">Player
                                    Name</label>
                                <input type="text" class="form-control" id="guardianInvitePlayerName"
                                    name="guardianInvitePlayerName" required>
                                <div class="invalid-feedback" id="guardianInvitePlayerNameError"></div>
                            </div>
                            <div class="d-flex align-items-center justify-content-end" style="min-height: 40px">
                                <input type="hidden" value="{{ @$team->id }}" id="currentTeamForInvitation"
                                    name="currentTeamForInvitation">
                                <button type="button" class="cta" id="inviteGuardianButton"><span
                                        id="inviteGuardianButtonText">Send Invite</span>
                                    <span id="loaderForInviteGuardian">
                                        <img id="loaderForInviteGuardianImage" src="{{ asset('images/loader.svg') }}"
                                            alt="Loading..."
                                            style="display: none; width: 40px; height: 40px; margin-left: 10px;"></span></button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>




    </section>


    <script>
        const loaderImageUrl = "{{ asset('images/loading.svg') }}";

        const csrfToken = "{{ csrf_token() }}";
    </script>

    @yield('js')

    <script>
        document.addEventListener('DOMContentLoaded', function() {



            const switchRoleButtonElement = document.getElementById('switchRoleToGuardian');
            const coachSwitchId = switchRoleButtonElement.getAttribute('data-coach-switch-id');

            switchRoleButtonElement.addEventListener('click', function(event) {
                // Prevent default form submission
                event.preventDefault();

                const url = route('coach.switchRoleToGuardian', {
                    coach: coachSwitchId
                });

                fetch(url, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'X-CSRF-TOKEN': "{{ csrf_token() }}",
                            'X-Requested-With': 'XMLHttpRequest'
                        },
                        body: JSON.stringify({
                            coachId: coachSwitchId
                        })
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {

                            window.location.href = data.redirectUrl;
                        } else {

                            alert(data.message || 'Failed to switch role.');
                        }
                    })
                    .catch(error => {
                        console.error('Error:', error);
                    });
            });


            const inviteGuardianModalButton = document.getElementById('inviteGuardianModalButton');

            if (inviteGuardianModalButton) {

                inviteGuardianModalButton.addEventListener('click', function() {
                    clearErrors();

                    const previousModalElement = document.getElementById("invitePlayerModal");
                    if (previousModalElement) {
                        const previousModal = bootstrap.Modal.getInstance(previousModalElement);
                        if (previousModal) {
                            previousModal.hide();
                        }
                    }
                    const modalElement = document.getElementById("guardianInviteModal");
                    const modal = bootstrap.Modal.getOrCreateInstance(modalElement);
                    modalElement.addEventListener('shown.bs.modal', function() {
                        clearErrors();
                        const guardianInviteEmailInput = document.getElementById(
                            'guardianInviteEmail');

                        if (guardianInviteEmailInput) {
                            guardianInviteEmailInput.focus();
                        }
                    });
                    modal.show();
                });
            }

            function clearErrors() {
                const inputs = ['guardianInviteEmail', 'guardianInviteName', 'guardianInvitePlayerName'];

                inputs.forEach(id => {
                    const inputElement = document.getElementById(id);
                    inputElement.classList.remove('is-invalid');
                    const errorElement = document.getElementById(`${id}Error`);
                    if (errorElement) errorElement.textContent = '';
                });

                document.getElementById('guardianInviteMissing').classList.add('d-none');
                document.getElementById('guardianInviteSuccess').classList.add('d-none');
                document.getElementById('guardianInviteError').classList.add('d-none');

            }


            const inviteGuardianButton = document.getElementById('inviteGuardianButton');
            inviteGuardianButton.addEventListener('click', function() {

                const guardianEmail = document.getElementById('guardianInviteEmail').value;
                const guardianName = document.getElementById('guardianInviteName').value;
                const playerName = document.getElementById('guardianInvitePlayerName').value;


                let hasError = false;


                const emailInput = document.getElementById('guardianInviteEmail');
                const nameInput = document.getElementById('guardianInviteName');
                const playerInput = document.getElementById('guardianInvitePlayerName');


                emailInput.classList.remove('is-invalid');
                nameInput.classList.remove('is-invalid');
                playerInput.classList.remove('is-invalid');
                document.getElementById('guardianInviteEmailError').textContent = '';
                document.getElementById('guardianInviteNameError').textContent = '';
                document.getElementById('guardianInvitePlayerNameError').textContent = '';


                const alertMessage = document.getElementById('guardianInviteMissing');
                alertMessage.classList.add('d-none');

                if (!guardianEmail) {
                    emailInput.classList.add('is-invalid');
                    document.getElementById('guardianInviteEmailError').textContent = 'Email is required.';
                    hasError = true;

                }
                if (!guardianName) {
                    nameInput.classList.add('is-invalid');
                    document.getElementById('guardianInviteNameError').textContent = 'Name is required.';
                    hasError = true;

                }
                if (!playerName) {
                    playerInput.classList.add('is-invalid');
                    document.getElementById('guardianInvitePlayerNameError').textContent =
                        'Player name is required.';
                    hasError = true;
                }


                if (!guardianEmail || !guardianName || !playerName) {
                    const alertMessage = document.getElementById('guardianInviteMissing');
                    alertMessage.classList.remove('d-none');
                    alertMessage.textContent = "Please fill all the details";

                    setTimeout(() => {
                        alertMessage.classList.add('d-none');
                    }, 3000);

                    return;

                }

                showLoader();

                const url = route('coach.sendInviteMailToGuardian');

                const teamId = parseInt(document.getElementById('currentTeamForInvitation').value, 10);

                const formData = {
                    guardian_email: guardianEmail,
                    guardian_name: guardianName,
                    player_name: playerName,
                    team_id: teamId,
                };

                fetch(url, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'X-CSRF-TOKEN': "{{ csrf_token() }}",
                            "X-Requested-With": "XMLHttpRequest",
                        },
                        body: JSON.stringify(formData),
                    })
                    .then(response => {

                        return response.json().then(data => ({
                            status: response.status,
                            ok: response.ok,
                            body: data
                        }));
                    })
                    .then(({
                        status,
                        ok,
                        body
                    }) => {
                        if (ok && body.success) {
                            hideLoader();

                            const successMessage = document.getElementById('guardianInviteSuccess');
                            successMessage.classList.remove('d-none');
                            successMessage.textContent = "Guardian Invited Successfully.";
                            document.getElementById('inviteGuardianForm').reset();
                            setTimeout(() => {
                                successMessage.classList.add('d-none');
                            }, 3000);
                        } else {
                            hideLoader();

                            const errorElement = document.getElementById('guardianInviteError');
                            if (errorElement) {
                                errorElement.classList.remove('d-none');

                                if (body.errors) {
                                    const firstErrorKey = Object.keys(body.errors)[0];
                                    const firstErrorMessage = body.errors[firstErrorKey][0];
                                    errorElement.textContent = firstErrorMessage;
                                } else if (body.message) {
                                    errorElement.textContent = body.message;
                                } else {
                                    errorElement.textContent = "An unexpected error occurred.";
                                }
                                setTimeout(() => {
                                    errorElement.classList.add('d-none');
                                }, 3000);
                            }
                        }
                    })
                    .catch(error => {
                        console.error('Error:', error);
                        hideLoader();

                        const errorElement = document.getElementById('guardianInviteError');
                        if (errorElement) {
                            errorElement.classList.remove('d-none');
                            errorElement.textContent = "A network error occurred. Please try again.";
                            setTimeout(() => {
                                errorElement.classList.add('d-none');
                            }, 3000);
                        }
                    });


            });


            function showLoader() {
                document.getElementById('inviteGuardianButtonText').style.display = 'none';
                document.getElementById('loaderForInviteGuardianImage').style.display = "inline-block";
                const button = document.getElementById('inviteGuardianButton');
                button.classList.add('buttonLoader');

            }

            function hideLoader() {
                document.getElementById('inviteGuardianButtonText').style.display = 'inline';
                document.getElementById('loaderForInviteGuardianImage').style.display = "none";
                const button = document.getElementById('inviteGuardianButton');
                button.classList.remove('buttonLoader');
            }


            attachInviteFormListeners();

            document.getElementById('manageTeam').addEventListener('click', function() {
                let url = route('coach.teams')
                fetch(url)
                    .then(response => response.json())
                    .then(data => {
                        if (data.success && Array.isArray(data.data)) {
                            const teams = data.data;

                            const teamListContainer = document.getElementById('teamListContainer');
                            teamListContainer.innerHTML = '';
                            teams.forEach(team => {
                                const radioButton = document.createElement('div');
                                radioButton.innerHTML = `
                    <input type="radio" id="team-${team.id}" name="team" value="${team.id}">
                    <label for="team-${team.id}">${team.name}</label>
                `;
                                teamListContainer.appendChild(radioButton);
                            });
                            new bootstrap.Modal(document.getElementById('teamSelectionModal')).show();
                        } else {
                            console.error('Failed to load teams.');
                        }
                    });
            });

            document.getElementById('manageSelectedTeamButton').addEventListener('click', function() {
                let selectedTeamId = parseInt(document.querySelector('input[name="team"]:checked').value,
                    10);




                if (selectedTeamId) {
                    let url = route('coach.manageTeam')
                    fetch(url, {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')
                                    .getAttribute('content')
                            },
                            body: JSON.stringify({
                                teamId: selectedTeamId
                            })
                        })
                        .then(response => response.json())
                        .then(data => {
                            if (data.success) {
                                bootstrap.Modal.getInstance(document.getElementById(
                                    'teamSelectionModal')).hide();
                                window.location.href = data.redirectUrl;

                            } else {
                                console.error('Failed to manage team:', data.message);
                                document.getElementById('teamSelectionMessage').textContent = data
                                    .message;
                                document.getElementById('teamSelectionMessage').classList.remove(
                                    'd-none');
                            }
                        });
                } else {
                    document.getElementById('teamSelectionMessage').textContent =
                        'Please select a team to manage.';
                    document.getElementById('teamSelectionMessage').classList.remove('d-none');
                }
            });




            const addNewTeamButton = document.getElementById('addNewTeamButton');

            addNewTeamButton.addEventListener('click', function() {
                const teamModal = new bootstrap.Modal(document.getElementById('teamModal'));
                const newTeamNameInput = document.getElementById('newTeamName');
                newTeamNameInput.value = "";
                const teamMessage = document.getElementById('teamMessage');
                teamMessage.classList.add('d-none');
                teamMessage.textContent = '';
                teamModal.show();

            });


            const createTeamButton = document.getElementById('createTeamButton');

            createTeamButton.addEventListener('click', function() {
                const newTeamNameInput = document.getElementById('newTeamName');
                const newTeamName = newTeamNameInput.value;
                const teamMessage = document.getElementById('teamMessage');
                const teamNameError = document.getElementById('teamNameError');
                teamMessage.classList.add('d-none');
                teamMessage.textContent = '';
                teamNameError.textContent = '';


                if (!newTeamName.trim()) {
                    teamNameError.textContent = 'Team name is required.';
                    return;
                }


                const url = route('coach.createNewTeam');


                fetch(url, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')
                                .getAttribute('content')
                        },
                        body: JSON.stringify({
                            newTeamName
                        })
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            teamMessage.classList.remove('d-none', 'alert-danger');
                            teamMessage.classList.add('alert-success');
                            teamMessage.textContent = data.message;
                            newTeamNameInput.value = '';
                            setTimeout(() => {
                                teamMessage.classList.add('d-none');
                            }, 3000);

                        } else {
                            teamMessage.classList.remove('d-none');
                            teamMessage.classList.add('alert-danger');
                            teamMessage.textContent = data.message;
                            setTimeout(() => {
                                teamMessage.classList.add('d-none');
                            }, 3000);
                        }

                    })
                    .catch(error => {
                        teamMessage.classList.remove('d-none', 'alert-success');
                        teamMessage.classList.add('alert-danger');
                        teamMessage.textContent =
                            'An error occurred while creating the team. Please try again later.';
                        console.error('Error:', error);
                        setTimeout(() => {
                            teamMessage.classList.add('d-none');
                        }, 3000);
                    });
            });






            const form = document.getElementById('editCoachTeamData');

            document.querySelectorAll(".player-meta .edit[data-coach-team-id]").forEach((item) => {
                item.addEventListener("click", function() {
                    const coachId = this.getAttribute("data-coach-team-id");
                    let url = route("coach.team.edit", {
                        id: coachId
                    });

                    const editButton = document.getElementById('editButton');
                    let editIcon = document.getElementById(`editTeam-${coachId}`);
                    let loaderUrl = editIcon.getAttribute("data-loader-coach-team-url");

                    const originalIconSrc = editIcon.src;
                    editIcon.src = loaderUrl;

                    fetch(url)
                        .then((response) => response.json())
                        .then((data) => {
                            if (data.success) {
                                editIcon.src = originalIconSrc;
                                document.getElementById("coachTeamIdInput").value = data.coach
                                    .id;
                                document.getElementById("editCoachTeam").value = data.coach
                                    .teamName;
                                document.getElementById("editCoachTown").value = data.coach
                                    .town;
                                const modal = new bootstrap.Modal(document.getElementById(
                                    "teamEdit"));
                                modal.show();
                            } else {
                                console.error("Failed to load coach data.");
                                editIcon.src = originalIconSrc;
                            }
                        });
                });
            });



            document.getElementById("editCoachTeamButton").addEventListener("click", function(event) {
                event.preventDefault();

                const form = document.getElementById('editCoachTeamData');
                const formData = new FormData(form);

                let url = route("coach.team.update");

                fetch(url, {
                        method: "POST",
                        headers: {
                            "X-CSRF-TOKEN": "{{ csrf_token() }}",
                            "X-Requested-With": "XMLHttpRequest",
                        },
                        body: formData,
                    })
                    .then((response) => response.json())
                    .then((data) => {
                        if (data.success) {
                            document.getElementById("editCoachTeamSuccessMessage").textContent =
                                "Coach updated successfully!";
                            document.getElementById("editCoachTeamSuccessMessage").classList.remove(
                                "d-none");
                            document.getElementById("editCoachTeam").value = "";

                            document.getElementById("editCoachTown").value = "";



                            setTimeout(() => {
                                document.getElementById("editCoachTeamSuccessMessage").classList
                                    .add("d-none");
                                const modal = bootstrap.Modal.getInstance(document
                                    .getElementById("teamEdit"));
                                modal.hide();
                                window.location.reload();
                            }, 2000);
                        } else {
                            document.getElementById("editCoachTeamErrorMessage").textContent =
                                "Failed to update Coach.";
                            document.getElementById("editCoachTeamErrorMessage").classList.remove(
                                "d-none");
                        }
                    });
            });


            document.querySelectorAll(".player-meta .edit[data-coach-id]").forEach((item) => {
                item.addEventListener("click", function() {
                    const coachId = this.getAttribute("data-coach-id");
                    let url = route("coach.coach.edit", {
                        id: coachId
                    });

                    const editButton = document.getElementById('editButton');
                    let editIcon = document.getElementById(`editIcon-${coachId}`);
                    let loaderUrl = editIcon.getAttribute("data-loader-coach-url");

                    const originalIconSrc = editIcon.src;
                    editIcon.src = loaderUrl;

                    fetch(url)
                        .then((response) => response.json())
                        .then((data) => {
                            if (data.success) {
                                editIcon.src = originalIconSrc;
                                document.getElementById("coachIdInput").value = data.coach.id;
                                document.getElementById("editCoachFirstName").value = data.coach
                                    .firstName;
                                document.getElementById("editCoachLastName").value = data.coach
                                    .lastName;
                                document.getElementById("editCoachEmail").value = data.coach
                                    .email;
                                const modal = new bootstrap.Modal(document.getElementById(
                                    "coachEdit"));
                                modal.show();
                            } else {
                                console.error("Failed to load coach data.");
                                editIcon.src = originalIconSrc;
                            }
                        });
                });
            });

            document.getElementById("editCoachButton").addEventListener("click", function(event) {
                event.preventDefault();
                const formData = new FormData(document.getElementById("editCoachData"));

                let url = route("coach.coach.update");

                fetch(url, {
                        method: "POST",
                        headers: {
                            "X-CSRF-TOKEN": "{{ csrf_token() }}",
                            "X-Requested-With": "XMLHttpRequest",
                        },
                        body: formData,
                    })
                    .then((response) => response.json())
                    .then((data) => {
                        if (data.success) {
                            document.getElementById("editCoachSuccessMessage").textContent =
                                "Coach updated successfully!";
                            document.getElementById("editCoachSuccessMessage").classList.remove(
                                "d-none");
                            document.getElementById("editCoachFirstName").value = "";

                            document.getElementById("editCoachLastName").value = "";

                            document.getElementById("editCoachEmail").value = "";

                            setTimeout(() => {
                                document.getElementById("editCoachSuccessMessage").classList
                                    .add("d-none");
                                const modal = bootstrap.Modal.getInstance(document
                                    .getElementById("coachEdit"));
                                modal.hide();
                                window.location.reload();
                            }, 2000);
                        } else {
                            document.getElementById("editCoachErrorMessage").textContent =
                                "Failed to update Coach.";
                            document.getElementById("editCoachErrorMessage").classList.remove("d-none");
                        }
                    });
            });

            const invitePlayerButton = document.getElementById('invitePlayerButton');

            if (invitePlayerButton) {



                document.getElementById('invitePlayerButton').addEventListener('click', function() {
                    const modal = new bootstrap.Modal(document.getElementById('invitePlayerModal'));

                    const firstName = document.getElementById('invitePlayerFirstName').value = '';
                    const lastName = document.getElementById('invitePlayerLastName').value = '';
                    const email = document.getElementById('inviteEmail').value = '';


                    const firstNameError = document.getElementById('firstNameError');
                    const lastNameError = document.getElementById('lastNameError');
                    const emailError = document.getElementById('emailError');
                    const resultsContainer = document.getElementById('searchResults');
                    const loadMoreButtonElement = document.getElementById('loadMorebtn');

                    if (loadMoreButtonElement) {
                        loadMoreButtonElement.style.display = "none";
                    }

                    firstNameError.textContent = '';
                    lastNameError.textContent = '';
                    emailError.textContent = '';
                    resultsContainer.innerHTML = "";


                    modal.show();
                });
            }

            document.getElementById('searchButton').addEventListener('click', function(event) {
                event.preventDefault();

                const firstName = document.getElementById('invitePlayerFirstName').value.trim();
                const lastName = document.getElementById('invitePlayerLastName').value.trim();
                const email = document.getElementById('inviteEmail').value.trim();

                const firstNameError = document.getElementById('firstNameError');
                const lastNameError = document.getElementById('lastNameError');
                const emailError = document.getElementById('emailError');
                firstNameError.textContent = '';
                lastNameError.textContent = '';
                emailError.textContent = '';

                if (!firstName && !lastName && !email) {
                    firstNameError.textContent = 'Please enter a first name or';
                    lastNameError.textContent = 'last name or';
                    emailError.textContent = 'email to search.';
                    return;
                }

                const loadMoreBtn = document.getElementById('loadMoreBtn');
                const baseUrl = route('coach.searchPlayer');
                const url = new URL(baseUrl, window.location.origin);
                url.searchParams.set('page', 1);

                if (firstName) url.searchParams.append('firstName', firstName);
                if (lastName) url.searchParams.append('lastName', lastName);
                if (email) url.searchParams.append('email', email);


                let selectedTeamId = document.getElementById('CurrentTeam').value;
                if (selectedTeamId) {
                    url.searchParams.append('team', selectedTeamId);

                }
                fetchPlayers(url, 1, loadMoreBtn);
            });

            function fetchPlayers(url, currentPage, loadMoreBtn) {
                fetch(url, {
                        method: 'GET',
                        headers: {
                            'X-Requested-With': 'XMLHttpRequest',
                        },
                    })
                    .then(response => response.json())
                    .then(data => {
                        const resultsContainer = document.getElementById('searchResults');

                        if (currentPage === 1) {
                            resultsContainer.innerHTML = `
                    <div class="program-table">
                        <div class="table-program table-responsive mb-5">
                            <table class="table table-hover" width="100%">
                                <tbody>
                                    <tr>
                                        <th class="py-0"></th>
                                        <th class="py-0"></th>
                                        <th class="py-0"></th>
                                        <th class="py-0" width="100"></th>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>`;
                        }

                        const tableBody = resultsContainer.querySelector('tbody');
                        const loadMoreBtn = document.getElementById('loadMoreBtn');

                        if (data.players.length === 0 && currentPage === 1) {
                            resultsContainer.innerHTML =
                                '<p class="text-center text-gray-600">No players found.</p>';
                            loadMoreBtn.style.display = 'none';
                        } else if (data.players.length === 0) {
                            //do nothing eat fivestar
                        }

                        if (data.success && data.players.length > 0) {
                            const invitationLookup = {};
                            data.playerInvitations.forEach(inv => {
                                invitationLookup[inv.user_id] = inv.invitation_status;
                            });

                            data.players.forEach(player => {
                                let actionContent;
                                const invitationStatus = invitationLookup[player.id];

                                if (invitationStatus === 'accepted' || invitationStatus === 'pending') {
                                    actionContent = invitationStatus === 'pending' ?
                                        '<p class="text-warning">Pending</p>' :
                                        '<p class="text-success">Accepted</p>';
                                } else {
                                    actionContent = `
                <form data-player-id="${player.id}" class="invite-form">
                    <input type="hidden" name="_token" value="${csrfToken}">
                    <input type="hidden" name="team_id" value="${data.team.id}">
                    <button type="submit" class="cta hover-dark" id="sendInviteButton-${player.id}">Invite</button>
                     <img id="submitLoader-${player.id}" src="${loaderImageUrl}" alt="Loading..."
                                style="display: none; width: 40px; height: 40px; margin-left: 10px;">
                </form>
            `;
                                }

                                const emailContent = invitationStatus === 'pending' ||
                                    invitationStatus === 'accepted' ? `
                        ${player.email ? `
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                <div class="email-checkbox-wrapper">
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    <span class="styled-checkbox-placeholder"></span> <!-- Placeholder for checkbox alignment -->
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    <span class="styled-email">${player.email}</span><br>
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                </div>
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            ` : ''}
                        <div class="email-checkbox-wrapper">
                            <span class="styled-checkbox-placeholder"></span> <!-- Placeholder for checkbox alignment -->
                            <span class="styled-email">${player.guardianEmail}</span>
                        </div>
                    ` : `
                        ${player.email ? `
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                <div class="email-checkbox-wrapper">
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    <input type="checkbox" name="emails[]" value="${player.email}" id="email-${player.id}" class="styled-checkbox">
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    <label for="email-${player.id}" class="styled-label">${player.email}</label><br>
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                </div>
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            ` : ''}
                        <div class="email-checkbox-wrapper">
                            <input type="checkbox" name="emails[]" value="${player.guardianEmail}" id="guardianEmail-${player.id}" class="styled-checkbox">
                            <label for="guardianEmail-${player.id}" class="styled-label">${player.guardianEmail}</label>
                        </div>
                    `;


                                const playerRow = `
                                    <tr>
                                        <td class="py-4" valign="middle">${player.firstName}</td>
                                        <td class="py-4" valign="middle">${player.lastName}</td>
                                        <td class="py-4" valign="middle">${emailContent}</td>
                                        <td class="py-4" valign="middle">${actionContent}</td>
                                    </tr>
                                `;




                                tableBody.insertAdjacentHTML('beforeend', playerRow);
                            });
                            if (currentPage >= data.last_page) {
                                loadMoreBtn.style.display = 'none';
                            } else {
                                loadMoreBtn.style.display = 'block';
                            }

                            loadMoreBtn.setAttribute('data-current-page', currentPage);
                            attachInviteFormListeners();

                        } else if (currentPage === 1) {
                            resultsContainer.innerHTML =
                                '<p class="text-center text-gray-600">No players found.</p>';
                            loadMoreBtn.style.display = 'none';
                        }

                    })
                    .catch(error => {
                        console.error('Error:', error);
                        document.getElementById('inviteErrorMessage').classList.remove('d-none');
                        document.getElementById('inviteErrorMessage').textContent =
                            'An error occurred while searching for players.';
                    });
            }

            document.getElementById('loadMoreBtn').addEventListener('click', function() {
                let currentPage = parseInt(this.getAttribute('data-current-page')) || 1;
                const nextPage = currentPage + 1;
                const baseUrl = route('coach.searchPlayer');
                const url = new URL(baseUrl, window.location.origin);
                url.searchParams.set('page', nextPage);
                const firstName = document.getElementById('invitePlayerFirstName').value.trim();
                const lastName = document.getElementById('invitePlayerLastName').value.trim();
                const email = document.getElementById('inviteEmail').value.trim();
                if (firstName) url.searchParams.append('firstName', firstName);
                if (lastName) url.searchParams.append('lastName', lastName);
                if (email) url.searchParams.append('email', email);

                fetchPlayers(url, nextPage, this);
            });



            const resultsContainer = document.getElementById('searchResults');

            resultsContainer.addEventListener('change', function(event) {
                if (event.target.matches('input[type="checkbox"][name="emails[]"]')) {
                    handleCheckboxChange();
                }
            });

            function handleCheckboxChange() {
                const selectedEmails = Array.from(document.querySelectorAll(
                        'input[type="checkbox"][name="emails[]"]:checked'))
                    .map(checkbox => checkbox.value);
            }

            function attachInviteFormListeners() {
                document.querySelectorAll('.invite-form').forEach(form => {
                    form.addEventListener('submit', function(event) {
                        event.preventDefault();

                        const selectedEmails = Array.from(document.querySelectorAll(
                                'input[type="checkbox"][name="emails[]"]:checked'))
                            .map(checkbox => checkbox.value);
                        if (selectedEmails.length === 0) {
                            const errorMessage = document.getElementById('inviteErrorMessage');
                            errorMessage.classList.remove('d-none');
                            errorMessage.textContent =
                                "Please select an email before sending the invite";
                            setTimeout(() => {
                                errorMessage.classList.add('d-none');
                                errorMessage.textContent = "";
                            }, 3000);
                            return;
                        }

                        const formData = new FormData(this);
                        const playerId = this.getAttribute('data-player-id');
                        const actionUrl = route('coach.invite.player', [playerId]);
                        const paymentModalElement = document.getElementById('paymentModal');
                        const paymentModal = new bootstrap.Modal(document.getElementById(
                            'paymentModal'));

                        const errorElement = document.getElementById('paymentAmountError');
                        errorElement.textContent = "";
                        errorElement.style.display = "none";
                        paymentModal.show();
                        paymentModalElement.addEventListener('shown.bs.modal', function() {
                            const paymentAmountInput = document.getElementById(
                                'paymentAmount');
                            if (paymentAmountInput) {
                                paymentAmountInput.focus();
                            }
                        });

                        document.getElementById('submitPaymentButton').onclick = null;

                        const submitPaymentButton = document.getElementById('submitPaymentButton');
                        submitPaymentButton.replaceWith(submitPaymentButton.cloneNode(true));
                        const clonedSubmitPaymentButton = document.getElementById(
                            'submitPaymentButton');
                        clonedSubmitPaymentButton.addEventListener('click', function() {
                            const paymentAmount = document.getElementById('paymentAmount')
                                .value;
                            if (!paymentAmount) {
                                const errorElement = document.getElementById(
                                    'paymentAmountError');
                                errorElement.textContent = "Please enter the amount";
                                errorElement.style.display = "block";
                                return;
                            }
                            formData.append('payment_amount', paymentAmount);
                            formData.append('emails', selectedEmails);

                            // // Show loader
                            const sendInviteButton = document.getElementById(
                                `sendInviteButton-${playerId}`);

                            const loader = document.getElementById(
                                `submitLoader-${playerId}`);

                            if (sendInviteButton) {

                                sendInviteButton.style.display = "none";
                                sendInviteButton.classList.add('d-none');
                                loader.style.display = 'block';

                            }





                            fetch(actionUrl, {
                                    method: 'POST',
                                    body: formData,
                                    headers: {
                                        'X-Requested-With': 'XMLHttpRequest',
                                    },
                                })
                                .then(response => response.json())
                                .then(result => {
                                    if (result.success) {
                                        form.outerHTML =
                                            '<p class="text-warning">Pending</p>';
                                        document.querySelectorAll(
                                                'input[type="checkbox"][name="emails[]"]:checked'
                                            )
                                            .forEach(checkbox => {
                                                checkbox.checked = false;
                                            });

                                    } else {
                                        const errorMessage = document.getElementById(
                                            'inviteErrorMessage');

                                        //Fixing this

                                        errorMessage.classList.remove('d-none');
                                        errorMessage.textContent =
                                            result.message;
                                        setTimeout(() => {
                                            errorMessage.classList.add(
                                                'd-none');
                                            errorMessage.textContent = "";
                                        }, 3000);
                                    }
                                    if (sendInviteButton) {
                                        loader.style.display = 'none';
                                        sendInviteButton.style.display = "block";
                                        sendInviteButton.classList.remove('d-none');
                                    }

                                })
                                .catch(error => {
                                    console.error('Error:', error);
                                    if (sendInviteButton) {
                                        loader.style.display = 'none';
                                        sendInviteButton.style.display = "block";
                                        sendInviteButton.classList.remove('d-none');
                                    }

                                });

                            paymentModal.hide();
                        });
                    });
                });
            }

            document.getElementById('invitePlayerModal').addEventListener('hidden.bs.modal', function() {
                document.getElementById('invitePlayerFirstName').value = '';
                document.getElementById('invitePlayerLastName').value = '';
                document.getElementById('inviteEmail').value = '';
                const resultsContainer = document.getElementById('searchResults');

                if (resultsContainer.innerHTML.trim() !== '') {
                    location.reload();
                }
                resultsContainer.innerHTML = '';
                const loadMoreBtn = document.getElementById('loadMoreBtn');
                if (loadMoreBtn) {
                    loadMoreBtn.style.display = 'none';
                    loadMoreBtn.setAttribute('data-current-page', '1');
                }

            });


            document.querySelectorAll('.invite-form-past').forEach(form => {
                form.addEventListener('submit', function(event) {
                    event.preventDefault();

                    const selectedEmails = Array.from(document.querySelectorAll(
                            'input[type="checkbox"][name="emails[]"]:checked'))
                        .map(checkbox => checkbox.value);
                    if (selectedEmails.length === 0) {
                        const errorMessage = document.getElementById('inviteErrorMessage');

                        errorMessage.classList.remove('d-none');
                        errorMessage.textContent =
                            "Please select an email before sending the invite";
                        setTimeout(() => {
                            errorMessage.classList.add('d-none');
                            errorMessage.textContent = "";
                        }, 3000);
                        return;
                    }

                    const formData = new FormData(this);
                    const playerId = this.getAttribute('data-player-id');
                    const actionUrl = route('coach.invite.player', [playerId]);

                    const paymentModal = new bootstrap.Modal(document.getElementById(
                        'paymentModal'));
                    const errorElement = document.getElementById('paymentAmountError');
                    errorElement.textContent = "";
                    errorElement.style.display = "none";
                    paymentModal.show();

                    document.getElementById('submitPaymentButton').addEventListener('click',
                        function() {
                            const paymentAmount = document.getElementById('paymentAmount')
                                .value;
                            if (!paymentAmount) {
                                const errorElement = document.getElementById(
                                    'paymentAmountError');
                                errorElement.textContent = "Please enter the amount";
                                errorElement.style.display = "block";
                                return;
                            }
                            formData.append('payment_amount', paymentAmount);
                            formData.append('emails', selectedEmails);

                            // // Show loader
                            const sendInviteButton = document.getElementById(
                                `sendInviteButtonfp-${playerId}`);

                            const loader = document.getElementById(
                                `submitLoaderfp-${playerId}`);

                            if (sendInviteButton) {
                                sendInviteButton.style.display = "none";
                                sendInviteButton.classList.add('d-none');
                                loader.style.display = 'block';

                            }

                            fetch(actionUrl, {
                                    method: 'POST',
                                    body: formData,
                                    headers: {
                                        'X-Requested-With': 'XMLHttpRequest',
                                    },
                                })
                                .then(response => response.json())
                                .then(result => {
                                    if (result.success) {
                                        form.outerHTML =
                                            '<p class="text-warning">Pending</p>';

                                    } else {
                                        const errorMessage = document.getElementById(
                                            'inviteErrorMessage');


                                        errorMessage.classList.remove('d-none');
                                        errorMessage.textContent =
                                            result.message;
                                        setTimeout(() => {
                                            errorMessage.classList.add(
                                                'd-none');
                                            errorMessage.textContent = "";
                                        }, 3000);
                                    }
                                    if (sendInviteButton) {
                                        loader.style.display = 'none';
                                        sendInviteButton.style.display = "block";
                                        sendInviteButton.classList.remove('d-none');
                                    }

                                })
                                .catch(error => {
                                    console.error('Error:', error);
                                    if (sendInviteButton) {
                                        loader.style.display = 'none';
                                        sendInviteButton.style.display = "block";
                                        sendInviteButton.classList.remove('d-none');
                                    }

                                });

                            paymentModal.hide();
                        });
                });
            });



            document.getElementById('inviteCoachButton').addEventListener('click', function() {

                const button = document.getElementById('inviteCoachButton');
                team = button.getAttribute('data-team-id');
                const modal = new bootstrap.Modal(document.getElementById('inviteCoachModal'));

                const firstNameError = document.getElementById('coachFirstNameError');
                const lastNameError = document.getElementById('coachLastNameError');
                const emailError = document.getElementById('coachEmailError');
                const townError = document.getElementById('coachTownError');
                const resultsContainer = document.getElementById('pastCoachesTableBody');

                firstNameError.textContent = '';
                lastNameError.textContent = '';
                emailError.textContent = '';
                townError.textContent = '';

                //     const url = route('coach.searchAnotherCoach');
                //     fetch(url, {
                //             method: 'GET',
                //             headers: {
                //                 'X-CSRF-TOKEN': document.querySelector('input[name="_token"]').value,
                //             }
                //         })
                //         .then(response => response.json())
                //         .then(data => {
                //             if (data.success) {
                //                 if (data.pastCoachDetails.length === 0) {
                //                     if (resultsContainer) {
                //                         resultsContainer.innerHTML =
                //                             '<tr><td colspan="5" class="text-center">UNABLE TO FIND PAST COACHES.</td></tr>';
                //                     }
                //                 } else {
                //                     // Generate HTML for past coaches
                //                     const pastCoachesHtml = data.pastCoachDetails.map(pastCoach => {
                //                         const teamOptions = data.teams.map(team =>
                //                             `<option value="${team.id}" ${team.id == pastCoach.team_id ? 'selected' : ''}>${team.name}</option>`
                //                         ).join('');

                //                         if (pastCoachesHtml) {

                //                             return `
            //             <tr>
            //                 <td>${pastCoach.firstName}</td>
            //                 <td>${pastCoach.lastName}</td>
            //                 <td>${pastCoach.email}</td>
            //                 <td>
            //                     <select name="team_id" class="team-select" style="width: 150px; height: 35px; background-color: #f0f0f0; border-radius: 4px; margin-right: 10px;">
            //                         ${teamOptions}
            //                     </select>
            //                 </td>
            //                 <td>
            //                     <form data-coach-id="${pastCoach.coach_id}" class="invite-form-for-coach">
            //                         <input type="hidden" name="_token" value="${document.querySelector('input[name="_token"]').value}">
            //                         <input type="hidden" name="team_id_hidden" class="team-id-hidden" value="${pastCoach.team_id}">
            //                         <button type="submit" class="cta hover-dark" id="sendInviteButton-${pastCoach.coach_id}">Invite</button>
            //                         <img src="${loaderImageUrl}" alt="Loading..." style="display: none; width: 40px; height: 40px; margin-left: 10px;">
            //                     </form>
            //                 </td>
            //             </tr>`;
                //                         }
                //                     }).join('');

                //                     if (pastCoachesHtml) {

                //                         resultsContainer.innerHTML = pastCoachesHtml;
                //                     }

                //                     attachInviteFormListenersForCoach();
                //                 }

                modal.show();
                //             } else {
                //                 console.error('Failed to fetch past coaches:', data.message);
                //             }
                //         })
                //         .catch(error => {
                //             console.error('Error fetching past coaches:', error);
                //         });
                //
            });


            const modalElement = document.getElementById('inviteCoachModal');
            modalElement.addEventListener('show.bs.modal', function() {
                document.getElementById('inviteCoachFirstName').value = '';
                document.getElementById('inviteCoachLastName').value = '';
                document.getElementById('inviteCoachEmail').value = '';
                document.getElementById('inviteCoachTown').value = '';
                document.getElementById('searchResultsForCoach').innerHTML = "";
                document.getElementById('loadMoreBtnForCoach').style.display = "none";
            });




            const coachSearchButton = document.getElementById('searchCoachButton');
            coachSearchButton.addEventListener('click', function(event) {
                event.preventDefault();



                const firstName = document.getElementById('inviteCoachFirstName').value.trim();
                const lastName = document.getElementById('inviteCoachLastName').value.trim();
                const email = document.getElementById('inviteCoachEmail').value.trim();
                const town = document.getElementById('inviteCoachTown').value.trim();

                const firstNameError = document.getElementById('coachFirstNameError');
                const lastNameError = document.getElementById('coachLastNameError');
                const emailError = document.getElementById('coachEmailError');
                const townError = document.getElementById('coachTownError');

                firstNameError.textContent = '';
                lastNameError.textContent = '';
                emailError.textContent = '';
                townError.textContent = '';

                const emailPattern = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;

                if (!firstName && !lastName && !email && !town) {
                    firstNameError.textContent = 'Please enter a first name or';
                    lastNameError.textContent = 'last name or';
                    emailError.textContent = 'email or';
                    townError.textContent = 'town to search';
                    return;
                }

                if (email && !emailPattern.test(email)) {
                    emailError.textContent = 'Please enter a valid email address.';
                    return;
                }

                const button = document.getElementById('inviteCoachButton');
                team = button.getAttribute('data-team-id');

                const loadMoreBtn = document.getElementById('loadMoreBtnForCoach');
                const baseUrl = route('coach.searchAnotherCoach');
                const url = new URL(baseUrl, window.location.origin);
                url.searchParams.set('page', 1);

                if (firstName) url.searchParams.append('firstName', firstName);
                if (lastName) url.searchParams.append('lastName', lastName);
                if (email) url.searchParams.append('email', email);
                if (town) url.searchParams.append('town', town);
                url.searchParams.append('team', team);

                fetchCoaches(url, 1, loadMoreBtn);
                showLoaderForSearch();
            });

            function fetchCoaches(url, currentPage, loadMoreBtn) {
                fetch(url, {
                        method: 'GET',
                        headers: {
                            'X-Requested-With': 'XMLHttpRequest',
                        },
                    })
                    .then(response => response.json())
                    .then(data => {
                        const resultsContainer = document.getElementById('searchResultsForCoach');

                        if (currentPage === 1) {
                            resultsContainer.innerHTML = `
                <div class="program-table">
                    <div class="table-program table-responsive mb-5">
                        <table class="table table-hover" width="100%">
                            <tbody>
                                <tr>
                                    <th class="py-0"></th>
                                    <th class="py-0"></th>
                                    <th class="py-0"></th>
                                    <th class="py-0"></th>
                                    <th class="py-0" width="100"></th>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>`;
                        }

                        const tableBody = resultsContainer.querySelector('tbody');

                        if (data.coaches) {
                            if (data.coaches.length === 0 && currentPage === 1) {
                                hideLoaderForSearch();
                                resultsContainer.innerHTML =
                                    '<p class="text-center text-gray-600">No coaches found.</p>';
                                loadMoreBtn.style.display = 'none';
                            } else if (data.coaches.length === 0) {
                                //no more coaches left
                            }
                        }

                        if (data.coaches) {

                            if (data.success && data.coaches.length > 0) {
                                hideLoaderForSearch();

                                const teams = data
                                    .teams;

                                data.coaches.forEach(coach => {
                                    let teamOptions = teams.map(team => {
                                        const isSelected = coach.team_id === team.id ?
                                            'selected' :
                                            '';
                                        return `<option value="${team.id}" ${isSelected}>${team.name}</option>`;
                                    }).join('');

                                    const teamDropdown = `
                            <select name="team_id" id="teamDropdown-${coach.id}" data-coach-id=${coach.id} class=" custom-select-dropdown team-select" style="width: 11rem; height: 35px; background-color: #0b4499; border: 2px solid #0b4499; border-radius: 1rem; margin-right: 10px;">
                                <option value="">Select Team</option>
                                ${teamOptions}
                            </select>`;

                                    const invitationStatus = coach.invitationStatus;

                                    const actionContent = invitationStatus === 'Not Invited' ? `
                        <form data-coach-id="${coach.id}" class="invite-form-for-coach">
                            <input type="hidden" name="_token" value="${csrfToken}">
                            <input type="hidden" name="team_id_hidden" class="team-id-hidden" value="">
                            <button type="submit" class="cta hover-dark" id="sendInviteButton-${coach.id}">Invite</button>
                        </form>` : `
                        <p id="inviteStatus-${coach.id}" class="text-${invitationStatus === 'Pending' ? 'warning' : 'success'}">${invitationStatus}</p>`;

                                    const coachRow = `
        <tr>
            <td class="py-4" valign="middle">${coach.firstName}</td>
            <td class="py-4" valign="middle">${coach.lastName}</td>
            <td class="py-4" valign="middle">${coach.email}</td>
            <td class="py-4" valign="middle">${teamDropdown}</td>
            <td class="py-4" valign="middle">${actionContent}</td>
        </tr>`;

                                    tableBody.insertAdjacentHTML('beforeend', coachRow);
                                });


                                if (currentPage >= data.last_page) {
                                    loadMoreBtn.style.display = 'none';
                                } else {
                                    loadMoreBtn.style.display = 'block';
                                }

                                loadMoreBtn.setAttribute('data-current-page', currentPage);


                                attachTeamDropdownListeners();


                                attachInviteFormListenersForCoach();


                            }
                        } else if (data.success && data.message.includes(
                                'We were unable to find a coach with that email')) {
                            hideLoaderForSearch();
                            resultsContainer.innerHTML =
                                '<p class="text-center text-gray-900">Unfortunately, we were unable to locate a coach linked to the given email. An invitation has been sent to the provided email address.</p>'
                        } else if (data.success && data.message.includes('User with the mail')) {
                            hideLoaderForSearch();
                            resultsContainer.innerHTML =
                                '<p class="text-center text-gray-900">User with the mail has been already invited by you.</p>'
                        } else if (currentPage === 1) {
                            hideLoaderForSearch();
                            resultsContainer.innerHTML =
                                '<p class="text-center text-gray-600" id="NoCoach">No coaches found.</p>';
                            loadMoreBtn.style.display = 'none';
                        }
                    })
                    .catch(error => {
                        console.error('Error:', error);
                        hideLoaderForSearch();
                        document.getElementById('inviteCoachErrorMessage').classList.remove(
                            'd-none');
                        document.getElementById('inviteCoachErrorMessage').textContent =
                            'An error occurred while searching for coaches.';
                    });
            }

            function showLoaderForSearch() {
                document.getElementById("searchButtonText").style.display = "none";
                document.getElementById("loaderForSearchCoach").style.display =
                    "inline-block";
                const button = document.getElementById("searchCoachButton");
                button.classList.add("buttonLoader");
            }

            function hideLoaderForSearch() {
                document.getElementById("searchButtonText").style.display = "inline";
                document.getElementById("loaderForSearchCoach").style.display =
                    "none";
                const button = document.getElementById("searchCoachButton");
                button.classList.remove("buttonLoader");
            }

            function attachTeamDropdownListeners() {
                document.querySelectorAll('.team-select').forEach(dropdown => {
                    dropdown.addEventListener('change', function() {
                        const coachId = this.getAttribute('data-coach-id');
                        const teamId = this.value;
                        if (teamId) {
                            checkCoachInvitationStatus(coachId, teamId);
                        }
                    });
                });
            }

            function checkCoachInvitationStatus(coachId, teamId) {

                const url = route('coach.checkCoachInvitationStatus', {
                    coachId: coachId,
                    teamId: teamId
                });

                fetch(url, {
                        method: "GET",
                        headers: {
                            "X-CSRF-TOKEN": "{{ csrf_token() }}",
                            "X-Requested-With": "XMLHttpRequest",
                        }
                    })
                    .then(response => response.json())
                    .then(data => {
                        const invitationStatusElement = document.querySelector(
                            `#sendInviteButton-${coachId}`);
                        if (data.invited) {


                            if (data.status === "pending") {
                                const invitationPending = document.getElementById(
                                        `inviteStatus-${coachId}`)
                                    .textContent =
                                    "Pending";
                            } else if (data.status === "accepted") {
                                const invitationPending = document.getElementById(
                                        `inviteStatus-${coachId}`)
                                    .textContent =
                                    "Accepted";
                            }




                            // invitationStatusElement.innerHTML = `<p class="text-alert">Invited</p>`;
                        } else {

                            if (invitationStatusElement) {
                                invitationStatusElement.outerHTML = `
            <form data-coach-id="${coachId}" class="invite-form-for-coach">
                <input type="hidden" name="_token" value="${csrfToken}">
                <input type="hidden" name="team_id_hidden" class="team-id-hidden" value="${teamId}">
                <button type="submit" class="cta hover-dark" id="sendInviteButton-${coachId}">Invite</button>
            </form>`;
                            } else {
                                const invitationPending = document.getElementById(
                                    `inviteStatus-${coachId}`);

                                invitationPending.innerHTML = `
            <form data-coach-id="${coachId}" class="invite-form-for-coach">
                <input type="hidden" name="_token" value="${csrfToken}">
                <input type="hidden" name="team_id_hidden" class="team-id-hidden" value="${teamId}">
                <button type="submit" class="cta hover-dark" id="sendInviteButton-${coachId}">Invite</button>
            </form>`;

                            }
                            attachInviteFormListenersForCoach();
                        }
                    })
                    .catch(error => {
                        console.error('Error:', error);
                        document.getElementById('inviteCoachErrorMessage').classList.remove(
                            'd-none');
                        document.getElementById('inviteCoachErrorMessage').textContent =
                            'An error occurred while checking invitation status.';
                    });
            }

            document.getElementById('loadMoreBtnForCoach').addEventListener('click', function() {
                let currentPage = parseInt(this.getAttribute('data-current-page')) || 1;
                const nextPage = currentPage + 1;
                const baseUrl = route(
                    'coach.searchAnotherCoach'); // Updated to search for coaches
                const url = new URL(baseUrl, window.location.origin);
                url.searchParams.set('page', nextPage);

                const firstName = document.getElementById('inviteCoachFirstName').value.trim();
                const lastName = document.getElementById('inviteCoachLastName').value.trim();
                const email = document.getElementById('inviteCoachEmail').value.trim();
                const town = document.getElementById('inviteCoachTown').value.trim();

                if (firstName) url.searchParams.append('firstName', firstName);
                if (lastName) url.searchParams.append('lastName', lastName);
                if (email) url.searchParams.append('email', email);
                if (town) url.searchParams.append('town', town);

                fetchCoaches(url, nextPage, this);
            });

            function attachInviteFormListenersForCoach() {
                document.querySelectorAll('.invite-form-for-coach').forEach(form => {

                    const coachId = form.getAttribute('data-coach-id');
                    const teamDropdown = document.getElementById(`teamDropdown-${coachId}`);
                    const hiddenTeamInput = form.querySelector('input[name="team_id_hidden"]');

                    if (form.dataset.listenerAttached) return;
                    form.dataset.listenerAttached = true;

                    teamDropdown.addEventListener('change', function() {
                        hiddenTeamInput.value = this.value;
                    });

                    form.addEventListener('submit', function(event) {
                        event.preventDefault();

                        const selectedTeam = hiddenTeamInput.value;
                        const inviteButton = document.getElementById(
                            `sendInviteButton-${coachId}`);
                        const loader = document.createElement('div');
                        loader.classList.add('spinner');
                        loader.id = `loader-${coachId}`;


                        if (!selectedTeam) {
                            const errorMessage = document.getElementById(
                                'inviteCoachErrorMessage');
                            errorMessage.classList.remove('d-none');
                            errorMessage.textContent =
                                "Please select a team before sending the invite";
                            setTimeout(() => {
                                errorMessage.classList.add('d-none');
                                errorMessage.textContent = '';
                            }, 3000);
                            return;
                        }


                        inviteButton.style.display = 'none';
                        inviteButton.parentNode.appendChild(loader);

                        const formData = new FormData(form);
                        const actionUrl = route('coach.invite.coach', [coachId]);

                        fetch(actionUrl, {
                                method: 'POST',
                                body: formData,
                                headers: {
                                    'X-Requested-With': 'XMLHttpRequest',
                                },
                            })
                            .then(response => response.json())
                            .then(result => {
                                if (result.success) {
                                    const successMessage = document.getElementById(
                                        'inviteCoachSuccessMessage');
                                    successMessage.classList.remove('d-none');
                                    successMessage.textContent = result.message;
                                    setTimeout(() => {
                                        successMessage.classList.add(
                                            'd-none');
                                        successMessage.textContent = '';
                                    }, 3000);


                                    form.outerHTML =
                                        `<p id="inviteStatus-${coachId}" class="text-warning">Pending</p>`;
                                } else {
                                    const errorMessage = document.getElementById(
                                        'inviteCoachErrorMessage');
                                    errorMessage.classList.remove('d-none');
                                    errorMessage.textContent = result.message;
                                    setTimeout(() => {
                                        errorMessage.classList.add(
                                            'd-none');
                                        errorMessage.textContent = '';
                                    }, 3000);


                                    inviteButton.style.display = 'block';
                                    loader.remove();
                                }
                            })
                            .catch(error => {
                                console.error('Error:', error);
                                inviteButton.style.display = 'block';
                                loader.remove();
                            });
                    });
                });
            }
        });
    </script>

@endsection
