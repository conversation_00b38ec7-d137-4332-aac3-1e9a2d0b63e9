<?php

namespace Database\Seeders;

use App\Models\User;
use App\Models\Role;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Testing\Fakes\Fake;
use Faker\Factory as Faker;

class UsersTableSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
     public function run(): void
    {
        $faker = Faker::create();

        // Create one admin user
        // $admin = User::create([
        //     'firstName' => 'Admin',
        //     'lastName' => 'User',
        //     'email' => '<EMAIL>',
        //     'password' => Hash::make('password'),
        //     'email_verified_at' => now(),
        // ]);

        // $admin->roles()->attach(ROLE::ADMIN);

    $superAdmin=User::create([
        'firstName'=>'Super',
         'lastName'=>'Admin',
         'email'=>'<EMAIL>',
         'password'=>Hash::make('password'),
         'email_verified_at'=>now(),
    ]);

     $superAdmin->roles()->attach(ROLE::ADMIN);


     $superAdminTwo=User::create([
        'firstName'=>'Super',
         'lastName'=>'Admintwo',
         'email'=>'<EMAIL>',
         'password'=>Hash::make('massPremierSuperAdmin'),
         'email_verified_at'=>now(),
    ]);

     $superAdminTwo->roles()->attach(ROLE::ADMIN);



    }
}
