<?php

namespace App\Listeners;

use App\Events\PlayerInvitedByAdmin;
use App\Jobs\SendPlayerInvitationByAdminEmail;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;

class sendInvitationEmailToPlayerByAdmin
{
    /**
     * Create the event listener.
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     */
    public function handle(PlayerInvitedByAdmin $event): void
    {
          SendPlayerInvitationByAdminEmail::dispatch($event->guardian, $event->player, $event->program);
    }
}
