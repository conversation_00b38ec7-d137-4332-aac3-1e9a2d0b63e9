<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateRecurringPaymentsTable extends Migration
{
    public function up()
    {
        Schema::create('recurring_payments', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->string('card_holder')->nullable();
            $table->string('address')->nullable();
            $table->string('state')->nullable();
            $table->foreignId('program_id')->constrained()->onDelete('cascade');
            $table->enum('payment_type', ['recurring', 'one-time']);
            $table->json('player_ids')->nullable();
            $table->foreignId('team_id')->nullable()->constrained()->onDelete('cascade');
            $table->string('email')->nullable();
            $table->integer('number_of_payments')->default(1);
            $table->decimal('initial_paid_amount', 10, 2);
            $table->decimal('total_amount_due', 10, 2);
            $table->decimal('paid_amount', 10, 2)->default(0);
            $table->date('start_date');
            $table->date('end_date');
            $table->timestamps();
        });
    }

    public function down()
    {
        Schema::dropIfExists('recurring_payments');
    }
}

