@extends('layouts.app')
@section('title', 'Admin')
@section('content')

    @if (session('error'))
        <div class="alert alert-danger text-center mx-auto" id="errorSession" role="alert" style="width: 50%;">
            {{ session('error') }}
        </div>
    @endif

    <section class="page_title text-center pt-5">
        <h1 class="text-uppercase lh-1 mb-0">Reports</h1>
    </section>
    <section class="sec report-generate">
        <div class="container">
            <div class="heading align-items-center d-flex flex-column text-center"><span
                    class="hero-bar d-inline-flex mb-5"></span></div>
            <div class="row justify-content-center mb-5">
                <div class="col-lg-10">
                    <div class="row justify-content-center report-ctas">
                        <div class="col-md-4 mt-4">
                            <a class="cta-generated active d-flex text-center align-items-center justify-content-center text-uppercase text-decoration-none w-100 py-3"
                                href="{{ route('admin.usersReport') }}"><span class="icon me-2"><img
                                        src="{{ asset('images/ur-white-icon.svg') }}" alt="user-icon" /></span> User
                                Report</a>
                        </div>
                        <div class="col-md-4 mt-4">
                            <a class="cta-generated d-flex text-center align-items-center justify-content-center text-uppercase text-decoration-none w-100 py-3"
                                href="{{ route('admin.programsReport') }}"><span class="icon me-2"><img
                                        src="{{ asset('images/pr-icon.svg') }}" alt="program-icon" /></span> PROGRAM
                                Report</a>
                        </div>
                        <div class="col-md-4 mt-4">
                            <a class="cta-generated d-flex text-center align-items-center justify-content-center text-uppercase text-decoration-none w-100 py-3"
                                href="{{ route('admin.financialReport') }}"><span class="icon me-2"><img
                                        src="{{ asset('images/fr-icon.svg') }}" alt="financial-icon" /></span> FINANCIAL
                                Report</a>
                        </div>
                    </div>
                </div>
            </div>
            <form id="filterForm" class="form mt-5">
                <div class="row align-items-md-center mb-4">
                    <div class="col-md-auto form-label mb-2 text-uppercase">SORT BY:</div>
                    <div class="mb-2 col-md-auto">
                        <div class="form-check">
                            <input class="form-check-input filter-checkbox" id="byTown" type="checkbox" value="town" />
                            <label class="form-check-label p text-uppercase" for="town">By Town</label>
                        </div>
                    </div>
                    <div class="mb-2 col-md-auto">
                        <div class="form-check">
                            <input class="form-check-input filter-checkbox" id="bySport" type="checkbox" value="sport" />
                            <label class="form-check-label p text-uppercase" for="sport">By Sport</label>
                        </div>
                    </div>
                    <div class="mb-2 col-md-auto">
                        <div class="form-check">
                            <input class="form-check-input filter-checkbox" id="byProgram" type="checkbox"
                                value="program" />
                            <label class="form-check-label p text-uppercase" for="program">By Program</label>
                        </div>
                    </div>
                    <div class="mb-2 col-md-auto">
                        <div class="form-check">
                            <input class="form-check-input filter-checkbox" id="byPlayer" type="checkbox" value="player" />
                            <label class="form-check-label p text-uppercase" for="player">By Player</label>
                        </div>
                    </div>
                    <div class="mb-2 col-md-auto">
                        <div class="form-check">
                            <input class="form-check-input filter-checkbox" id="byAge" type="checkbox" value="age" />
                            <label class="form-check-label p text-uppercase" for="age">By Age</label>
                        </div>
                    </div>
                    <div class="mb-2 col-md-auto">
                        <div class="form-check">
                            <input class="form-check-input filter-checkbox" id="byGender" type="checkbox" value="gender" />
                            <label class="form-check-label p text-uppercase" for="gender">By Gender</label>
                        </div>
                    </div>
                    <div class="mb-2 col-md-auto">
                        <div class="form-check">
                            <input class="form-check-input filter-checkbox" id="byDate" type="checkbox" value="date" />
                            <label class="form-check-label p text-uppercase" for="date">By Date</label>
                        </div>
                    </div>
                </div>
                <div class="row mb-4">
                    <div class="col-md-auto form-label mb-2 mb-md-0 text-uppercase align-self-md-center">FILTER:</div>
                    <div class="mb-4 mb-md-0 d-flex align-items-center col-lg-4 col-xl-5">
                        <label class="form-label text-uppercase mb-md-0 me-md-3 text-black fw-normal"
                            for="category">Category</label>
                        <div class="select-arrow position-relative flex-grow-1">
                            <select class="form-control" id="category">
                                <option value="">Select Category</option>

                            </select><span class="arrow"><i class="bi bi-chevron-down"></i></span>
                        </div>
                    </div>
                    <div class="mb-4 mb-md-0 d-flex align-items-center col-lg-4 col-xl-4">
                        <label class="form-label text-uppercase mb-md-0 me-md-3 text-black fw-normal"
                            for="Sub category">Sub
                            Category</label>
                        <div class="select-arrow position-relative flex-grow-1">
                            <select class="form-control" id="subCategory">
                                <option value="">Select Sub Category</option>
                            </select><span class="arrow"><i class="bi bi-chevron-down"></i></span>
                        </div>
                    </div>
                    <div class="col-md mb-4 mb-md-0">
                        <button id="createReport" class="cta rounded-3 w-100 h-100">CREATE</button>
                    </div>
                </div>
            </form>
        </div>
    </section>

    <div class="container">
        <div class="program-table">
            <div class="program-table mt-5">
                <hr class="mt-5 mb-0" />
            </div>

            @include('admin.reports.partialUsersReports')
        </div>
    </div>
@endsection
<script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.17.1/xlsx.full.min.js"></script>


<script>
    document.addEventListener('DOMContentLoaded', function() {
        const checkboxes = document.querySelectorAll('.filter-checkbox');
        const categorySelect = document.getElementById('category');
        const subCategorySelect = document.getElementById('subCategory');
        let selectedCheckboxes = [];

        checkboxes.forEach(checkbox => {
            checkbox.addEventListener('change', function() {
                if (this.checked) {
                    if (selectedCheckboxes.length >= 2) {
                        this.checked = false;
                        showGlobalError("You can select two filters");
                        return;
                    } else {
                        selectedCheckboxes.push(this.id);
                    }
                } else {
                    selectedCheckboxes = selectedCheckboxes.filter(id => id !== this.id);
                }

                updateFilters(selectedCheckboxes);
            });
        });

        function updateFilters(selected) {

            categorySelect.style.display = 'block';
            subCategorySelect.style.display = 'block';
            categorySelect.innerHTML = '<option selected value="">Select Category</option>';
            subCategorySelect.innerHTML = '<option selected value="">Select Sub Category</option>';
            categorySelect.name = '';
            subCategorySelect.name = '';
            if (selected.length >= 1) {
                categorySelect.name = selected[0];
            }
            if (selected.length === 2) {
                subCategorySelect.name = selected[1];
            }


            removeDateInputs();


            if (selected.includes('byDate')) {
                if (selected.indexOf('byDate') === 0) {

                    insertDateInputs(categorySelect);
                } else if (selected.indexOf('byDate') === 1) {

                    insertDateInputs(subCategorySelect);
                }
            }


            if (selected.length > 0) {
                const firstOption = selected[0];
                fillDropdown(categorySelect, firstOption);
            }

            if (selected.length > 1) {
                const secondOption = selected[1];
                fillDropdown(subCategorySelect, secondOption);
            }
        }

        function insertDateInputs(targetElement) {
            targetElement.style.display = 'none';

            const dateRangeContainer = document.createElement('div');
            dateRangeContainer.id = 'date-range-container';
            dateRangeContainer.style.marginTop = '10px';
            dateRangeContainer.style.padding = '10px';
            dateRangeContainer.style.border = '1px solid #ccc';
            dateRangeContainer.style.borderRadius = '5px';
            dateRangeContainer.style.backgroundColor = '#f9f9f9';
            dateRangeContainer.style.display = 'flex';
            dateRangeContainer.style.flexWrap = 'wrap';
            dateRangeContainer.style.gap = '20px';

            const startContainer = document.createElement('div');
            startContainer.style.display = 'flex';
            startContainer.style.flexDirection = 'column';

            const startLabel = document.createElement('label');
            startLabel.setAttribute('for', 'startDate');
            startLabel.textContent = 'Start Date:';
            startLabel.style.marginBottom = '5px';

            const startInput = document.createElement('input');
            startInput.type = 'date';
            startInput.id = 'startDate';
            startInput.style.padding = '5px';

            startContainer.appendChild(startLabel);
            startContainer.appendChild(startInput);

            const endContainer = document.createElement('div');
            endContainer.style.display = 'flex';
            endContainer.style.flexDirection = 'column';

            const endLabel = document.createElement('label');
            endLabel.setAttribute('for', 'endDate');
            endLabel.textContent = 'End Date:';
            endLabel.style.marginBottom = '5px';

            const endInput = document.createElement('input');
            endInput.type = 'date';
            endInput.id = 'endDate';
            endInput.style.padding = '5px';

            endContainer.appendChild(endLabel);
            endContainer.appendChild(endInput);

            dateRangeContainer.appendChild(startContainer);
            dateRangeContainer.appendChild(endContainer);

            targetElement.parentNode.insertBefore(dateRangeContainer, targetElement);
        }



        function removeDateInputs() {
            const dateRangeContainer = document.getElementById('date-range-container');
            if (dateRangeContainer) {
                dateRangeContainer.parentNode.removeChild(dateRangeContainer);
            }
        }

        function fillDropdown(dropdown, option) {
            if (dropdown.options.length > 1) return;

            let url;

            if (option === 'byTown') {
                url = route('admin.towns');
            } else if (option === 'bySport') {
                url = route('admin.sport');
            } else if (option === 'byPlayer') {
                url = route('admin.players');
            } else if (option === 'byGender') {
                url = route('admin.genders');
            } else if (option === 'byProgram') {
                url = route('admin.programs');
            } else if (option === 'byAge') {
                url = route('admin.ages');
            }

            if (!url) return;

            fetch(url)
                .then(response => response.json())
                .then(data => {
                    dropdown.innerHTML =
                        '<option selected value="">Select an Option</option>';

                    if (option === 'byTown' || option === 'bySport' || option === 'byGender' || option ===
                        'byProgram') {
                        data.forEach(item => {
                            const optionElement = document.createElement('option');
                            optionElement.value = item.town || item.name || item.gender || item.id;
                            optionElement.textContent = item.town || item.name || item.gender ||
                                item.name;
                            dropdown.appendChild(optionElement);
                        });
                    } else if (option === 'byPlayer') {
                        data.forEach(player => {
                            const optionElement = document.createElement('option');
                            optionElement.value = player.id;
                            optionElement.textContent = `${player.firstName} ${player.lastName}`;
                            dropdown.appendChild(optionElement);
                        });
                    } else if (option === 'byAge') {
                        for (let ageGroup in data) {
                            if (data.hasOwnProperty(ageGroup)) {
                                const optionElement = document.createElement('option');
                                optionElement.value = ageGroup;
                                optionElement.textContent = data[ageGroup];
                                dropdown.appendChild(optionElement);
                            }
                        }
                    }
                })
                .catch(() => {
                    alert(`Failed to fetch data for ${option}.`);
                });
        }



        let form = document.getElementById('filterForm');



        form.addEventListener('submit', function(event) {
            event.preventDefault();
        })


        let createReportButton = document.getElementById('createReport');

        createReportButton.addEventListener('click', function() {
            let category = document.getElementById('category');
            let subCategory = document.getElementById('subCategory');
            let startDateInput = document.getElementById('startDate');
            let endDateInput = document.getElementById('endDate');

            let data = {};
            if (!category.value && !subCategory.value) {
                showGlobalError('Select Values from filters');
                return;
            }

            if (startDateInput && endDateInput) {
                data.startDate = startDateInput.value;
                data.endDate = endDateInput.value;
            }
            if (category && category.value) {
                const categoryName = category.getAttribute('name');
                if (categoryName) {
                    data[categoryName] = category.value;
                }
            }

            if (subCategory && subCategory.value) {
                const subCategoryName = subCategory.getAttribute('name');
                if (subCategoryName) {
                    data[subCategoryName] = subCategory.value;
                }
            }
            const queryString = new URLSearchParams(data).toString();
            let url = route('admin.specificReport') + '?' + queryString;
            fetch(url, {
                    method: 'GET',
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest',
                        'Content-Type': 'application/json',
                    },
                })
                .then((response) => {
                    if (response.ok) {
                        window.location.href = url;
                    } else {
                        return response.json().then((errorData) => {
                            showGlobalError(errorData.message || 'An error occurred');
                        });
                    }
                })
                .catch((error) => {
                    showGlobalError(error.message || 'An error occurred');
                })
        });


        let errorSession = document.getElementById('errorSession');
        if (errorSession) {

            setTimeout(() => {
                errorSession.style.display = 'none';
            }, 3000);
        }



        document.addEventListener("click", function(event) {
            if (event.target.classList.contains("toggle-arrow")) {
                const arrow = event.target;
                const userId = arrow.getAttribute("data-toggle");

                const programTable = document.getElementById(`program-table${userId}`);
                const loadMoreContainer = document.getElementById(`load-more-container-${userId}`);
                const programTableForCoachAndGuardian = document.getElementById(
                    `program-tableForCoachAndGuardian${userId}`
                );

                if (programTable) {
                    programTable.classList.toggle("d-none");

                    if (programTableForCoachAndGuardian) {
                        programTableForCoachAndGuardian.classList.toggle("d-none");
                    }


                    if (loadMoreContainer) {
                        if (programTable.classList.contains("d-none")) {
                            loadMoreContainer.classList.add("d-none");
                        } else {
                            loadMoreContainer.classList.remove("d-none");
                        }
                    }


                    if (programTable.classList.contains("d-none")) {
                        arrow.classList.remove("bi-caret-down-fill");
                        arrow.classList.add("bi-caret-up-fill");
                    } else {
                        arrow.classList.remove("bi-caret-up-fill");
                        arrow.classList.add("bi-caret-down-fill");
                    }
                } else {
                    console.warn(`No table found for user ID: ${userId}`);
                }
            }
        });







        //printing and excel

        const printIcons = document.querySelectorAll('.print-icon');

        printIcons.forEach(icon => {
            icon.addEventListener('click', function(e) {
                e.preventDefault();
                const userId = this.getAttribute('data-user-id');

                const userRow = this.closest('.tables_collapse_heading').querySelector(
                    'table tr');


                const detailsSection = document.querySelector(`#program-table${userId}`)
                    ?.closest('.table_collapse_table');


                const guardianCoachTable = document.querySelector(
                    `#program-tableForCoachAndGuardian${userId}`);


                printUserData(userRow, detailsSection, guardianCoachTable);
            });
        });

        function printUserData(userRow, detailsSection, guardianCoachTable) {
            const printWindow = window.open('', '_blank');


            const userData = {
                firstName: getUserDataFromRow(userRow, 2), // First name in column 2
                lastName: getUserDataFromRow(userRow, 3), // Last name in column 3
                role: getUserDataFromRow(userRow, 4), // Role in column 4
                town: getUserDataFromRow(userRow, 5), // Town in column 5
                gender: getUserDataFromRow(userRow, 6), // Gender in column 6
                email: getUserDataFromRow(userRow, 7), // Email in column 7
            };


            const printContent = `
        <!DOCTYPE html>
        <html>
        <head>
            <title>User Details - ${userData.firstName} ${userData.lastName}</title>
            <style>
                body { font-family: Arial, sans-serif; padding: 20px; }
                h2 { text-align: center; color: #0B4499; margin-bottom: 20px; }
                h3 { color: #0B4499; margin: 20px 0; }
                table { width: 100%; margin-bottom: 20px; border-collapse: collapse; }
                th, td { padding: 12px; border: 1px solid #dee2e6; text-align: left; }
                th { background-color: #f8f9fa; }
                .no-data {
                    text-align: center;
                    padding: 20px;
                    color: #666;
                    font-style: italic;
                    border: 1px solid #dee2e6;
                }
                @media print {
                    body { padding: 0; }
                    table { page-break-inside: auto; }
                    tr { page-break-inside: avoid; page-break-after: auto; }
                    thead { display: table-header-group; }
                }
            </style>
        </head>
        <body>
            <h2>${userData.firstName} ${userData.lastName}</h2>

            <!-- User Details Table -->
            <table>
                <thead>
                    <tr>
                        <th>First Name</th>
                        <th>Last Name</th>
                        <th>Role</th>
                        <th>Town</th>
                        <th>Gender</th>
                        <th>Email</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>${userData.firstName}</td>
                        <td>${userData.lastName}</td>
                        <td>${userData.role}</td>
                        <td>${userData.town}</td>
                        <td>${userData.gender}</td>
                        <td>${userData.email}</td>
                    </tr>
                </tbody>
            </table>

            <!-- Additional Details Tables -->
            ${processDetailsTables(detailsSection, guardianCoachTable, userData.role)}
        </body>
        </html>
    `;

            printWindow.document.write(printContent);
            printWindow.document.close();

            printWindow.onload = function() {
                printWindow.print();
                printWindow.onafterprint = function() {
                    printWindow.close();
                };
            };
        }

        function getUserDataFromRow(userRow, index) {
            const cell = userRow.querySelector(`td:nth-child(${index})`);
            if (cell) {

                const textContent = cell.textContent.trim();
                return textContent || 'N/A';
            }
            return 'N/A';
        }

        function processDetailsTables(detailsSection, guardianCoachTable, userRole) {
            let content = '';

            if (detailsSection) {
                const tableContent = processTableContent(detailsSection, userRole);
                if (tableContent) {
                    content += tableContent;
                }
            }

            // Ensure the guardian/coach team details are processed for both roles
            if (guardianCoachTable && (userRole.includes('guardian') || userRole.includes('coach'))) {
                const guardianCoachContent = processGuardianCoachTable(guardianCoachTable);
                if (guardianCoachContent) {
                    content += guardianCoachContent;
                }
            }

            return content || '<div class="no-data">No additional information available</div>';
        }

        function processTableContent(detailsSection, userRole) {
            const detailsContent = detailsSection.cloneNode(true);
            let content = '';


            const noDataMessage = detailsContent.querySelector('td[colspan]');
            if (noDataMessage) {
                content += handleNoData(userRole);
                return content;
            }


            const actionsToRemove = detailsContent.querySelectorAll(
                'td:last-child, th:last-child, td:first-child, th:first-child');
            actionsToRemove.forEach(cell => cell.remove());


            content += getRoleSpecificHeaders(userRole);

            const table = detailsContent.querySelector('table');
            if (table) {

                const cells = table.querySelectorAll('td');
                cells.forEach(cell => {
                    const cellContent = cell.textContent.trim();
                    if (!cellContent || cellContent === 'null' || cellContent === 'undefined') {
                        cell.textContent = 'N/A';
                    }
                });

                content += table.outerHTML;
            }

            return content;
        }

        function processGuardianCoachTable(guardianCoachTable) {
            const coachContent = guardianCoachTable.cloneNode(true);

            const actionsToRemove = coachContent.querySelectorAll(
                'td:last-child, th:last-child, td:first-child, th:first-child');
            actionsToRemove.forEach(cell => cell.remove());


            let table = coachContent.querySelector('table');

            if (!table) {
                const thead = coachContent.querySelector('thead');
                const tbody = coachContent.querySelector('tbody');


                if (thead && tbody) {

                    table = document.createElement('table');
                    table.appendChild(thead);
                    table.appendChild(tbody);
                }
            }

            if (table) {

                const rows = table.querySelectorAll('tr');

                let hasValidData = false;
                rows.forEach(row => {
                    const cells = row.querySelectorAll('td');

                    if (cells.length > 0 && cells[0].textContent.trim() !== '') {
                        hasValidData = true;
                    }
                });

                if (!hasValidData) {
                    return '<h3>Team Information</h3><div class="no-data">No teams information available</div>';
                }


                const cells = table.querySelectorAll('td');


                cells.forEach(cell => {
                    const cellContent = cell.textContent.trim();
                    if (!cellContent || cellContent === 'null' || cellContent === 'undefined') {
                        cell.textContent = 'N/A';
                    }
                });

                return '<h3>Team Information</h3>' + table.outerHTML;
            }

            return '<h3>Team Information</h3><div class="no-data">No team information available</div>';
        }

        function handleNoData(userRole) {
            let content = '';
            if (userRole.includes('guardian')) {
                content +=
                    '<h3>Children Information</h3><div class="no-data">No children information available</div>';
            }
            if (userRole.includes('coach')) {
                content += '<h3>Team Information</h3><div class="no-data">No team information available</div>';
            }
            if (userRole.includes('player')) {
                content +=
                    '<h3>Program Information</h3><div class="no-data">No program information available</div>';
            }
            return content;
        }

        function getRoleSpecificHeaders(userRole) {
            let content = '';
            if (userRole.includes('guardian')) {
                content += '<h3>Children Information</h3>';
            }
            if (userRole.includes('coach') && !userRole.includes('guardian')) {
                content += '<h3>Team Information</h3>';
            }
            if (userRole.includes('player')) {
                content += '<h3>Program Information</h3>';
            }
            return content;
        }

        const exportUserDataButtons = document.querySelectorAll('.export-xls-icon');

        exportUserDataButtons.forEach(exportButton => {
            exportButton.addEventListener('click', function(event) {
                event.preventDefault();
                const selectedUserId = this.getAttribute('data-user-id');

                const selectedUserRow = this.closest('.tables_collapse_heading').querySelector(
                    'table tr');
                const selectedUserDetailsSection = document.querySelector(
                    `#program-table${selectedUserId}`)?.closest('.table_collapse_table');
                const selectedGuardianCoachTable = document.querySelector(
                    `#program-tableForCoachAndGuardian${selectedUserId}`);

                const extractedUserData = extractSelectedUserData(selectedUserRow);
                const extractedRelatedData = processSelectedUserDetails(
                    selectedUserDetailsSection,
                    selectedGuardianCoachTable,
                    extractedUserData.role
                );

                generateExcelFile(extractedUserData, extractedRelatedData);
            });
        });

        function extractSelectedUserData(userRow) {
            return {
                firstName: extractTableCellData(userRow, 2),
                lastName: extractTableCellData(userRow, 3),
                role: extractTableCellData(userRow, 4),
                town: extractTableCellData(userRow, 5),
                gender: extractTableCellData(userRow, 6),
                email: extractTableCellData(userRow, 7),
            };
        }

        function extractTableCellData(row, columnIndex) {
            const cell = row.querySelector(`td:nth-child(${columnIndex})`);
            return cell ? cell.textContent.trim() || 'N/A' : 'N/A';
        }


        function extractProgramDetails(detailsSection) {
            const programDetails = [];
            const rows = detailsSection.querySelectorAll('tr');

            // Get actual headers (assuming they're in first row)
            const validHeaders = [
                'Child First Name',
                'Child Last Name',
                'Email',
                'Grade',
                'Age',
                'Gender'
            ];

            // Skip the header row and process data rows
            for (let i = 1; i < rows.length; i++) {
                const cells = rows[i].querySelectorAll('td');
                const rowDetails = {};

                // Only process the cells that correspond to valid headers
                cells.forEach((cell, index) => {
                    if (index < validHeaders.length) {
                        const value = cell.textContent.trim();
                        if (value && value !== 'N/A') { // Only add non-empty and non-N/A values
                            rowDetails[validHeaders[index]] = value;
                        }
                    }
                });

                // Only add rows that have actual data
                if (Object.keys(rowDetails).length > 0) {
                    programDetails.push(rowDetails);
                }
            }

            return programDetails;
        }

        function extractTeamDetails(guardianCoachTable) {
            const teamDetails = [];
            const rows = guardianCoachTable.querySelectorAll('tr');

            // Get actual headers for team details
            const validHeaders = [
                'Team Name',
                'Coach',
                'Type'
            ];

            // Skip the header row and process data rows
            for (let i = 1; i < rows.length; i++) {
                const cells = rows[i].querySelectorAll('td');
                const rowDetails = {};

                // Only process the cells that correspond to valid headers
                cells.forEach((cell, index) => {
                    if (index < validHeaders.length) {
                        const value = cell.textContent.trim();
                        if (value && value !== 'N/A') { // Only add non-empty and non-N/A values
                            rowDetails[validHeaders[index]] = value;
                        }
                    }
                });

                // Only add rows that have actual data
                if (Object.keys(rowDetails).length > 0) {
                    teamDetails.push(rowDetails);
                }
            }

            return teamDetails;
        }

        function generateExcelFile(userData, relatedData) {
            const workbook = XLSX.utils.book_new();

            // User Information Sheet - Horizontal format
            const userHeaders = ['First Name', 'Last Name', 'Role', 'Town', 'Gender', 'Email'];
            const userValues = [userData.firstName, userData.lastName, userData.role, userData.town, userData
                .gender, userData.email
            ];

            const userInfoSheetData = [
                userHeaders,
                userValues
            ];
            const userInfoSheet = XLSX.utils.aoa_to_sheet(userInfoSheetData);
            XLSX.utils.book_append_sheet(workbook, userInfoSheet, "User Information");

            // Program Details Sheet
            if (relatedData.programDetails.length > 0) {
                const headers = Object.keys(relatedData.programDetails[0]).filter(header =>
                    relatedData.programDetails.some(row => row[header] && row[header] !== 'N/A')
                );

                const programDetailsSheetData = [
                    headers,
                    ...relatedData.programDetails.map(row =>
                        headers.map(header => row[header] || '')
                    )
                ];

                const programDetailsSheet = XLSX.utils.aoa_to_sheet(programDetailsSheetData);
                XLSX.utils.book_append_sheet(workbook, programDetailsSheet, "Program Details");
            }

            // Team Details Sheet
            if (relatedData.teamDetails.length > 0) {
                const headers = Object.keys(relatedData.teamDetails[0]).filter(header =>
                    relatedData.teamDetails.some(row => row[header] && row[header] !== 'N/A')
                );

                const teamDetailsSheetData = [
                    headers,
                    ...relatedData.teamDetails.map(row =>
                        headers.map(header => row[header] || '')
                    )
                ];

                const teamDetailsSheet = XLSX.utils.aoa_to_sheet(teamDetailsSheetData);
                XLSX.utils.book_append_sheet(workbook, teamDetailsSheet, "Team Details");
            }

            // Export the Excel file
            const fileName = `${userData.firstName || "User"}_${userData.lastName || "Details"}.xlsx`;
            XLSX.writeFile(workbook, fileName);
        }


        function processSelectedUserDetails(detailsSection, guardianCoachTable, userRole) {
            console.log('Processing details with role:', userRole); // Debug log

            const processedData = {
                programDetails: [],
                teamDetails: []
            };

            if (detailsSection) {
                processedData.programDetails = extractProgramDetails(detailsSection);
            }

            // Add null/undefined check for userRole
            if (guardianCoachTable && userRole &&
                (userRole.toLowerCase().includes('guardian') || userRole.toLowerCase().includes('coach'))) {
                processedData.teamDetails = extractTeamDetails(guardianCoachTable);
            }

            return processedData;
        }



    });
</script>
