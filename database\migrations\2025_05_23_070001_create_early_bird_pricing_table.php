<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
public function up(): void
{
    Schema::create('early_bird_pricing', function (Blueprint $table) {
        $table->id();
        $table->foreignId('program_id')->constrained('programs')->onDelete('cascade');
        $table->integer('from');
        $table->integer('to');
        $table->decimal('price_before', 8, 2);
        $table->decimal('price_on_or_after', 8, 2);
        $table->timestamps();
    });
}


    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('early_bird_pricing');
    }
};

