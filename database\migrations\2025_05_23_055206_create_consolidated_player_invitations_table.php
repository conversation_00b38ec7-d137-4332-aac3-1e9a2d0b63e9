<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Drop the old player_invitations table if it exists
        if (Schema::hasTable('player_invitations')) {
            Schema::dropIfExists('player_invitations');
        }

        // Create the new consolidated player_invitations table
        Schema::create('player_invitations', function (Blueprint $table) {
            $table->id();
            $table->integer('coach_id');
            $table->integer('user_id');
            $table->integer('team_id');
            $table->unsignedBigInteger('program_id');
            $table->enum('invitation_status', ['pending', 'accepted', 'declined'])->default('pending');
            $table->decimal('balance_due', 10, 2)->default(0.00);
            $table->decimal('balance_assigned', 10, 2)->default(0.00);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('player_invitations');
    }
};
