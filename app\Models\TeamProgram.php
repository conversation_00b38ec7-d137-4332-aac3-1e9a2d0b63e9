<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class TeamProgram extends Model
{
    use HasFactory;

     protected $fillable = [
            'team_id',
            'program_id'
    ];

     protected $table = 'team_program';


public function program()
{
    return $this->belongsTo(Program::class, 'program_id');
}


public function team()
{
    return $this->belongsTo(Team::class);
}


}
