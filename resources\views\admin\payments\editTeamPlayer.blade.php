@extends('layouts.app')
@section('title', 'Admin')
@section('content')

    <section class="page_title text-center pt-5">
        <h1 class="text-uppercase lh-1 mb-0">Payments/<br>Registrations</h1>

        @if (session('success'))
            <div id="successMessageForSession">
                <span id="successText">{{ session('success') }}</span>
            </div>
        @endif

        @if (session('error'))
            <div id="errorMessageForSession">
                <span id="errorText">{{ session('error') }}</span>
            </div>
        @endif
    </section>

    <section class="sec admin-welcome">
        <div class="container">
            <div class="d-flex align-items-center justify-content-between mb-4">

                <div>
                    <a class="cta" href="{{ url()->previous() }}">Back</a>
                </div>



                <div class="text-center">
                    <span class="hero-bar d-inline-flex mb-2"></span>
                    <h6 class="text-uppercase mb-0" style="color: #d4c26f; font-weight: bold;">{{ $teamName }}</h6>
                </div>


                <div style="width: 100px;"></div>
            </div>
            <div class="table-program table-responsive">
                <table class="table table-hover" width="100%">

                    <tbody>
                        <table class="table table-hover" width="100%">
                            <thead>
                                <tr>
                                    <th>First Name</th>
                                    <th>Last Name</th>
                                    <th>Email</th>
                                    <th>Invite Status</th>
                                    <th>Balance Due</th>
                                    <th>Actions</th>
                                    <th>Delete</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach ($teams as $invitation)
                                    <tr>
                                        <td class="py-4" valign="middle">{{ $invitation->user->firstName }}</td>
                                        <td class="py-4" valign="middle">{{ $invitation->user->lastName }}</td>
                                        <td class="py-4" valign="middle">{{ $invitation->user->email }}</td>


                                        <td class="py-4" valign="middle">{{ $invitation->invitation_status }}</td>
                                        <td class="py-4" valign="middle">${{ $invitation->balance_due }}</td>


                                        <td class="py-4" valign="middle">
                                            <span class="action edit">
                                                <a href="">
                                                    <img src="{{ asset('images/edit-icon.svg') }}" alt=""
                                                        width="20" height="20" />
                                                </a>
                                            </span>
                                        </td>


                                        <td class="py-4" valign="middle">
                                            <form action="{{ route('admin.deletePlayerFromTeam') }}" method="POST"
                                                style="display: inline;" id="deleteForm{{ $invitation->user->id }}">
                                                <input type="hidden" name="playerId" id="playerId"
                                                    value='{{ $invitation->user->id }}'>
                                                <input type="hidden"name="teamId" id="teamId"
                                                    value='{{ $invitation->team_id }}'>
                                                <input type="hidden"name="programId" id="programId"
                                                    value='{{ $invitation->program_id }}'>
                                                <input type="hidden"name="coachId" id="coachId"
                                                    value='{{ $invitation->coach_id }}'>
                                                @csrf
                                                @method('DELETE')
                                                <button type="submit"
                                                    onsubmit="showConfirmation(event, 'deletePlayerFromeTeam-{{ $invitation->user->id }}')"
                                                    data-id="{{ $invitation->user->id }}"
                                                    id="delete{{ $invitation->user->id }}"
                                                    style="border: none; background: none; padding: 0; cursor: pointer;">
                                                    <span class="action delete">
                                                        <img src="{{ asset('images/delete-icon.svg') }}" alt=""
                                                            width="18" height="20" />
                                                    </span>
                                                </button>

                                            </form>
                                        </td>
                                    </tr>
                                @endforeach
                            </tbody>
                        </table>

                        <div class="cta-row text-center mt-4">
                            <button class="cta playerModalButton" id="add-player-btn-{{ $program->slug }}"
                                data-program-id={{ $program->slug }}
                                style="font-size: 10px; line-height: 1; font-family: 'Poppins', sans-serif; margin-left: 50px; margin-bottom: 20px;">
                                ADD player to team
                            </button>
                        </div>
            </div>


        </div>

        <div class="modal fade" id="searchAndAddPlayer" tabindex="-1" aria-labelledby="searchPlayerLabel"
            aria-hidden="true" data-bs-backdrop="static">
            <div class="modal-dialog modal-dialog-centered" style="max-width: 1160px">
                <div class="modal-content">
                    <div class="modal-body p-5">
                        <span class="modal-close" data-bs-dismiss="modal">&#x2715;</span>
                        <form class="form mb-5" id="addPlayerForm">
                            <div id="addPlayerSuccessMessage" class="alert alert-success d-none" role="alert">
                            </div>
                            <div id="addPlayerErrorMessage" class="alert alert-danger d-none" role="alert"></div>
                            @csrf
                            <div class="row">
                                <div class="mb-4 col-md-3">
                                    <label class="text-uppercase form-label" for="firstName"> Player First
                                        Name</label>
                                    <input class="form-control" id="addPlayerFirstName" type="text" />
                                    <div id="playerFirstNameError" class="text-danger"></div>
                                </div>
                                <div class="mb-4 col-md-3">
                                    <label class="text-uppercase form-label" for="lastName"> Player Last Name</label>
                                    <input class="form-control" id="addPlayerLastName" type="text" />
                                    <div id="playerLastNameError" class="text-danger"></div>
                                </div>
                                <div class="mb-4 col-md-3">
                                    <label class="text-uppercase form-label" for="email"> Player Email</label>
                                    <input class="form-control" id="addPlayerEmail" type="email" />
                                    <div id="playerEmailError" class="text-danger"></div>
                                </div>
                                <div class="mb-4 col-md-3">
                                    <label class="text-uppercase form-label" for="team">Guardian Email</label>
                                    <input class="form-control" id="addPlayerGuardianMail" type="text" />
                                    <div id="playerGuardianMailError" class="text-danger"></div>
                                </div>
                            </div>
                            <div class="col-12 text-center mt-4">
                                <button class="cta" id="searchPlayerButton" type="button"> <span
                                        id="searchButtonTextForPlayers">
                                        Search</span>
                                    <span id="loaderForPlayerSearch">
                                        <img id="loaderForSearchPlayer" src="{{ asset('images/loader.svg') }}"
                                            alt="Loading..."
                                            style="display: none; width: 40px; height: 40px; margin-left: 10px;"></span></button>
                            </div>
                        </form>
                        <div class="program-table" id="searchResultsForPlayer">

                        </div>

                        <button id="loadMorePlayers" class="submit-btn" style="display:none">Load More</button>
                    </div>
                </div>
            </div>
        </div>
    </section>
    <script>
        let individualProgramSlug;
        document.addEventListener('click', function(event) {
            if (event.target && event.target.classList.contains('playerModalButton')) {
                individualProgramSlug = event.target.getAttribute("data-program-id");
                const addPlayerModal = new bootstrap.Modal(document.getElementById(
                    "searchAndAddPlayer"));

                const firstNameError = document.getElementById("playerFirstNameError");
                const lastNameError = document.getElementById("playerLastNameError");
                const emailError = document.getElementById("playerEmailError");
                const guardianMailError = document.getElementById(
                    "playerGuardianMailError"
                );

                document
                    .getElementById("addPlayerFirstName")
                    .value = ""
                document
                    .getElementById("addPlayerLastName")
                    .value = ""
                document.getElementById("addPlayerEmail").value.trim();
                document
                    .getElementById("addPlayerGuardianMail")
                    .value = ""


                document.getElementById("searchResultsForPlayer").innerHTML = "";

                document.getElementById('loadMorePlayers').style.display = "none";

                firstNameError.textContent = "";
                lastNameError.textContent = "";
                emailError.textContent = "";
                guardianMailError.textContent = "";
                addPlayerModal.show();
            }
        });


        document.addEventListener('click', function(event) {
            if (event.target && (event.target.id === 'searchPlayerButton' || event.target.id ==
                    "searchButtonTextForPlayers")) {
                event.preventDefault();

                const firstName = document
                    .getElementById("addPlayerFirstName")
                    .value.trim();
                const lastName = document
                    .getElementById("addPlayerLastName")
                    .value.trim();
                const email = document.getElementById("addPlayerEmail").value.trim();
                const guardianMail = document
                    .getElementById("addPlayerGuardianMail")
                    .value.trim();

                const firstNameError = document.getElementById("playerFirstNameError");
                const lastNameError = document.getElementById("playerLastNameError");
                const emailError = document.getElementById("playerEmailError");
                const guardianMailError = document.getElementById(
                    "playerGuardianMailError"
                );

                firstNameError.textContent = "";
                lastNameError.textContent = "";
                emailError.textContent = "";
                guardianMailError.textContent = "";

                const emailPattern = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                if (!firstName && !lastName && !email && !guardianMail) {
                    firstNameError.textContent = "Please enter a first name or";
                    lastNameError.textContent = "last name or";
                    emailError.textContent = "email or";
                    guardianMailError.textContent = "Guardian email to search";
                    return;
                }
                if (email && !emailPattern.test(email)) {
                    emailError.textContent = "Please enter a valid email address.";
                    return;
                }

                const loadMorePlayers = document.getElementById("loadMorePlayers");
                const url = new URL(
                    route("admin.allPlayers", {
                        program: individualProgramSlug,
                    })
                );

                url.searchParams.set("page", 1);
                if (firstName) url.searchParams.append("playerFirstName", firstName);
                if (lastName) url.searchParams.append("playerLastName", lastName);
                if (email) url.searchParams.append("playerEmail", email);
                if (guardianMail)
                    url.searchParams.append("guardianEmail", guardianMail);

                fetchPlayers(url, 1, loadMorePlayers);
                showLoaderForSearchPlayer();
            }
        });


        let loadedPlayerIds = new Set();

        function fetchPlayers(url, currentPage, loadMorePlayers) {
            fetch(url, {
                    method: "GET",
                    headers: {
                        "X-Requested-With": "XMLHttpRequest",
                    },
                })
                .then((response) => response.json())
                .then((data) => {
                    const resultsContainer = document.getElementById("searchResultsForPlayer");
                    if (currentPage === 1) {
                        resultsContainer.innerHTML = `
                    <div class="program-table">
                        <div class="table-program table-responsive mb-5">
                            <table class="table table-hover" width="100%">
                                <tbody>
                                    <tr>
                                        <th class="py-0">First Name</th>
                                        <th class="py-0">Last Name</th>
                                        <th class="py-0">Email</th>
                                        <th class="py-0">Guardian Mail</th>
                                        <th class="py-0" width="100"></th>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>`;
                        loadedPlayerIds.clear();
                    }

                    const tableBody = resultsContainer.querySelector("tbody");
                    const playersArray = Array.isArray(data.players) ? data.players : [];

                    if (playersArray.length > 0) {
                        playersArray.forEach((player) => {
                            if (loadedPlayerIds.has(player.id)) {
                                return;
                            }

                            loadedPlayerIds.add(player.id);
                            hideLoaderForSearchPlayer()
                            const actionContent = player.is_registered ?
                                `<span class="text-success">Registered</span>` :
                                player.is_invited ?
                                `<span class="text-warning">Invited</span>` :
                                `<button type="button" class="cta hover-dark addPlayerButton" data-player-id="${player.id}">Add</button>`;

                            const playerRow = `
                        <tr>
                            <td class="py-4" valign="middle">${player.firstName}</td>
                            <td class="py-4" valign="middle">${player.lastName || "N/A"}</td>
                            <td class="py-4" valign="middle">${player.email || "N/A"}</td>
                            <td class="py-4" valign="middle">${player.guardian_email || "N/A"}</td>
                            <td class="py-4" valign="middle">${actionContent}</td>
                        </tr>`;

                            tableBody.insertAdjacentHTML("beforeend", playerRow);
                        });

                        if (currentPage >= data.last_page) {
                            loadMorePlayers.style.display = "none";
                        } else {
                            loadMorePlayers.style.display = "block";
                        }

                        loadMorePlayers.setAttribute("data-current-page", currentPage);
                    } else {
                        hideLoaderForSearchPlayer()
                        if (currentPage === 1) {
                            resultsContainer.innerHTML =
                                '<p class="text-center text-gray-600">No Players found.</p>';
                            loadMorePlayers.style.display = "none";
                        }
                    }
                })
                .catch((error) => {
                    console.error("Error:", error);
                    hideLoaderForSearchPlayer()
                    document.getElementById("inviteCoachErrorMessage").classList.remove("d-none");
                    document.getElementById("inviteCoachErrorMessage").textContent =
                        "An error occurred while searching for players.";
                });
        }



        document.addEventListener('click', function(event) {
            if (event.target && event.target.matches('.addPlayerButton')) {
                const playerId = event.target.getAttribute("data-player-id");
                const button = event.target;

                button.innerHTML =
                    `<span class="spinner-border spinner-border-sm text-primary" role="status" aria-hidden="true"></span>`;
                button.style.backgroundColor = "transparent";
                button.style.border = "none";
                button.disabled = true;

                const requestData = {
                    player_id: playerId,
                    program_slug: individualProgramSlug,
                };

                const url = route("admin.addPlayerToProgram");

                fetch(url, {
                        method: "POST",
                        headers: {
                            "Content-Type": "application/json",
                            "X-CSRF-TOKEN": document.querySelector('meta[name="csrf-token"]')
                                .getAttribute("content"),
                        },
                        body: JSON.stringify(requestData),
                    })
                    .then((response) => {
                        if (!response.ok) {
                            throw new Error("Failed to register Player");
                        }
                        return response.json();
                    })
                    .then((data) => {
                        if (data.success) {
                            const span = document.createElement("span");
                            span.innerText = "Invited";
                            span.classList.add("text-warning");

                            button.parentNode.replaceChild(span, button);
                        } else {
                            button.innerHTML = "Add";
                            button.style.backgroundColor = "";
                            button.style.border = "";
                            button.disabled = false;
                            alert(data.message || "An error occurred. Please try again. error 3");
                        }
                    })
                    .catch((error) => {
                        console.error("Error:", error);
                        button.innerHTML = "Add";
                        button.style.backgroundColor = "";
                        button.style.border = "";
                        button.disabled = false;
                        alert("An error occurred. Please try again. error 4");
                    });
            }
        });


        function showLoaderForSearchPlayer() {
            document.getElementById("searchButtonTextForPlayers").style.display =
                "none";
            document.getElementById("loaderForSearchPlayer").style.display =
                "inline-block";
            const searchPlayerButton =
                document.getElementById("searchPlayerButton");
            searchPlayerButton.classList.add("buttonLoader");
        }

        function hideLoaderForSearchPlayer() {
            document.getElementById("searchButtonTextForPlayers").style.display =
                "inline";
            document.getElementById("loaderForSearchPlayer").style.display = "none";
            const searchPlayerButton =
                document.getElementById("searchPlayerButton");
            searchPlayerButton.classList.remove("buttonLoader");
        }

        document.addEventListener('click', function(event) {

            if (event.target && event.target.id === 'loadMorePlayers') {
                const loadMorePlayers = event.target;
                const currentPage = parseInt(loadMorePlayers.getAttribute("data-current-page")) || 1;
                const nextPage = currentPage + 1;

                const url = new URL(route("admin.allPlayers", {
                    program: individualProgramSlug,
                }));
                url.searchParams.set("page", nextPage);

                const firstName = document.getElementById("addPlayerFirstName").value.trim();
                const lastName = document.getElementById("addPlayerLastName").value.trim();
                const email = document.getElementById("addPlayerEmail").value.trim();
                const guardianMail = document.getElementById("addPlayerGuardianMail").value.trim();

                if (firstName) url.searchParams.append("addPlayerFirstName", firstName);
                if (lastName) url.searchParams.append("addPlayerLastName", lastName);
                if (email) url.searchParams.append("addPlayerEmail", email);
                if (guardianMail) url.searchParams.append("addPlayerGuardianMail", guardianMail);

                fetchPlayers(url, nextPage, loadMorePlayers);
            }
        });










        function submitForm(id) {
            const form = document.getElementById(`deleteForm${id}`);
            form.submit();
        }

        successAlert = document.getElementById('successAlert');


        if (successAlert) {

            setTimeout(() => {

                successAlert.classList.add('d-none');

            }, 2000);
        }

        // showSessionSuccessMessage();
        // showSessionErrorMessage();
    </script>
@endsection
