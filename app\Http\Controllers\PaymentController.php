<?php
namespace App\Http\Controllers;
use App\Models\TeamPlayer;
use App\Models\User;
use App\Models\GuardianPayment;
use App\Models\PlayerInvitation;
use App\Models\ProgramRegistration;
use App\Models\PlayerProgram;
use Illuminate\Http\Request;
use Stripe\Stripe;
use Stripe\PaymentIntent;
use Stripe\Exception\ApiErrorException;
use Stripe\Customer;
use Stripe\Subscription;
use DateTime;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Stripe\Webhook;
use Stripe\Exception\SignatureVerificationException;
use DateTimeZone;

class PaymentController extends Controller
{
   public function index(Request $request)
{

    return view('payment.StripePayment');
}


public function updatePaymentStatus(Request $request)
{
    $user = auth()->user();

    // Validate the request input
    $validated = $request->validate([
        'paymentIntentId' => 'required|string',
        'amount' => 'required|numeric',
        'playerIds' => 'required|array',
        'playerIds.*' => 'integer',
        'programId' => 'required|integer',
        'totalAmountToBePaid' => 'required|numeric',
    ]);

    $playerIds = $validated['playerIds'];
    $programId = $validated['programId'];
    $amount = $validated['amount'];
    $totalAmountToBePaid = $validated['totalAmountToBePaid'];
    $paymentId=$validated['paymentIntentId'];

    $updateCount = 0;

    $pendingAmount=$totalAmountToBePaid-$amount;


    foreach ($playerIds as $playerId) {

        $amountForPerPlayerByCost = $user->calculateAmountForProgram($programId);


    PlayerProgram::create([
        'player_id' => $playerId,
        'program_id' => $programId,
    ]);


    $existingRegistration = ProgramRegistration::where('user_id', $user->id)
        ->where('player_id', $playerId)
        ->where('program_id', $programId)
        ->first();

    if ($existingRegistration) {

        $existingRegistration->update([
            'is_paid' => true,
        ]);
    } else {

        ProgramRegistration::create([
            'user_id' => $user->id,
            'program_id' => $programId,
            'amount' => $amountForPerPlayerByCost,
            'player_id' => $playerId,
            'is_paid' => true,
        ]);
    }

    $updateCount++;
}

  GuardianPayment::create([
    'user_id'=>$user->id,
    'payment_id'=>$paymentId,
    'paid_amount'=>$amount,
    'pending_amount'=>$pendingAmount
  ]);


    if ($updateCount > 0) {
        return response()->json(['message' => 'Payment status updated successfully.']);
    } else {
        return response()->json(['error' => 'Failed to update payment status. No records found.'], 500);
    }
}


public function updatePaymentStatusForPaymentOptions(Request $request)
{
    Stripe::setApiKey(env('STRIPE_SECRET'));

    try {
        // Validate the request
        $validated = $request->validate([
            'paymentIntentId' => 'required|string',
            'paymentId'=>'required|string',
            'amount' => 'required|numeric|min:0',
            'totalAmountToBePaid' => 'required|numeric|min:0',
        ]);

        $user = auth()->user();

        $guardianPayments = GuardianPayment::where('user_id', $user->id)
            ->where('payment_type', 'split')
            ->where('pending_amount', '>', 0)
            ->get();

        $totalBalanceDue = $guardianPayments->sum('pending_amount');

        $amount = $validated['amount'];
        if ($amount > $totalBalanceDue) {
            return response()->json([
                'success' => false,
                'message' => 'Payment exceeds the total balance due.',
            ], 400);
        }

        $remainingAmount = $amount;
        $affectedPayments = []; // To store program IDs, player IDs, and amounts deducted

        DB::beginTransaction();

        foreach ($guardianPayments as $payment) {
            if ($remainingAmount <= 0) {
                break;
            }

            $programId = $payment->program_id;
             $playerIds = json_decode($payment->player_ids, true);
            $currentPending = $payment->pending_amount;

            $amountToSubtract = min($currentPending, $remainingAmount);

            $payment->pending_amount -= $amountToSubtract;
            $payment->save();

            Log::info('Payment update', [
                'program_id' => $programId,
                'player_ids' => $playerIds,
                'amount_subtracted' => $amountToSubtract,
                'remaining_pending_amount' => $payment->pending_amount,
            ]);

            // Store program ID, player ID, and amount deducted
            $affectedPayments[] = [
                'program_id' => $programId,
                'player_ids' => $playerIds,
                'amount' => $amountToSubtract,
            ];

            $remainingAmount -= $amountToSubtract;
        }

        // Update the PaymentIntent with the affected programs, players, and deducted amounts
      $paymentIntent=  PaymentIntent::update($validated['paymentId'], [
            'metadata' => [
                'affected_payments' => json_encode($affectedPayments),
                'updated_amount' => $amount,
            ],
        ]);

        $paymentIntent->save();




        DB::commit();

        return response()->json([
            'success' => true,
            'message' => 'Payment status updated successfully.',
        ]);
    } catch (\Exception $e) {
        DB::rollBack();
        Log::error('Payment update failed', ['error' => $e->getMessage()]);

        return response()->json([
            'success' => false,
            'message' => 'Failed to update payment status.',
        ], 500);
    }
}





//  public function processPayment(Request $request)

// {
//     Stripe::setApiKey(env('STRIPE_SECRET'));

//     try {
//         $amount = $request->input('amount');
//         $email = $request->input('email');
//         $guardians = $request->input('guardians', []);
//         $recurring = $request->input('recurring', false);
//         $startDate = $request->input('startDate');
//         $numberOfPayments = $request->input('numberOfPayments');

//         // Validate amount or recurring settings
//         if (!$amount && !$recurring) {
//             return response()->json(['error' => 'Invalid payment data.'], 400);
//         }

//         $metadata = [];


//         if ($email) {
//             $metadata['invoice_email'] = $email;
//         }

//         if (!empty($guardians)) {
//             $metadata['guardians'] = json_encode($guardians);
//         }

//         if ($recurring) {
//             $metadata['recurring'] = true;
//             $metadata['start_date'] = $startDate;
//             $metadata['number_of_payments'] = $numberOfPayments;
//         }


//         $paymentIntent = PaymentIntent::create([
//             'amount' => $amount * 100,
//             'currency' => 'usd',
//             'metadata' => $metadata,
//         ]);

//         return response()->json([
//             'clientSecret' => $paymentIntent->client_secret,
//         ]);

//     } catch (ApiErrorException $e) {

//         return response()->json([
//             'error' => 'Stripe Error: ' . $e->getMessage(),
//         ], 500);
//     } catch (\Exception $e) {

//         return response()->json([
//             'error' => 'Payment failed: ' . $e->getMessage(),
//         ], 500);
//     }
// }



public function fullPayment(Request $request)
{
    try {
        Stripe::setApiKey(env('STRIPE_SECRET'));


        $user = auth()->user();

        $validated = $request->validate([
            'payment_type' => 'required|string',
            'amount' => 'required|numeric',
            'paymentMethodId' => 'required|string',
            'paidAmount' => 'required|numeric',
            'player_ids' => 'required|array',
            'program_id' => 'required|integer',
            'createdAt' => 'required|date',
            'cardHolderName' => 'required|string',
            'state' => 'required|string',
            'address' => 'required|string',
            'team_id'=>'nullable',
        ]);


        $paymentIntent = PaymentIntent::create([
            'amount' => $validated['paidAmount'] * 100,
            'currency' => 'usd',
            'payment_method' => $validated['paymentMethodId'],
            'confirm' => true,
            'description' => 'Program payment for user ID: ' . $user->id,
            'metadata' => [
                'cardHolderName' => $validated['cardHolderName'],
                'address' => $validated['address'],
                'state' => $validated['state'],
                'user_id' => $user->id,
                'program_id' => $validated['program_id'],
                'player_ids' => json_encode($validated['player_ids']),
                'payment_type'=>$validated['payment_type'],
                'paid_amount'=>$validated['paidAmount'],
                'town'=>$user->town??null,
                'email'=>$user->email,
                'payment_id'=>$validated['paymentMethodId']

            ],
             'automatic_payment_methods' => [
                'enabled' => true,
                'allow_redirects' => 'never',
            ],
        ]);

        if ($paymentIntent->status === 'requires_action') {
            return response()->json([
                'requires_action' => true,
                'payment_intent_client_secret' => $paymentIntent->client_secret,
            ]);
        }



        return $this->processPlayerRegistrationsAndPayments($user, $validated);

    } catch (\Exception $e) {
        return response()->json(['error' => 'Payment processing error', 'details' => $e->getMessage()], 500);
    }
}


protected function processPlayerRegistrationsAndPayments($user, $validated) {

         if (!isset($validated['team_id'])) {

    try {
        $playerIds = $validated['player_ids'];
        $programId = $validated['program_id'];
        $updateCount = 0;

        foreach ($playerIds as $playerId) {

            $amountForPerPlayerByCost = $user->calculateAmountForProgram($programId);


            PlayerProgram::updateOrCreate(['player_id' => $playerId, 'program_id' => $programId]);


            ProgramRegistration::updateOrCreate(
                [
                    'user_id' => $user->id,
                    'player_id' => $playerId,
                    'program_id' => $programId,
                ],
                ['is_paid' => true, 'amount' => $amountForPerPlayerByCost]
            );

            $updateCount++;
        }



        GuardianPayment::create([
            'user_id' => $user->id,
            'payment_id' => $validated['paymentMethodId'],
            'paid_amount' => $validated['paidAmount'],
            'pending_amount' => $validated['amount'] - $validated['paidAmount'],
            'created_at' => $validated['createdAt'],
            'payment_type' => ($validated['payment_type'] === 'fullAmount') ? 'full' : 'unknown',
            'program_id' => $programId,
            'player_ids' => json_encode($playerIds),
        ]);

        return response()->json(['message' => 'Payment status updated successfully.']);

    } catch (\Exception $e) {
        return response()->json(['error' => 'Error processing player registrations', 'details' => $e->getMessage()], 500);
    }

}

else{

    try {
        $playerIds = $validated['player_ids'];
        $programId = $validated['program_id'];
        $updateCount = 0;

        foreach ($playerIds as $playerId) {

            $amountForPerPlayerByCost = $user->calculateAmountForProgram($programId);

             PlayerProgram::create([
                'player_id' => $playerId,
                'program_id' => $programId,
            ]);

             TeamPlayer::create([
                'team_id'=>$validated['team_id'],
                'player_id'=>$playerId,
            ]);

          $playerInvitation =  PlayerInvitation::where('user_id', $playerId)->where('team_id', $validated['team_id'])->where('program_id', $programId)->first();

          $balanceDue=$validated['amount']-$validated['paidAmount'];


          $playerInvitation->update([
            'invitation_status'=>'accepted',
            'balance_due'=>$balanceDue
          ]);

            $updateCount++;
        }



        GuardianPayment::create([
            'user_id' => $user->id,
            'payment_id' => $validated['paymentMethodId'],
            'paid_amount' => $validated['paidAmount'],
            'pending_amount' => $validated['amount'] - $validated['paidAmount'],
            'created_at' => $validated['createdAt'],
            'payment_type' => ($validated['payment_type'] === 'fullAmount') ? 'full' : 'unknown',
            'program_id' => $programId,
            'player_ids' => json_encode($playerIds),
        ]);

        return response()->json(['message' => 'Payment status updated successfully.']);

    } catch (\Exception $e) {
        return response()->json(['error' => 'Error processing player registrations', 'details' => $e->getMessage()], 500);
    }



}
}





public function processPaymentForPaymentOptions(Request $request)
{
    Stripe::setApiKey(env('STRIPE_SECRET'));

    $user = auth()->user();

    $validated = $request->validate([
        'amount' => 'required',
        'payment_type' => 'required',
    ]);

    if ($validated['payment_type'] == 'split' || $validated['payment_type']=='full') {
        $pendingAmountSum = GuardianPayment::where('user_id', $user->id)
            ->where('payment_type', '!=', 'recurring')
            ->where('pending_amount', '>', 0)
            ->sum('pending_amount');

        $programIds = GuardianPayment::where('user_id', $user->id)
            ->where('payment_type', '!=', 'recurring')
            ->where('pending_amount', '>', 0)
            ->pluck('program_id');

         $playerIds = GuardianPayment::where('user_id', $user->id)
            ->where('payment_type', '!=', 'recurring')
            ->where('pending_amount', '>', 0)
            ->pluck('player_ids');
    }


    try {
        // Create the PaymentIntent
        $paymentIntent = PaymentIntent::create([
            'amount' => $validated['amount'] * 100,
            'currency' => 'usd',
            'metadata' => [
                'user_id' => $user->id,
                'payment_type' => $validated['payment_type'],
                'program_ids' => $programIds?->toJson(),
                'player_ids'=>$playerIds?->toJson(),
                'paid_amount'=>$validated['amount'],
                'town'=>$user->town??null,
            ],
        ]);

          $paymentIntent->metadata['payment_id'] = $paymentIntent->id;
          $paymentIntent->save();








        return response()->json([
            'clientSecret' => $paymentIntent->client_secret,
            'paymentId'=>$paymentIntent->id
        ]);
    } catch (ApiErrorException $e) {
        return response()->json([
            'error' => 'Stripe Error: ' . $e->getMessage(),
        ], 500);
    } catch (\Exception $e) {
        return response()->json([
            'error' => 'Payment failed: ' . $e->getMessage(),
        ], 500);
    }
}





public function success()
{
    return view('payment.success');
}

public function error()
{
    return view('payment.error');
}

protected function createStripeCustomer($user)
{
    $customer = Customer::create([
        'name' => "{$user->firstName} {$user->lastName}",
        'email' => $user->email,
        'phone' => $user->mobile_number ?? null,
        'description' => "Customer for {$user->email}",
    ]);
    $user->update(['stripe_customer_id' => $customer->id]);

    return $customer->id;
}


// {
//     $user = auth()->user();
//     Stripe::setApiKey(env('STRIPE_SECRET'));

//     try {
//         $customerId = $user->stripe_customer_id ?: $this->createStripeCustomer($user);

//         $paymentMethodId = $request->input('paymentMethodId');
//         $paymentMethod = \Stripe\PaymentMethod::retrieve($paymentMethodId);
//         $paymentMethod->attach(['customer' => $customerId]);

//         Customer::update($customerId, [
//             'invoice_settings' => [
//                 'default_payment_method' => $paymentMethodId,
//             ],
//         ]);

//         $paymentDate = new DateTime($request->input('paymentDate'));
//         $currentDate = new DateTime();


//         if ($paymentDate > $currentDate) {
//             $billingCycleAnchor = $paymentDate->getTimestamp();
//             $firstPaymentNow = false;
//         } else {
//             $billingCycleAnchor = $currentDate->getTimestamp();
//             $firstPaymentNow = true;
//         }


//         $productId = $this->createStripeProduct(
//             'Recurring Payment',
//             'Monthly subscription for recurring payments'
//         );

//         $subscription = Subscription::create([
//             'customer' => $customerId,
//             'items' => [[
//                 'price_data' => [
//                     'currency' => 'usd',
//                     'product' => $productId,
//                     'unit_amount' => $request->input('amount') * 100,
//                     'recurring' => ['interval' => 'month'],
//                 ],
//             ]],
//             'billing_cycle_anchor' => $billingCycleAnchor,
//             'proration_behavior' => 'none',
//             'payment_behavior' => 'default_incomplete',
//             'metadata' => [
//                 'number_of_payments' => $request->input('numberOfPayments'),
//                 'payment_date' => $paymentDate->format('Y-m-d'),
//             ],
//         ]);
//         if ($firstPaymentNow) {
//             $invoice = \Stripe\Invoice::create([
//                 'customer' => $customerId,
//                 'subscription' => $subscription->id,
//                 'auto_advance' => true,
//                 'billing_reason' => 'subscription_create',
//             ]);

//             $retrievedInvoice = \Stripe\Invoice::retrieve($invoice->id);
//             $retrievedInvoice->pay();
//         }

//         $this->scheduleSubscriptionCancellation(
//             $subscription->id,
//             $request->input('numberOfPayments'),
//             $paymentDate
//         );

//         return response()->json(['subscriptionId' => $subscription->id]);
//     } catch (\Exception $e) {
//         return response()->json(['error' => $e->getMessage()], 500);
//     }
// }

public function setupSubscriptionForPaymentOptions(Request $request)
{
    $user = auth()->user();
    Stripe::setApiKey(env('STRIPE_SECRET'));

    try {
        $customerId = $user->stripe_customer_id ?: $this->createStripeCustomer($user);
        $paymentMethodId = $request->input('paymentMethodId');
        if (!$paymentMethodId) {
            Log::error('Payment method ID is missing');
            throw new \Exception('Payment method ID is required.');
        }
        $paymentMethod = \Stripe\PaymentMethod::retrieve($paymentMethodId);
        $paymentMethod->attach(['customer' => $customerId]);

        Customer::update($customerId, [
            'invoice_settings' => [
                'default_payment_method' => $paymentMethodId,
            ],
        ]);

        $paymentDate = new DateTime($request->input('paymentDate'), new DateTimeZone('UTC'));
        $currentDate = new DateTime('now', new DateTimeZone('UTC'));

        $numPayments = $request->input('numberOfPayments');
        $endDate = clone $paymentDate;
        $endDate->modify('+' . ($numPayments - 1) . ' months');

        $currentDate = new DateTime('now', new DateTimeZone('UTC'));
            if ($endDate < $currentDate) {
            Log::error('The calculated end date is in the past. Process terminated.');
            return response()->json(['error' => 'Invalid end date: The calculated end date is in the past. Please provide a valid payment start date and number of payments.'], 400);
            }

                if ($paymentDate->format('Y-m-d') < $currentDate->format('Y-m-d')) {
            return response()->json([
                'success' => false,
                'message' => 'Payment date cannot be in the past',
            ], 400);
        }

        $billingCycleAnchor = ($paymentDate <= $currentDate)
            ? $currentDate->modify('+1 month')->getTimestamp()
            : $paymentDate->getTimestamp();

        $productId = $this->createStripeProduct(
            'Recurring Payment',
            'Monthly subscription for recurring payments'
        );

        if (!$productId) {
            Log::error('Failed to create or retrieve Stripe product');
            throw new \Exception('Failed to create or retrieve Stripe product.');
        }


   if ($request->input('payment_type') == 'recurring') {
    // Fetch pending amounts and other required data
       $guardianPayments = GuardianPayment::where('user_id', $user->id)
        ->where('payment_type', '=', 'split')
        ->where('pending_amount', '>', 0)
        ->select('program_id', 'player_ids', DB::raw('SUM(pending_amount) as total_pending_amount'))
        ->groupBy('program_id', 'player_ids')
        ->get();

    $pendingAmountSum = $guardianPayments->sum('total_pending_amount');

    $playerProgram = $guardianPayments->mapWithKeys(function ($item) {
        return [$item->program_id => explode(',', $item->player_ids)];
    });


    // Fetch pending amounts grouped by program_id
    $pendingAmountsByProgram = GuardianPayment::where('user_id', $user->id)
        ->where('payment_type', '=', 'split')
        ->where('pending_amount', '>', 0)
        ->groupBy('program_id')
        ->select('program_id', DB::raw('SUM(pending_amount) as total_pending_amount'))
        ->pluck('total_pending_amount', 'program_id');

}




        $subscription = Subscription::create([
            'customer' => $customerId,
            'items' => [[
                'price_data' => [
                    'currency' => 'usd',
                    'product' => $productId,
                    'unit_amount' => $request->input('amount') * 100,
                    'recurring' => ['interval' => 'month'],
                ],
            ]],
            'billing_cycle_anchor' => $billingCycleAnchor,
            'proration_behavior' => 'none',
            'payment_behavior' => 'default_incomplete',
            'metadata' => [
                'number_of_payments' => $numPayments,
                'start_date' => $paymentDate->format('Y-m-d'),
                'end_date' => $endDate->format('Y-m-d'),
                'user_id' => $user->id,
                'initial_amount_paid' => $request->input('amount'),
                'total_amount_due' => $request->input('totalAmount'),
                'payment_type' => 'Outstanding to recurring',
                'email' => $user->email,
                'address' => $request->input('address'),
                'state' => $request->input('state'),
                 'player_program'=>$playerProgram??null,
                'pendingAmountsByProgram'=>$pendingAmountsByProgram??null,
            ],
        ]);

                $subscription->metadata['subscription_id'] = $subscription->id ?: null;
                    $subscription->save();


            if ($paymentDate <= $currentDate) {
            try {
                \Stripe\InvoiceItem::create([
                    'customer' => $customerId,
                    'amount' => $request->input('amount') * 100,
                    'currency' => 'usd',
                    'description' => 'Initial payment for subscription',
                    'metadata' => [
                        'number_of_payments' => $numPayments,
                        'start_date' => $paymentDate->format('Y-m-d'),
                        'end_date' => $endDate->format('Y-m-d'),
                        'user_id' => $user->id,
                        'initial_amount_paid' => $request->input('amount'),
                        'total_amount_due' => $request->input('totalAmount'),
                        'payment_type' => 'Outstanding to recurring',
                        'email' => $user->email,
                        'address' => $request->input('address'),
                        'state' => $request->input('state'),
                        'player_program'=>$playerProgram??null,
                        'pendingAmountsByProgram'=>$pendingAmountsByProgram??null,
                        'town'=>$user->town??null,
                        'subscription_id'=>$subscription->id??null,

        ],
                ]);




                 $paymentIntent = PaymentIntent::create([
                        'amount' => $request->input('amount') * 100,
                        'currency' => 'usd',
                        'customer' => $customerId,
                    ]);

                $invoice = \Stripe\Invoice::create([
                    'customer' => $customerId,
                    'subscription' => $subscription->id,
                    'auto_advance' => true,
                          'metadata' => [
                        'number_of_payments' => $numPayments,
                        'start_date' => $paymentDate->format('Y-m-d'),
                        'end_date' => $endDate->format('Y-m-d'),
                        'user_id' => $user->id,
                        'initial_amount_paid' => $request->input('amount'),
                        'total_amount_due' => $request->input('totalAmount'),
                        'payment_type' => 'Outstanding to recurring',
                        'email' => $user->email,
                        'address' => $request->input('address'),
                        'state' => $request->input('state'),
                        'player_program'=>$playerProgram??null,
                        'pendingAmountsByProgram'=>$pendingAmountsByProgram??null,
                        'payment_id'=>$paymentIntent->id,
                        'town'=>$user->town??null,
                        'subscription_id'=>$subscription->id??null,

        ],

                ]);

                $retrievedInvoice = \Stripe\Invoice::retrieve($invoice->id);
                $retrievedInvoice->finalizeInvoice();
                $retrievedInvoice->pay();

            } catch (ApiErrorException $e) {
                Log::error('Failed to process the initial payment: ' . $e->getMessage());
                throw new \Exception('Failed to process the initial payment: ' . $e->getMessage());
            }
        }
        $this->scheduleSubscriptionCancellationforPaymentOption(
            $subscription->id,
            $endDate
        );

        return response()->json(['subscriptionId' => $subscription->id]);
    } catch (\Exception $e) {
        Log::error('Error setting up subscription: ' . $e->getMessage());
        Log::info('end date: ' . $endDate->format('Y-m-d'));
        return response()->json(['error' => $e->getMessage()], 500);
    }
}










protected function scheduleSubscriptionCancellationforPaymentOption($subscriptionId, DateTime $endDate)
{
    try {

        $subscription = Subscription::retrieve($subscriptionId);

        $subscription->cancel_at = $endDate->getTimestamp();
        $subscription->save();
    } catch (\Exception $e) {
        throw $e;
    }
}


public function recurringPaymentStatusForPaymentOptions(Request $request)
{
    try {
        $user = auth()->user();
        $validated = $request->validate([
            'subscriptionId' => 'required|string',
            'address'=>'required|string',
            'state'=>'required|string',
            'status' => 'required|string',
            'amount' => 'required|numeric',
            'totalAmount' => 'required|numeric',
            'paymentDate' => 'required|date',
            'numberOfPayments' => 'required|integer',
            'paymentMethodId' => 'required|string',
            'createdAt' => 'required|date',
        ]);

        $guardianPayments = GuardianPayment::where('user_id', $user->id)
            ->get();

        $totalBalanceDue = $guardianPayments->sum('pending_amount');


      $paymentDate = new DateTime($validated['paymentDate'], new DateTimeZone('UTC'));
        $currentDate = new DateTime('now', new DateTimeZone('UTC'));


         if ($paymentDate->format('Y-m-d') == $currentDate->format('Y-m-d')){
        foreach ($guardianPayments as $payment) {
            $payment->update(['pending_amount' => 0.00]);

        }
    }
    else{


try {
     foreach ($guardianPayments as $payment) {
            $payment->update(['pending_amount' => 0.00]);

        }


    $end_date_timestamp = strtotime("+" . ($validated['numberOfPayments'] - 1) . " months", strtotime($validated['paymentDate']));
    $end_date = date('Y-m-d', $end_date_timestamp);


    $outStandingRecurringPayments = DB::table('outstanding_recurring_payments')->insert([
        'user_id' => $user->id,
        'email' => $user->email,
        'stripe_subscription_id' => $validated['subscriptionId'],
        'initial_amount_paid' => 0.00,
        'amount_paid' => 0.00,
        'start_date' => $validated['paymentDate'],
        'end_date' => $end_date,
        'total_amount_due' => $validated['totalAmount'],
        'payment_type' => 'recurring',
        'number_of_payments' => $validated['numberOfPayments'],
        'created_at' => now(),
    ]);

} catch (\Exception $e) {

    Log::error('Failed to insert outstanding recurring payment record.', [
        'error_message' => $e->getMessage(),
        'user_id' => $user->id ?? null,
        'validated_data' => $validated,
    ]);
    throw $e;
}

    }

        return response()->json([
            'success' => true,
            'message' => 'Recurring payment status updated successfully',
        ]);
    } catch (\Exception $e) {
        return response()->json([
            'success' => false,
            'message' => 'An error occurred while processing the recurring payment: ' . $e->getMessage(),
        ], 500);
    }
}




public function setupRecurringPayments(Request $request)
{
    $user = auth()->user();
    Stripe::setApiKey(env('STRIPE_SECRET'));

    $playerIds = $request->input('player_ids');
    $playerIdsString = json_encode($playerIds);

    try {
        // Create Stripe Customer if not already exists
        $customerId = $user->stripe_customer_id ?: $this->createStripeCustomer($user);

        // Attach the payment method to the customer
        $paymentMethodId = $request->input('paymentMethodId');
        $paymentMethod = \Stripe\PaymentMethod::retrieve($paymentMethodId);
        $paymentMethod->attach(['customer' => $customerId]);

        // Set the default payment method for the customer
        \Stripe\Customer::update($customerId, [
            'invoice_settings' => [
                'default_payment_method' => $paymentMethodId,
            ],
        ]);

        // Calculate the total and the paid amount
        $totalAmountToBePaid = $request->input('totalAmountToBePaid');
        $paidAmount = $request->input('paidAmount');
        $numberOfPayments = ceil($totalAmountToBePaid / $paidAmount);
        $productId = $this->createStripeProduct(
            'Recurring Payment',
            'Monthly subscription for recurring payments'
        );

        // Dates for subscription
        $startDate = time();
        $numPayments = $numberOfPayments - 1;
        $endDate = strtotime("+$numPayments months", $startDate);

        $initialInvoiceAmount = $paidAmount * 100;

        // Create the Stripe Subscription
        $subscription = \Stripe\Subscription::create([
            'customer' => $customerId,
            'items' => [[
                'price_data' => [
                    'currency' => 'usd',
                    'product' => $productId,
                    'unit_amount' => $paidAmount * 100,
                    'recurring' => ['interval' => 'month'],
                ],
            ]],
            'proration_behavior' => 'none',
            'payment_behavior' => 'allow_incomplete',
            'metadata' => [
                'user_id' => $user->id,
                'card_holder' => $request->input('cardHolderName'),
                'address' => $request->input('address'),
                'state' => $request->input('state'),
                'program_id' => $request->input('program_id'),
                'payment_type' => $request->input('payment_type'),
                'player_ids' => $playerIdsString,
                'town' => $user->town ?? null,
                'team_id' => $request->input('team_id') ?: null,
                'email' => $request->input('email'),
                'number_of_payments' => $numberOfPayments,
                'initial_paid_amount' => $paidAmount,
                'total_amount_due' => $totalAmountToBePaid,
                'start_date' => date('Y-m-d', $startDate),
                'end_date' => date('Y-m-d', $endDate),
            ],
        ]);

        // If the invoice amount is greater than 0, create the invoice
        if ($initialInvoiceAmount > 0) {
            // Create the invoice
            $invoice = \Stripe\Invoice::create([
                'customer' => $customerId,
                'subscription' => $subscription->id,
                'auto_advance' => true,  // Auto-finalize the invoice
                'metadata' => [
                    'user_id' => $user->id,
                    'program_id' => $request->input('program_id'),
                    'payment_type' => $request->input('payment_type'),
                    'player_ids' => $playerIdsString,
                    'town' => $user->town ?? null,
                    'team_id' => $request->input('team_id') ?: null,
                    'email' => $request->input('email'),
                    'paid_amount' => $paidAmount,
                    'payment_id' => $request->input('paymentMethodId'),
                ],
            ]);

            // Create the PaymentIntent explicitly for the invoice
            $paymentIntent = \Stripe\PaymentIntent::create([
                'amount' => $initialInvoiceAmount,
                'currency' => 'usd',
                'customer' => $customerId,
                'payment_method' => $paymentMethodId,
                'off_session' => true,  // Off-session for recurring payments
                'confirm' => true,
                 'metadata' => [
                    'user_id' => $user->id,
                    'program_id' => $request->input('program_id'),
                    'payment_type' => $request->input('payment_type'),
                    'player_ids' => $playerIdsString,
                    'town' => $user->town ?? null,
                    'team_id' => $request->input('team_id') ?: null,
                    'email' => $request->input('email'),
                    'paid_amount' => $paidAmount,
                    'payment_id' => $request->input('paymentMethodId'),
                ],
            ]);
        }

        // Retrieve the invoice and attempt to pay it if the amount is due
        $retrievedInvoice = \Stripe\Invoice::retrieve($invoice->id);
        if ($retrievedInvoice->amount_due > 0) {
            $retrievedInvoice->pay();
        }

        // Schedule cancellation after the last payment
        $this->scheduleSubscriptionCancellation(
            $subscription->id,
            new DateTime("@$endDate")
        );

        return response()->json([
            'subscriptionId' => $subscription->id,
            'start_date' => date('Y-m-d', $startDate),
            'number_of_payments' => $numberOfPayments,
            'end_date' => date('Y-m-d', $endDate),
        ]);
    } catch (\Exception $e) {
        return response()->json(['error' => $e->getMessage()], 500);
    }
}








protected function scheduleSubscriptionCancellation($subscriptionId,  DateTime $endDate)
{
    $cancellationDate = clone $endDate;

    Subscription::update($subscriptionId, [
        'cancel_at' => $cancellationDate->getTimestamp(),
    ]);
}




public function createStripeProduct($name, $description = null)
{

    Stripe::setApiKey(env('STRIPE_SECRET'));

    try {

        $product = \Stripe\Product::create([
            'name' => $name,
            'description' => $description,
            'type' => 'service',
        ]);

        return $product->id;
    } catch (\Exception $e) {

        return response()->json(['error' => $e->getMessage()], 500);
    }
}


// public function recurringPaymentStatus(Request $request){

//     $user=auth()->user();

//     $validated = $request->validate([
//             'subscriptionId' => 'required|string',
//             'status' => 'required|string',
//             'amount' => 'required|numeric',
//             'paymentDate' => 'required|date',
//             'numberOfPayments' => 'required|integer',
//             'paymentMethodId' => 'required|string',
//             'playerIds' => 'required|array',
//         'playerIds.*' => 'integer',
//         'programId' => 'required|integer',
//             'createdAt' => 'required|date',
//         ]);



//                 $playerIds = $validated['playerIds'];
//                 $programId = $validated['programId'];

//     $updateCount=0;

//         foreach ($playerIds as $playerId) {

//         $amountForPerPlayerByCost = $user->calculateAmountForProgram($programId);


//     PlayerProgram::create([
//         'player_id' => $playerId,
//         'program_id' => $programId,
//     ]);


//     $existingRegistration = ProgramRegistration::where('user_id', $user->id)
//         ->where('player_id', $playerId)
//         ->where('program_id', $programId)
//         ->first();

//     if ($existingRegistration) {

//         $existingRegistration->update([
//             'is_paid' => true,
//         ]);
//     } else {

//         ProgramRegistration::create([
//             'user_id' => $user->id,
//             'program_id' => $programId,
//             'amount' => $amountForPerPlayerByCost,
//             'player_id' => $playerId,
//             'is_paid' => true,
//         ]);
//     }

//     $updateCount++;
// }

//     if ($updateCount > 0) {
//         return response()->json(['message' => 'Payment status updated successfully.']);
//     } else {
//         return response()->json(['error' => 'Failed to update payment status. No records found.'], 500);
//     }
//     }



public function recurringPaymentDetails(Request $request)
{
    try {
        $user = auth()->user();

        $validated = $request->validate([
            'subscriptionId' => 'required|string',
            'payment_type' => 'required|string',
            'status' => 'required|string',
            'amount' => 'required|numeric',
            'paymentMethodId' => 'required|string',
            'paidAmount' => 'required|numeric',
            'playerIds' => 'required|array',
            'playerIds.*' => 'integer',
            'programId' => 'required|integer',
            'createdAt' => 'required|date',
            'team_id' => 'nullable'
        ]);

    } catch (\Illuminate\Validation\ValidationException $e) {
        return response()->json(['error' => 'Validation error', 'details' => $e->errors()], 422);
    }

     if (!isset($validated['team_id'])) {

    try {
        $playerIds = $validated['playerIds'];
        $programId = $validated['programId'];
        $updateCount = 0;

        foreach ($playerIds as $playerId) {
            $amountForPerPlayerByCost = $user->calculateAmountForProgram($programId);

            PlayerProgram::create([
                'player_id' => $playerId,
                'program_id' => $programId,
            ]);

            $existingRegistration = ProgramRegistration::where('user_id', $user->id)
                ->where('player_id', $playerId)
                ->where('program_id', $programId)
                ->first();

            if ($existingRegistration) {
                $existingRegistration->update(['is_paid' => true]);
            } else {
                ProgramRegistration::create([
                    'user_id' => $user->id,
                    'program_id' => $programId,
                    'amount' => $amountForPerPlayerByCost,
                    'player_id' => $playerId,
                    'is_paid' => true,
                ]);
            }

            $updateCount++;
        }
    } catch (\Exception $e) {
        return response()->json(['error' => 'Error processing player registrations', 'details' => $e->getMessage()], 500);
    }
     }

  else{

     $playerIds = $validated['playerIds'];
        $programId = $validated['programId'];
        $updateCount = 0;

        foreach ($playerIds as $playerId) {
            $amountForPerPlayerByCost = $user->calculateAmountForProgram($programId);

            PlayerProgram::create([
                'player_id' => $playerId,
                'program_id' => $programId,
            ]);

             TeamPlayer::create([
                'team_id'=>$validated['team_id'],
                'player_id'=>$playerId,
            ]);

          $playerInvitation =  PlayerInvitation::where('user_id', $playerId)->where('team_id', $validated['team_id'])->where('program_id', $programId)->first();

          $balanceDue=$validated['amount']-$validated['paidAmount'];


          $playerInvitation->update([
            'invitation_status'=>'accepted',
            'balance_due'=>$balanceDue
          ]);

  }
   $updateCount++;

}


    try {
        $totalAmount = $validated['amount'];
        $paidAmount = $validated['paidAmount'];
        $pendingAmount = $totalAmount - $paidAmount;
        if($validated['payment_type'] === 'recurringPayments'){
                $paymentType = 'recurring';
        }
        elseif($validated['payment_type']==='split'){
            $paymentType="split";
        }

        GuardianPayment::create([
            'user_id' => $user->id,
            'payment_id' => $validated['paymentMethodId'],
            'paid_amount' => $paidAmount,
            'pending_amount' => $pendingAmount,
            'created_at' => $validated['createdAt'],
            'payment_type' => $paymentType,
            'program_id' => $programId,
            'player_ids' => json_encode($playerIds),
        ]);
    } catch (\Exception $e) {
        return response()->json(['error' => 'Error saving payment details', 'details' => $e->getMessage()], 500);
    }

    return $updateCount > 0
        ? response()->json(['message' => 'Payment status updated successfully.'])
        : response()->json(['error' => 'Failed to update payment status. No records found.'], 500);
}



public function specificAmountPayment(Request $request)
{
     $user = auth()->user();

    try {
        Stripe::setApiKey(env('STRIPE_SECRET'));
        $user = auth()->user();

        $validated = $request->validate([
            'payment_type' => 'required|string',
            'amount' => 'required|numeric',
            'paymentMethodId' => 'required|string',
            'paidAmount' => 'required|numeric',
            'player_ids' => 'required|array',
            'player_ids.*' => 'integer',
            'program_id' => 'required|integer',
            'createdAt' => 'required|date',
            'cardHolderName' => 'required|string',
            'state' => 'required|string',
            'address' => 'required|string',
            'team_id' => 'nullable'
        ]);


        $paymentIntent = PaymentIntent::create([
            'amount' => $validated['paidAmount'] * 100,
            'currency' => 'usd',
            'payment_method' => $validated['paymentMethodId'],
            'confirm' => true,
            'description' => 'Program payment for user ID: ' . $user->id,
            'metadata' => [
               'cardHolderName' => $validated['cardHolderName'],
                'address' => $validated['address'],
                'state' => $validated['state'],
                'user_id' => $user->id,
                'program_id' => $validated['program_id'],
                'player_ids' => json_encode($validated['player_ids']),
                'payment_type'=>$validated['payment_type'],
                'paid_amount'=>$validated['paidAmount'],
                'town'=>$user->town??null,
                'email'=>$user->email,
                'payment_id'=>$validated['paymentMethodId']
            ],
             'automatic_payment_methods' => [
                'enabled' => true,
                'allow_redirects' => 'never',
            ],
        ]);

        if ($paymentIntent->status === 'requires_action') {
            return response()->json([
                'requires_action' => true,
                'payment_intent_client_secret' => $paymentIntent->client_secret,
            ]);
        }


         if (!isset($validated['team_id']))
         {
     try {
        $playerIds = $validated['player_ids'];
        $programId = $validated['program_id'];
        $updateCount = 0;

        foreach ($playerIds as $playerId) {

            $amountForPerPlayerByCost = $user->calculateAmountForProgram($programId);


            PlayerProgram::create(['player_id' => $playerId, 'program_id' => $programId]);


            ProgramRegistration::updateOrCreate(
                [
                    'user_id' => $user->id,
                    'player_id' => $playerId,
                    'program_id' => $programId,
                ],
                ['is_paid' => true, 'amount' => $amountForPerPlayerByCost]
            );

            $updateCount++;
        }
        GuardianPayment::create([
            'user_id' => $user->id,
            'payment_id' => $validated['paymentMethodId'],
            'paid_amount' => $validated['paidAmount'],
            'pending_amount' => $validated['amount'] - $validated['paidAmount'],
            'created_at' => $validated['createdAt'],
            'payment_type' => ($validated['payment_type'] === 'specificAmount') ? 'split' : 'unknown',
            'program_id' => $programId,
            'player_ids' => json_encode($playerIds),
        ]);

        return response()->json(['message' => 'Payment status updated successfully.']);

    } catch (\Exception $e) {
        return response()->json(['error' => 'Error processing player registrations', 'details' => $e->getMessage()], 500);
    }
} else{


      try {
        $playerIds = $validated['player_ids'];
        $programId = $validated['program_id'];
        $updateCount = 0;

        foreach ($playerIds as $playerId) {

            $amountForPerPlayerByCost = $user->calculateAmountForProgram($programId);


            PlayerProgram::create(['player_id' => $playerId, 'program_id' => $programId]);

            TeamPlayer::create([
                'team_id'=>$validated['team_id'],
                'player_id'=>$playerId,
            ]);



          $playerInvitation =  PlayerInvitation::where('user_id', $playerId)->where('team_id', $validated['team_id'])->where('program_id', $programId)->first();

          $balanceDue=$validated['amount']-$validated['paidAmount'];


          $playerInvitation->update([
            'invitation_status'=>'accepted',
            'balance_due'=>$balanceDue
          ]);

            $updateCount++;
        }



        GuardianPayment::create([
            'user_id' => $user->id,
            'payment_id' => $validated['paymentMethodId'],
            'paid_amount' => $validated['paidAmount'],
            'pending_amount' => $validated['amount'] - $validated['paidAmount'],
            'created_at' => $validated['createdAt'],
            'payment_type' => ($validated['payment_type'] === 'specificAmount') ? 'split' : 'unknown',
            'program_id' => $programId,
            'player_ids' => json_encode($playerIds),
        ]);

        return response()->json(['message' => 'Payment status updated successfully.']);

    } catch (\Exception $e) {
        return response()->json(['error' => 'Error processing player registrations', 'details' => $e->getMessage()], 500);
    }

}

    } catch (\Exception $e) {
        return response()->json(['error' => 'Payment processing error', 'details' => $e->getMessage()], 500);
    }
}




public function acceptInvitePayment(Request $request)
{
    Stripe::setApiKey(env('STRIPE_SECRET'));

    try {
        $validated = $request->validate([
            'paymentMethodId' => 'required|string',
            'amount' => 'required|numeric|min:1',
            'coach' => 'required|integer|exists:users,id',
            'team' => 'required|integer|exists:teams,id',
            'program' => 'required|integer|exists:programs,id',
            'user' => 'required|integer|exists:users,id',
        ]);
    } catch (\Illuminate\Validation\ValidationException $e) {
        return response()->json([
            'success' => false,
            'message' => 'Validation failed.',
            'errors' => $e->errors(),
        ], 422);
    }

    try {
        $amountInCents = $validated['amount'] * 100;

   $paymentIntent = PaymentIntent::create([
            'amount' => $amountInCents,
            'currency' => 'usd',
            'payment_method' => $validated['paymentMethodId'],
            'confirm' => true,
            'automatic_payment_methods' => [
                'enabled' => true,
                'allow_redirects' => 'never',  // Prevent redirects
            ],
        ]);;

        $guardian = auth()->user();

        $playerInvitation = PlayerInvitation::where('coach_id', $validated['coach'])
            ->where('user_id', $validated['user'])
            ->where('program_id', $validated['program'])
            ->firstOrFail();

        $balanceDue = $validated['amount'] - $playerInvitation->balance_due;

        $playerInvitation->update([
            'balance_due' => $balanceDue,
            'invitation_status' => 'accepted',
        ]);

        GuardianPayment::create([
            'user_id' => $guardian->id,
            'payment_id' => $paymentIntent->id,
            'paid_amount' => $validated['amount'],
            'pending_amount' => $balanceDue,
        ]);

        return response()->json([
            'success' => true,
            'paymentIntent' => $paymentIntent,
            'redirectUrl' => route('payment.success'),
        ]);
    } catch (\Exception $e) {
        return response()->json([
            'success' => false,
            'message' => 'Payment processing failed: ' . $e->getMessage(),
        ], 400);
    }
}


//  public function storeRecurringPayments(Request $request)
// {
//     Stripe::setApiKey(env('STRIPE_SECRET'));
//     $payload = $request->getContent();
//     $sigHeader = $request->header('Stripe-Signature');
//     $endpointSecret = env('STRIPE_WEBHOOK_SECRET');

//     try {
//         $event = Webhook::constructEvent($payload, $sigHeader, $endpointSecret);
//         if ($event->type == 'invoice.payment_succeeded') {
//             $invoice = $event->data->object;
//             $metadata = $invoice->lines->data[0]->metadata ?? null;

//             if ($metadata) {
//                 $playerIds = json_encode($metadata['player_ids'] ?? []);
//                 $paymentRecord = DB::table('recurring_payments')
//                     ->where('user_id', $metadata['user_id'] ?? null)
//                     ->where('program_id', $metadata['program_id'] ?? null)
//                     ->where('team_id', $metadata['team_id'] ?? null)
//                     ->where('email', $metadata['email'] ?? null)
//                     ->whereJsonContains('player_ids', $playerIds)
//                     ->first();

//                 if ($paymentRecord) {
//                     DB::table('recurring_payments')
//                         ->where('id', $paymentRecord->id)
//                         ->update([
//                             'paid_amount' => DB::raw("paid_amount + {$invoice->amount_paid} / 100"),
//                             'updated_at' => now(),
//                         ]);
//                 } else {
//                     DB::table('recurring_payments')->insert([
//                         'user_id' => $metadata['user_id'] ?? null,
//                         'card_holder' => $metadata['card_holder'] ?? null,
//                         'address' => $metadata['address'] ?? null,
//                         'state' => $metadata['state'] ?? null,
//                         'program_id' => $metadata['program_id'] ?? null,
//                         'player_ids' => $playerIds,
//                         'team_id' => $metadata['team_id'] ?? null,
//                         'email' => $metadata['email'] ?? null,
//                         'number_of_payments' => $metadata['number_of_payments'] ?? null,
//                         'initial_paid_amount' => $metadata['initial_paid_amount'] ?? null,
//                         'total_amount_due' => $metadata['total_amount_due'] ?? null,
//                         'paid_amount' => $invoice->amount_paid / 100,
//                         'start_date' => $metadata['start_date'] ?? null,
//                         'end_date' => $metadata['end_date'] ?? null,
//                         'created_at' => now(),
//                         'updated_at' => now(),
//                     ]);
//                 }
//             } else {
//                 Log::error('No metadata found in the invoice', ['invoice' => $invoice]);
//             }
//         }
//         return response()->json(['status' => 'success']);
//     } catch (SignatureVerificationException $e) {

//         Log::error('Webhook signature verification failed', ['error' => $e->getMessage()]);
//         return response()->json(['error' => 'Webhook signature verification failed'], 400);
//     } catch (\Exception $e) {
//         Log::error('Error processing webhook', ['error' => $e->getMessage()]);
//         return response()->json(['error' => $e->getMessage()], 500);
//     }
// }


public function subscriptionCreated(Request $request){

    return response()->json([
        'success'=>true,
        'message'=>'recurring payments setted successfully'
    ]);

}



public function storeRecurringPayments(Request $request)
{
    Stripe::setApiKey(env('STRIPE_SECRET'));
    $payload = $request->getContent();
    $sigHeader = $request->header('Stripe-Signature');
    $endpointSecret = env('STRIPE_WEBHOOK_SECRET');

    try {
        $event = Webhook::constructEvent($payload, $sigHeader, $endpointSecret);

         //TODO: it will trigger when first payment is made but when next month payment will come the event will be payment succeeded
          //it will save the one time payment efficiently but we also have to store recurring payments here so we will think something
          //to fix it.
        if ($event->type === 'invoice.created') {
            Log::info('event is invoice created');
            $invoice = $event->data->object; // Stripe\Invoice object
            $metadata = $invoice->metadata;
            if(!$metadata){
                Log::info('no metadata is in invoice created');
            }


           if ($metadata && $metadata['payment_type'] === 'Outstanding to recurring') {

           $paymentId = $metadata['payment_id'] ?? $invoice->payment_intent;

            $pendingAmountsByProgram = json_decode($metadata['pendingAmountsByProgram'], true);
            $playerProgram = json_decode($metadata['player_program'], true);
            $initialAmountPaid = (float)$metadata['initial_amount_paid'];
            $remainingAmount = $initialAmountPaid;

    foreach ($pendingAmountsByProgram as $programId => $pendingAmount) {
        $pendingAmount = (float)$pendingAmount;
        $amountToDeduct = min($remainingAmount, $pendingAmount);
        $remainingAmount -= $amountToDeduct;

        $playerIds = $playerProgram[$programId] ?? [];

        $playerIds = array_map(function ($id) {
            return trim($id, '[]"');
        }, $playerIds);

        foreach ($playerIds as $playerId) {
            DB::table('all_payments')->insert([
                'user_id' => $metadata['user_id'],
                'payment_id' => $paymentId,
                'town'=>$metadata['town']??null,
                'paid_amount' => $amountToDeduct,
                'program_ids' => json_encode([$programId]),
                'player_ids' => json_encode([$playerId]),
                'payment_type' => $metadata['payment_type'],
                'created_at' => now(),
                'updated_at' => now(),
            ]);
        }
        if ($remainingAmount <= 0) {
            break;
        }
    }
}

            Log::info('Invoice Metadata:',  ['metadata'=>$metadata]);
        }



       if ($event->type == "charge.succeeded") {

        Log::info('Event Object', ['event' => $event]);

        $invoice = $event->data->object;
         $paymentIntent = $invoice->payment_intent;
        $metadata = $invoice->metadata ?? $invoice->lines->data[0]->metadata ?? null;
         Log::info('metadata is coming properly', ['metadata' => $metadata]);

        if(!$metadata){
            $charge = $event->data->object;
            Log::info('Charge object', ['charge' => $charge]);
            $metadata = $charge->metadata ?? null;
        }

   if ($metadata) {

    // Just changing the payment type to keep consistency in the DB
    if (isset($metadata['payment_type'])) {
        $metadata['payment_type'] = match ($metadata['payment_type']) {
            'fullAmount' => 'full',
            'specificAmount' => 'split',
            'recurringPayments' => 'recurring',
            default => $metadata['payment_type'],
        };
    }
    if (isset($metadata['affected_payments'])) {
        $affectedPayments = json_decode($metadata['affected_payments'], true);


        foreach ($affectedPayments as $payment) {

            $programId = $payment['program_id'];
            $playerIds = $payment['player_ids'];
            $amount = $payment['amount'];


            foreach ($playerIds as $playerId) {
                DB::table('all_payments')->insert([
                    'user_id' => $metadata['user_id'],
                    'payment_id' => $metadata['payment_id'] ?? null,
                    'paid_amount' => $amount,
                    'program_ids' => json_encode([$programId]),
                    'player_ids' => json_encode([$playerId]),
                    'town' => $metadata['town'] ?? null,
                    'payment_type' => $metadata['payment_type'] ?? null,
                    'created_at' => now(),
                    'updated_at' => now(),
                ]);
            }
        }
    } else if(isset($metadata['payment_type']) && $metadata['payment_type']!="Outstanding to recurring") {
          Log::info('inserting into all payments table', ['metdata'=>$metadata]);
        DB::table('all_payments')->insert([
            'user_id' => $metadata['user_id'],
            'payment_id' => $metadata['payment_id'] ?? null,
            'paid_amount' => $metadata['paid_amount'] ?? 0,
            'program_ids' => isset($metadata['program_id']) ? json_encode($metadata['program_id']) : null,
            'player_ids' => isset($metadata['player_ids']) ? json_encode($metadata['player_ids']) : null,
            'town' => $metadata['town'] ?? null,
            'payment_type' => $metadata['payment_type'] ?? null,
            'created_at' => now(),
            'updated_at' => now(),
        ]);
    }

} else {
    Log::error('No metadata found for this event');
}

        }

        if ($event->type == 'invoice.payment_succeeded') {
             Log::info('payment successfull');
             $invoice = $event->data->object;
             $paymentIntent = $invoice->payment_intent;
                      $invoice = $event->data->object;
                         $metadata = $invoice->lines->data[0]->metadata ?? $invoice->metadata ?? null;

              Log::info('payment is successfull', ['metadata'=>$metadata]);

            if ($metadata) {
                Log::info('Metadata found in the invoice', ['metadata' => $metadata]);
                if ($metadata['payment_type'] === 'Outstanding to recurring') {
                    Log::info('Payment type is outstanding to recurring', ['metadata' => $metadata]);
                    // Handle outstanding recurring payments
                    $paymentRecord = DB::table('outstanding_recurring_payments')
                        ->where('user_id', $metadata['user_id'] ?? null)
                        ->where('stripe_subscription_id', $metadata['subscription_id'])
                        ->first();

                    if ($paymentRecord) {
                        Log::info('Payment record found, updating...', ['payment_record' => $paymentRecord]);

                        DB::table('outstanding_recurring_payments')
                            ->where('id', $paymentRecord->id)
                            ->update([
                                'initial_amount_paid' => $metadata['initial_amount_paid'] ?? null,
                                'amount_paid' => DB::raw("amount_paid + {$metadata['initial_amount_paid']}"),
                                'updated_at' => now(),
                            ]);
                    } else {
                        Log::info('No payment record found, inserting new record...', ['metadata' => $metadata]);

                        DB::table('outstanding_recurring_payments')->insert([
                            'user_id' => $metadata['user_id'] ?? null,
                            'address' => $metadata['address'] ?? null,
                            'state' => $metadata['state'] ?? null,
                            'email' => $metadata['email'] ?? null,
                            'number_of_payments' => $metadata['number_of_payments'] ?? null,
                            'initial_amount_paid' => $metadata['initial_amount_paid'] ?? null,
                            'total_amount_due' => $metadata['total_amount_due'] ?? null,
                            'amount_paid' => DB::raw("amount_paid + {$metadata['initial_amount_paid']}"),
                            'payment_type' => 'recurring',
                            'start_date' => $metadata['start_date'] ?? null,
                            'end_date' => $metadata['end_date'] ?? null,
                             'stripe_customer_id'=>$metadata['customer_id']??null,
                             'stripe_subscription_id'=>$metadata['subscription_id']??null,
                            'created_at' => now(),
                            'updated_at' => now(),
                        ]);
                    }
                } else {
                    // Handle recurring payments
                    $playerIds = json_encode($metadata['player_ids'] ?? []);
                    $paymentRecord = DB::table('recurring_payments')
                        ->where('user_id', $metadata['user_id'] ?? null)
                        ->where('program_id', $metadata['program_id'] ?? null)
                        ->where('team_id', $metadata['team_id'] ?? null)
                        ->where('email', $metadata['email'] ?? null)
                        ->whereJsonContains('player_ids', $playerIds)
                        ->first();

                    if ($paymentRecord) {
                        DB::table('recurring_payments')
                            ->where('id', $paymentRecord->id)
                            ->update([
                                'paid_amount' => DB::raw("paid_amount + {$invoice->amount_paid} / 100"),
                                'updated_at' => now(),
                            ]);
                    } else {
                     if (!isset($metadata['user_id']) || empty($metadata['user_id'])) {
                            Log::error('User ID is missing in metadata:', ['metadata' => $metadata]);


                            if ($metadata instanceof \Stripe\StripeObject) {
                                Log::info('Complete metadata:', $metadata->toArray());
                            } else {
                                Log::info('Complete metadata:', $metadata);
                            }
                        }


                        DB::table('recurring_payments')->insert([
                            'user_id' => $metadata['user_id'] ?? null,
                            'card_holder' => $metadata['card_holder'] ?? null,
                            'address' => $metadata['address'] ?? null,
                            'state' => $metadata['state'] ?? null,
                            'program_id' => $metadata['program_id'] ?? null,
                            'player_ids' => $playerIds,
                            'team_id' => $metadata['team_id'] ?? null,
                            'email' => $metadata['email'] ?? null,
                            'number_of_payments' => $metadata['number_of_payments'] ?? null,
                            'initial_paid_amount' => $metadata['initial_paid_amount'] ?? null,
                            'total_amount_due' => $metadata['total_amount_due'] ?? null,
                            'paid_amount' => $invoice->amount_paid / 100,
                            'start_date' => $metadata['start_date'] ?? null,
                            'end_date' => $metadata['end_date'] ?? null,
                            'created_at' => now(),
                            'updated_at' => now(),
                        ]);
                    }
                }
            } else {
                Log::error('No metadata found in the invoice', ['invoice' => $invoice]);
            }
        }

        return response()->json(['status' => 'success']);
    } catch (SignatureVerificationException $e) {
        Log::error('Webhook signature verification failed', ['error' => $e->getMessage()]);
        return response()->json(['error' => 'Webhook signature verification failed'], 400);
    } catch (\Exception $e) {
        Log::error('Error processing webhook', ['error' => $e->getMessage()]);
        return response()->json(['error' => $e->getMessage()], 500);
    }
}


}






