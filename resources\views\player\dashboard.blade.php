@extends('layouts.app')

@section('title', 'dashboard')

@section('content')
    <section class="page_title text-center pt-5">
        <h1 class="text-uppercase lh-1 mb-0">Welcome {{ $player->firstName }} {{ $player->lastName }}</h1>
        @if ($errors->any())
            <div class="bg-red-500 text-white p-4 mb-4">
                <ul>
                    @foreach ($errors->all() as $error)
                        <li>{{ $error }}</li>
                    @endforeach
                </ul>
            </div>
        @endif

        @if (session('success'))
            <div class="alert alert-success" id="successAlert" role="alert" style="max-width: 600px; margin: 0 auto;">
                {{ session('success') }}
            </div>
        @endif
    </section>


    <section class="sec partition-hr">
        <div class="container">
            <hr class="border-navy opacity-100 my-0" />
        </div>
    </section>
    <section class="sec program-table">
        <div class="container">
            <div class="heading text-center mb-5">
                <h2 class="text-uppercase fs-6 mb-0">My PROGRAMS:</h2>
            </div>
            @if ($currentPrograms->isEmpty())
                <p class="text-center">NO PROGRAMS JOINED YET.</p>
            @else
                <div class="table-program table-responsive">
                    <table class="table table-hover" width="100%">
                        <tbody>
                            <tr>
                                <th>Name</th>
                                <th>Sub Program</th>
                                <th>Start Date</th>
                                <th>Day of the Week</th>
                                <th>Individual Price</th>
                                <th>Location</th>

                            </tr>
                            @foreach ($currentPrograms as $program)
                                <tr>
                                    <td class="py-4" valign="middle"> <a
                                            href="{{ route('program.show', ['program' => Crypt::encrypt($program->id)]) }}">{{ $program->name }}</a>
                                    </td>
                                    <td class="py-4"valign="middle">{{ $program->sub_program }}</td>
                                    <td class="py-4" valign="middle">
                                        {{ \Carbon\Carbon::parse($program->start_date)->format('m-d-Y') }}
                                    </td>
                                    <td class="py-4" valign="middle">
                                        {{ is_array($program->frequency_days) ? implode(', ', $program->frequency_days) : $program->frequency_days }}
                                    </td>

                                    <td class="py-4"valign="middle">${{ number_format($program->cost, 2) }}</td>
                                    <td class="py-4" valign="middle">{{ $program->location }}</td>

                                </tr>
                            @endforeach
                        </tbody>
                    </table>
                    {{-- <div class="mt-6">
                    {{ $playerPrograms->links('pagination::tailwind') }}
                </div> --}}
                </div>
            @endif
        </div>
    </section>


    <section class="sec partition-hr">
        <div class="container">
            <hr class="border-navy opacity-100 my-0" />
        </div>
    </section>
    <section class="sec program-table">
        <div class="container">
            <div class="heading text-center mb-5">
                <h2 class="text-uppercase fs-6 mb-0">My Teams:</h2>
            </div>
            @if ($playerTeams->isEmpty())
                <p class="text-center">NO TEAMS JOINED YET.</p>
            @else
                <div class="table-program table-responsive">
                    <table class="table table-hover" width="100%">
                        <tbody>
                            <tr>
                                <th>Name</th>
                                {{-- <th>Sub Program</th>
                            <th>Start Date</th>
                            <th>Day of the Week</th>
                            <th>Individual Price</th>
                            <th>Location</th> --}}

                            </tr>
                            @foreach ($playerTeams as $team)
                                <tr>
                                    <td class="py-4" valign="middle">{{ $team }}</td>
                                </tr>
                            @endforeach
                        </tbody>
                    </table>
                    {{-- <div class="mt-6">
                    {{ $playerPrograms->links('pagination::tailwind') }}
                </div> --}}
                </div>
            @endif
        </div>
    </section>

    <section class="sec program-table">
        <div class="container">
            <div class="heading text-center mb-5">
                <h2 class="text-uppercase fs-6 mb-0">PAST TEAMS, CAMPS & CLINICS:</h2>
            </div>
            @if ($pastPrograms->isEmpty())
                <p class="text-center">NO PAST PROGRAMS.</p>
            @else
                <div class="table-program table-responsive">
                    <table class="table table-hover" width="100%">
                        <tbody>

                            @foreach ($pastPrograms as $program)
                                <tr>
                                    <td class="py-4" valign="middle"> <a
                                            href="{{ route('program.show', ['program' => Crypt::encrypt($program->id)]) }}">{{ $program->name }}</a>
                                    </td>
                                    <td class="py-4" valign="middle"><a href="#">{{ $program->sub_program }}</a>
                                    </td>
                                    <td class="py-4" valign="middle">{{ $program->start_date }}</td>
                                    <td class="py-4" valign="middle">
                                        {{ is_array($program->frequency_days) ? implode(', ', $program->frequency_days) : $program->frequency_days }}
                                    </td>
                                    <td class= "py-4" valign="middle">${{ number_format($program->cost, 2) }}</td>
                                    <td class="py-4" valign="middle">{{ $program->location }}</td>
                                </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>
        </div>
        @endif
    </section>
@endsection
