@extends('layouts.app')
@section('title', 'Tryout Programs')

<style>
    /* Custom Select2 Styling */
    .select2-container {
        width: 100% !important;
    }

    .select2-container .select2-selection--single {
        box-sizing: border-box;
        cursor: pointer;
        display: block;
        height: 43px;
        user-select: none;
        -webkit-user-select: none;
    }

    .select2-container--default .select2-selection--single {
        background-color: #d9d9d9;
        border-radius: 14px;
        height: 43px;
        border: none;
        padding: 10px 15px;
        font: 500 16px/1 "Montserrat", sans-serif;
    }

    .select2-container--default .select2-selection--single .select2-selection__rendered {
        color: #000;
        line-height: 24px;
    }

    .select2-container--default .select2-selection--single .select2-selection__arrow {
        height: 43px;
        right: 10px;
    }

    .select2-container--default .select2-selection--single .select2-selection__arrow b {
        border-color: #062e69 transparent transparent transparent;
    }

    .select2-dropdown {
        background-color: #fff;
        border: 1px solid #d9d9d9;
        border-radius: 14px;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    }

    .select2-container--default .select2-search--dropdown .select2-search__field {
        border: 1px solid #d9d9d9;
        border-radius: 8px;
        padding: 8px;
    }

    .select2-container--default .select2-results__option--highlighted[aria-selected] {
        background-color: #0b4499;
        color: white;
    }

    .select2-container--default .select2-results__option {
        padding: 8px 12px;
        font-family: "Montserrat", sans-serif;
    }

    .select2-container--default .select2-selection--single .select2-selection__placeholder {
        color: #666;
    }

    /* Fix for Select2 inside Bootstrap modal */
    .modal-open .select2-container--open .select2-dropdown {
        z-index: 1056;
        /* Higher than modal backdrop */
    }

    /* Modal Button Styling */
    #create-team-button {
        margin: 0 auto;
        min-width: 200px;
    }

    .select2-container .select2-selection--single {
        box-sizing: border-box;
        cursor: pointer;
        display: block;
        height: 43px !important;
        user-select: none;
        -webkit-user-select: none
    }

    /* Modal Form Styling */
    #team-create-modal .modal-content {
        border-radius: 20px;
        border: none;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
    }

    #team-create-modal .modal-body {
        padding: 2rem;
    }

    #team-create-modal .form-label {
        margin-bottom: 0.875rem;
        color: #c1b05c;
        font-weight: 900;
        letter-spacing: 2px;
    }

    #team-create-modal .heading h2 {
        color: #062e69;
        font-weight: 900;
        letter-spacing: 2px;
    }

    #team-create-modal .invalid-feedback-admin {
        font-size: 0.875rem;
        margin-top: 0.25rem;
    }

    /* Responsive fixes */
    @media (max-width: 767px) {
        #team-create-modal .modal-dialog {
            margin: 0.5rem;
            max-width: calc(100% - 1rem);
        }

        #team-create-modal .modal-body {
            padding: 1.5rem;
        }

        #create-team-button {
            width: 100%;
        }
    }

    /* Spinning animation for loading states */
    .spin {
        animation: spin 1s linear infinite;
    }

    @keyframes spin {
        from { transform: rotate(0deg); }
        to { transform: rotate(360deg); }
    }
</style>

@section('content')

    <section class="page_title text-center pt-5">
        <h1 class="text-uppercase lh-1 mb-0">Programs</h1>
    </section>

    @if (session('success'))
        <div id="successMessageForSession">
            <span id="successText">{{ session('success') }}</span>
        </div>
    @endif

    @if (session('error'))
        <div id="errorMessageForSession">
            <span id="errorText">{{ session('error') }}</span>
        </div>
    @endif



    <section class="sec partition-hr">
        <a class="cta mb-5 ms-5" href="{{ route('admin.program.allPrograms') }}">Back</a>
        {{-- <button id="add-the-team" class="cta-button-popup" style="position:absolute; right:2rem;">Create Team</button> --}}

        <div class="container">
            <hr class="border-navy opacity-100 my-0" />
        </div>
    </section>

    <section class="sec program-table">

        @livewire('admin.tryout-programs')

    </section>





@endsection
