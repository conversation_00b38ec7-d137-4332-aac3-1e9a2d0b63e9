@extends('layouts.app')

@section('title', 'Manage Teams')

@section('content')
    <section class="page_title text-center pt-5">
        <h1 class="text-uppercase lh-1 mb-0">Welcome {{ $user->firstName }} {{ $user->lastName }}</h1>
    </section>
    <section class="sec program-welcome">
        <div class="container">
            <div class="heading align-items-center d-flex flex-column text-center mb-5"><span
                    class="hero-bar d-inline-flex mb-5"></span></div>
            <div class="row player-meta justify-content-center">
                <div class="heading text-center">
                    <h2 class="fs-6 text-uppercase mb-3">Information</h2>
                </div>
                <div class="col-md-6">
                    <div class="player-meta__box d-flex flex-column text-center align-items-center justify-content-center">
                        <p>
                            {{ $user->firstName }} {{ $user->lastName }} <br />

                            {{ $user->email }}
                        </p>
                        <div class="edit d-flex" data-coach-id="{{ $user->id }}" id="editButton"><img
                                src="{{ asset('images/edit-icon.svg') }}" alt="" width="20" height="20"
                                id="editIcon-{{ $user->id }}"
                                data-loader-coach-url="{{ asset('images/ripple-loading.svg') }}" />
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
    <section class="sec partition-hr">
        <div class="container">
            <hr class="border-navy opacity-100 my-0" />
        </div>
    </section>
    <section class="sec program-table">
        <div class="container">
            <div class="heading text-center mb-5">
                <h2 class="text-uppercase fs-6 mb-0">add team</h2>
            </div>
            <div class="table-program table-responsive">
                <table class="table table-hover" width="100%">
                    <tbody>
                        <tr>
                            <th>Team Name</th>
                            <th>First Name</th>
                            <th>Last Name</th>
                            <th>Email</th>
                            <th>Invite Status</th>
                            <th width="100"></th>
                        </tr>


                        @foreach ($invitedCoaches as $invitation)
                            <tr>
                                <td class="py-6" valign="middle">
                                    <a href="#">{{ $invitation->team->name }}</a>
                                </td>
                                <td class="py-4" valign="middle">
                                    {{ $invitation->user->firstName }}
                                </td>
                                <td class="py-4" valign="middle">
                                    {{ $invitation->user->lastName }}
                                </td>
                                <td class="py-4" valign="middle">
                                    {{ $invitation->user->email }}
                                </td>
                                <td class="py-4" valign="middle">
                                    {{ $invitation->invitation_status }}
                                </td>
                                <td class="py-4" valign="middle">
                                    <div class="cta-row">
                                        <a class="cta hover-dark"
                                            href="{{ route('coach.dashboard', ['team' => $invitation->team->id]) }}">Manage
                                            your team</a>
                                    </div>
                                </td>
                            </tr>
                        @endforeach

                        @foreach (@$coachTeams as $team)
                            <tr>
                                <td class="py-6" valign="middle">
                                    <a href="#">{{ @$team->team->name }}</a>
                                </td>
                                <td class="py-4" valign="middle">
                                    <a href="#"></a>
                                </td>
                                <td class="py-4" valign="middle">

                                </td>
                                <td class="py-4" valign="middle">

                                </td>
                                <td class="py-4" valign="middle">

                                </td>
                                <td class="py-4" valign="middle">
                                    <div class="cta-row">
                                        <a class="cta hover-dark"
                                            href="{{ route('coach.dashboard', ['team' => @$team->team->id]) }}">Manage
                                            your team</a>
                                    </div>
                                </td>


                            </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>
            <div class="cta-row text-center mt-4"><button class="cta" id="addNewTeamButton">Add a new team</button></div>
        </div>
    </section>

    <!-- adding new Team modal -->

    <div class="modal fade" id="teamModal" tabindex="-1" aria-labelledby="teamModalLabel" aria-hidden="true"
        data-bs-backdrop="static">
        <div class="modal-dialog modal-dialog-centered" style="max-width:640px;">
            <div class="modal-content">
                <div class="modal-body p-5 form">
                    <span class="modal-close" data-bs-dismiss="modal">&#x2715;</span>
                    <div id="teamMessage" class="alert d-none" role="alert"></div>
                    <div class="mb-4">
                        <label class="form-label text-uppercase" for="newTeamName">Enter Team Name</label>
                        <input type="text" class="form-control" id="newTeamName" name="newTeamName" required>
                        <div class="invalid-feedback" id="teamNameError"></div>
                    </div>
                    <div class="d-flex align-items-center justify-content-end" style="min-height: 40px">
                        <button type="button" class="cta" id="createTeamButton">Create New Team</button>
                    </div>
                </div>
            </div>
        </div>
    </div>




    <!--coach edit modal -->

    <div class="modal fade" id="coachEdit" tabindex="-1" aria-labelledby="coachEditLabel" aria-hidden="true"
        data-bs-backdrop="static">
        <div class="modal-dialog modal-dialog-centered" style="max-width:640px;">
            <div class="modal-content">
                <div class="modal-body p-5">
                    <span class="modal-close" data-bs-dismiss="modal">&#x2715;</span>

                    <form id="editCoachData" class="form row">

                        @csrf
                        <input type="hidden" name="coach_id" id="coachIdInput">
                        <div id="editCoachSuccessMessage" class="alert alert-success d-none" role="alert"></div>
                        <div id="editCoachErrorMessage" class="alert alert-danger d-none" role="alert"></div>
                        <div class="mb-4 col-md-12">
                            <label class="form-label text-uppercase" for="addFirstName">First Name</label>
                            <input class="form-control" id="editCoachFirstName" type="text" name="firstName" />
                            <div class="invalid-feedback" id="addFirstNameError"></div>
                        </div>
                        <div class="mb-4 col-md-12">
                            <label class="form-label text-uppercase" for="addLastName">Last Name</label>
                            <input class="form-control" id="editCoachLastName" type="text" name="lastName" />
                            <div class="invalid-feedback" id="addLastNameError"></div>
                        </div>
                        <div class="mb-4 col-md-12">
                            <label class="form-label text-uppercase" for="editEmail">Email</label>
                            <input class="form-control" id="editCoachEmail" type="text" name="email" />
                            <div class="invalid-feedback" id="addEmailError"></div>
                        </div>
                        <div class="col-md-12 d-flex align-items-center justify-content-end" style="min-height: 40px">
                            <button class="cta" id="editCoachButton">Save Changes</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>






    @yield('js')
    <script>
        // edit coach
        document.querySelectorAll(".player-meta .edit[data-coach-id]").forEach((item) => {
            item.addEventListener("click", function() {
                const coachId = this.getAttribute("data-coach-id");
                let url = route("coach.coach.edit", {
                    id: coachId
                });

                const editButton = document.getElementById('editButton');
                let editIcon = document.getElementById(`editIcon-${coachId}`);
                let loaderUrl = editIcon.getAttribute("data-loader-coach-url");

                const originalIconSrc = editIcon.src;
                editIcon.src = loaderUrl;

                fetch(url)
                    .then((response) => response.json())
                    .then((data) => {
                        if (data.success) {
                            editIcon.src = originalIconSrc;
                            document.getElementById("coachIdInput").value = data.coach.id;
                            document.getElementById("editCoachFirstName").value = data.coach
                                .firstName;
                            document.getElementById("editCoachLastName").value = data.coach
                                .lastName;
                            document.getElementById("editCoachEmail").value = data.coach
                                .email;
                            const modal = new bootstrap.Modal(document.getElementById(
                                "coachEdit"));
                            modal.show();
                        } else {
                            console.error("Failed to load coach data.");
                            editIcon.src = originalIconSrc;
                        }
                    });
            });
        });

        document.getElementById("editCoachButton").addEventListener("click", function(event) {
            event.preventDefault();
            const formData = new FormData(document.getElementById("editCoachData"));

            let url = route("coach.coach.update");

            fetch(url, {
                    method: "POST",
                    headers: {
                        "X-CSRF-TOKEN": "{{ csrf_token() }}",
                        "X-Requested-With": "XMLHttpRequest",
                    },
                    body: formData,
                })
                .then((response) => response.json())
                .then((data) => {
                    if (data.success) {
                        document.getElementById("editCoachSuccessMessage").textContent =
                            "Coach updated successfully!";
                        document.getElementById("editCoachSuccessMessage").classList.remove(
                            "d-none");
                        document.getElementById("editCoachFirstName").value = "";

                        document.getElementById("editCoachLastName").value = "";

                        document.getElementById("editCoachEmail").value = "";

                        setTimeout(() => {
                            document.getElementById("editCoachSuccessMessage").classList
                                .add("d-none");
                            const modal = bootstrap.Modal.getInstance(document
                                .getElementById("coachEdit"));
                            modal.hide();
                            window.location.reload();
                        }, 2000);
                    } else {
                        document.getElementById("editCoachErrorMessage").textContent =
                            "Failed to update Coach.";
                        document.getElementById("editCoachErrorMessage").classList.remove("d-none");
                    }
                });
        });



        // add new team
        const addNewTeamButton = document.getElementById('addNewTeamButton');

        addNewTeamButton.addEventListener('click', function() {
            const teamModal = new bootstrap.Modal(document.getElementById('teamModal'));
            const newTeamNameInput = document.getElementById('newTeamName');
            newTeamNameInput.value = "";
            const teamMessage = document.getElementById('teamMessage');
            teamMessage.classList.add('d-none');
            teamMessage.textContent = '';
            teamModal.show();

        });


        const createTeamButton = document.getElementById('createTeamButton');

        createTeamButton.addEventListener('click', function() {
            const newTeamNameInput = document.getElementById('newTeamName');
            const newTeamName = newTeamNameInput.value;
            const teamMessage = document.getElementById('teamMessage');
            const teamNameError = document.getElementById('teamNameError');
            teamMessage.classList.add('d-none');
            teamMessage.textContent = '';
            teamNameError.textContent = '';


            if (!newTeamName.trim()) {
                teamNameError.textContent = 'Team name is required.';
                return;
            }


            const url = route('coach.createNewTeam');


            fetch(url, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')
                            .getAttribute('content')
                    },
                    body: JSON.stringify({
                        newTeamName
                    })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        teamMessage.classList.remove('d-none', 'alert-danger');
                        teamMessage.classList.add('alert-success');
                        teamMessage.textContent = data.message;
                        newTeamNameInput.value = '';
                        setTimeout(() => {
                            teamMessage.classList.add('d-none');
                        }, 3000);

                    } else {
                        teamMessage.classList.remove('d-none');
                        teamMessage.classList.add('alert-danger');
                        teamMessage.textContent = data.message;
                        setTimeout(() => {
                            teamMessage.classList.add('d-none');
                        }, 3000);
                    }

                })
                .catch(error => {
                    teamMessage.classList.remove('d-none', 'alert-success');
                    teamMessage.classList.add('alert-danger');
                    teamMessage.textContent =
                        'An error occurred while creating the team. Please try again later.';
                    console.error('Error:', error);
                    setTimeout(() => {
                        teamMessage.classList.add('d-none');
                    }, 3000)
                });
        });
    </script>

@endsection
