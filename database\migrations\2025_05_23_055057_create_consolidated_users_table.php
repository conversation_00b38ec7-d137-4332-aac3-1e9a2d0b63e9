<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Drop the old users table if it exists
        if (Schema::hasTable('users')) {
            Schema::dropIfExists('users');
        }

        // Create the new consolidated users table
        Schema::create('users', function (Blueprint $table) {
            $table->id();
            $table->string('firstName');
            $table->string('lastName');
            $table->string('slug')->unique();
            $table->string('email')->nullable()->unique();
            $table->string('mobile_number')->nullable();
            $table->string('password')->nullable();
            $table->string('birthDate')->nullable();
            $table->integer('age')->nullable();
            $table->string('grade')->nullable();
            $table->string('gender')->nullable();
            $table->string('address')->nullable();
            $table->string('street')->nullable();
            $table->string('town')->nullable();
            $table->string('state')->nullable();
            $table->string('program')->nullable();
            $table->string('teamName')->nullable();
            $table->string('parent_id')->nullable();
            $table->string('primary_parent_id')->nullable();
            $table->boolean('is_joined')->default(false);
            $table->boolean('is_guardian')->default(false);
            $table->boolean('is_coach')->default(false);
            $table->string('current_role')->nullable();
            $table->string('stripe_customer_id')->nullable();
            $table->string('profilePhoto')->nullable();
            $table->timestamp('email_verified_at')->nullable();
            $table->rememberToken();
            $table->timestamps();
        });

        Schema::create('password_reset_tokens', function (Blueprint $table) {
            $table->string('email')->primary();
            $table->string('token');
            $table->timestamp('created_at')->nullable();
        });

        Schema::create('sessions', function (Blueprint $table) {
            $table->string('id')->primary();
            $table->foreignId('user_id')->nullable()->index();
            $table->string('ip_address', 45)->nullable();
            $table->text('user_agent')->nullable();
            $table->longText('payload');
            $table->integer('last_activity')->index();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('users');
        Schema::dropIfExists('password_reset_tokens');
        Schema::dropIfExists('sessions');
    }
};
