@extends('layouts.app')

@section('title', 'Sign up')

@section('content')
    <section class="page_title text-center pt-5">
        <h1 class="text-uppercase lh-1 mb-0">Sign up</h1>
    </section>
    <section class="sec form mb-5">
        <div class="container">
            <div class="heading align-items-center d-flex flex-column text-center mb-5">
                <span class="hero-bar d-inline-flex"></span>
            </div>
            <div class="row justify-content-center">
                <div class="col-xl-10">
                    @if (session('error'))
                        <div class="alert alert-error" role="alert">
                            {{ session('error') }}
                        </div>
                    @endif
                    <form class="row" method="POST" action="{{ route('guardian.signup') }}">
                        @csrf

                        <div class="mb-4 col-md-6">
                            <label class="form-label text-uppercase" for="first-name">FIRST NAME</label>
                            <input class="form-control" type="text" id="first-name" name="firstName"
                                value="{{ old('firstName', @$additionalGuardian->firstName ?? '') }}" />
                            <!-- Laravel Validation Error -->
                            @if ($errors->has('firstName'))
                                <span class="text-danger">{{ $errors->first('firstName') }}</span>
                            @endif
                            <!-- Client-side Error -->
                            <div class="text-danger d-none" id="firstName-Error"></div>
                        </div>


                        <div class="mb-4 col-md-6">
                            <label class="form-label text-uppercase" for="last-name">LAST NAME</label>
                            <input class="form-control" type="text" id="last-name" name="lastName"
                                value="{{ old('lastName', @$additionalGuardian->lastName ?? '') }}" />
                            @if ($errors->has('lastName'))
                                <span class="text-danger">{{ $errors->first('lastName') }}</span>
                            @endif
                            <div class="text-danger d-none" id="lastName-Error"></div>
                        </div>


                        <div class="mb-4 col-md-6">
                            <label class="form-label text-uppercase" for="email">EMAIL</label>
                            <input class="form-control" type="email" id="email" name="email"
                                value="{{ old('email', @$additionalGuardian->email ?? (@$invitation->guardian_email ?? '')) }}" />
                            @if ($errors->has('email'))
                                <span class="text-danger">{{ $errors->first('email') }}</span>
                            @endif
                            <div class="text-danger d-none" id="email-Error"></div>
                        </div>

                        <div class="mb-4 col-md-6 position-relative" style="min-height: 80px;">
                            <label class="form-label text-uppercase" for="password">PASSWORD</label>
                            <div class="position-relative">
                                <input class="form-control" type="password" id="password" name="password" />
                                <div id="eyeToggle" class="eye-icon closed">
                                    <div class="eye-shape">
                                        <div class="pupil"></div>
                                        <div class="eyelid"></div>
                                    </div>
                                </div>
                            </div>

                            <div id="passwordStrengthIndicator" class="progress mt-2 d-none"
                                style="height: 8px; opacity: 0; transition: opacity 0.3s;">
                                <div id="passwordStrengthBar" class="progress-bar" role="progressbar" style="width: 0%;">
                                </div>
                            </div>

                            <small id="passwordStrengthText" class="text-muted d-none"
                                style="opacity: 0; transition: opacity 0.3s;"></small>

                            <div class="invalid-feedback" id="passwordErrorMessage"></div>

                            @if ($errors->has('password'))
                                <span class="text-danger">{{ $errors->first('password') }}</span>
                            @endif
                        </div>

                        <div class="mb-4 col-md-12">
                            <label class="form-label text-uppercase" for="mobile-number">Phone</label>
                            <input class="form-control" type="text" id="mobile-number" name="mobile_number"
                                value="{{ old('mobile_number', @$additionalGuardian->mobile_number ?? '') }}" />
                            @if ($errors->has('mobile_number'))
                                <span class="text-danger">{{ $errors->first('mobile_number') }}</span>
                            @endif
                            <div class="text-danger d-none" id="mobile_number-Error"></div>
                        </div>



                        <div class="mb-4 col-md-12">
                            <input type="checkbox" id="makeCoachCheckbox" class="styled-checkbox" name="makeCoach" />
                            <label for="makeCoachCheckbox" class="form-label text-uppercase ms-2">I am also a coach</label>
                        </div>


                        <div id="coachDetails" class="d-none">
                            <div class="mb-4 col-md-12">
                                <label class="form-label text-uppercase" for="guardianTown">Town</label>
                                <input class="form-control" id="guardianTown" type="text" name="town" />
                                @if ($errors->has('town'))
                                    <span class="text-danger">{{ $errors->first('town') }}</span>
                                @endif
                                <div class="text-danger d-none" id="guardianTown-Error"></div>
                            </div>

                            <div class="mb-4 col-md-12">
                                <label class="form-label text-uppercase" for="guardianTeam">Team Name</label>
                                <input class="form-control" id="guardianTeam" type="text" name="team" />
                                @if ($errors->has('team'))
                                    <span class="text-danger">{{ $errors->first('team') }}</span>
                                @endif
                                <div class="text-danger d-none" id="guardianTeam-Error"></div>
                            </div>
                        </div>


                        <div class="col-md-6 d-flex justify-content-end mt-5">
                            <button class="cta py-0" type="submit">SIGN UP</button>
                        </div>
                        <div class="col-md-6 d-flex mt-5">
                            <a class="cta py-0" href="{{ route('loginPage') }}">ALREADY A MEMBER? LOGIN HERE</a>
                        </div>
                    </form>

                </div>
            </div>
        </div>
    </section>

    @yield('js')
    <script>
        const makeCoachCheckbox = document.getElementById("makeCoachCheckbox");
        const coachDetails = document.getElementById("coachDetails");

        makeCoachCheckbox.addEventListener("change", () => {
            if (makeCoachCheckbox.checked) {
                coachDetails.classList.remove("d-none");
                coachDetails.classList.add("show");
            } else {
                coachDetails.classList.remove("show");
                coachDetails.classList.add("d-none");
            }
        });

        const form = document.querySelector('form');
        const firstName = document.getElementById('first-name');
        const lastName = document.getElementById('last-name');
        const emailField = document.getElementById('email');
        const passwordField = document.getElementById('password');
        const mobileField = document.getElementById('mobile-number');
        const townField = document.getElementById('guardianTown');
        const teamField = document.getElementById('guardianTeam');

        form.addEventListener('submit', function(event) {
            event.preventDefault();
            if (validateForm()) {
                form.submit();
            }
        });

        function validateForm() {
            let isValid = true;
            if (firstName.value.trim() == '') {
                showError(firstName, 'First name cannot be empty');
                isValid = false;
            } else {
                hideError(firstName);
            }


            if (lastName.value.trim() == '') {
                showError(lastName, 'Last name cannot be empty');
                isValid = false;
            } else {
                hideError(lastName);
            }

            if (emailField.value.trim() == '') {
                showError(emailField, 'Email cannot be empty');
                isValid = false;
            } else if (!validateEmail(emailField.value)) {
                showError(emailField, 'Please enter a valid email address');
                isValid = false;
            } else {
                hideError(emailField);
            }

            if (passwordField.value.trim() == '') {
                showError(passwordField, 'Password cannot be empty');
                isValid = false;
            } else if (passwordField.value.length < 6) {
                showError(passwordField, 'Password must be at least 6 characters');
                isValid = false;
            } else {
                hideError(passwordField);
            }
            if (mobileField.value.trim() === '') {
                showError(mobileField, 'Mobile number cannot be empty');
                isValid = false;
            } else if (!/^\d{10}$/.test(mobileField.value.trim())) {
                showError(mobileField, 'Mobile number must be exactly 10 digits');
                isValid = false;
            } else {
                hideError(mobileField);
            }

            if (makeCoachCheckbox.checked) {
                if (!townField.classList.contains('d-none') && townField.value.trim() === '') {
                    showError(townField, 'Town cannot be empty');
                    isValid = false;
                } else {
                    hideError(townField);
                }
                if (!teamField.classList.contains('d-none') && teamField.value.trim() === '') {
                    showError(teamField, 'Team name cannot be empty');
                    isValid = false;
                } else {
                    hideError(teamField);
                }
            }
            return isValid;
        }

        function validateEmail(email) {
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            return emailRegex.test(email);
        }

        function showError(input, message) {
            const errorElement = input.nextElementSibling;
            const passwordErrorElement = document.getElementById('passwordErrorMessage');

            if (input === passwordField) {
                passwordErrorElement.textContent = message;
                passwordErrorElement.style.display = 'block';
            } else {
                errorElement.textContent = message;
                errorElement.classList.remove('d-none');
                input.classList.add('is-invalid');
            }

            setTimeout(() => {
                hideErrorTimeOut(errorElement, input);
            }, 2000);
        }

        function hideErrorTimeOut(errorElement, input) {
            if (input === passwordField) {
                const passwordErrorElement = document.getElementById('passwordErrorMessage');
                passwordErrorElement.textContent = '';
                passwordErrorElement.style.display = 'none';
            } else {
                errorElement.textContent = '';
                errorElement.classList.add('d-none');
                input.classList.remove('is-invalid');
            }
        }

        function hideError(input) {
            const errorElement = input.nextElementSibling;
            const passwordErrorElement = document.getElementById('passwordErrorMessage');

            if (input === passwordField) {
                passwordErrorElement.style.display = 'none';
            } else {
                errorElement.classList.add('d-none');
                input.classList.remove('is-invalid');
            }
        }




        document.getElementById('password').addEventListener('input', function() {
            var passwordStrengthIndicator = document.getElementById('passwordStrengthIndicator');
            var passwordStrengthText = document.getElementById('passwordStrengthText');
            var strengthBar = document.getElementById('passwordStrengthBar');
            var password = this.value;

            if (password.length > 0) {
                passwordStrengthIndicator.classList.remove("d-none");
                passwordStrengthText.classList.remove("d-none");
                passwordStrengthIndicator.style.opacity = 1;
                passwordStrengthText.style.opacity = 1;
            } else {

                passwordStrengthIndicator.classList.add("d-none");
                passwordStrengthText.classList.add("d-none");
                passwordStrengthIndicator.style.opacity = 0;
                passwordStrengthText.style.opacity = 0;
                return;
            }


            let strength = '';
            let strengthClass = '';
            let width = 0;


            if (password.length < 6) {
                strength = 'Too short';
                strengthClass = 'too-short';
                width = 20;
            } else if (password.length < 8) {
                strength = 'Weak';
                strengthClass = 'weak';
                width = 40;
            } else if (password.length >= 8 && /[A-Z]/.test(password) && /\d/.test(password) && /[\W_]/.test(
                    password)) {
                strength = 'Strong';
                strengthClass = 'strong';
                width = 100;
            } else {
                strength = 'Medium';
                strengthClass = 'medium';
                width = 70;
            }


            strengthBar.style.width = `${width}%`;
            strengthBar.className = `progress-bar ${strengthClass}`;


            passwordStrengthText.textContent = `Password strength: ${strength}`;
        });

        document.getElementById("eyeToggle").addEventListener("mouseenter", function() {
            let passwordField = document.getElementById("password");
            let eyeIcon = document.getElementById("eyeToggle");

            if (passwordField.type === "password") {
                eyeIcon.setAttribute("title", "Show Password");
            } else {
                eyeIcon.setAttribute("title", "Hide Password");
            }
        });

        const eyeIcon = document.getElementById('eyeToggle');


        eyeIcon.addEventListener('click', function() {
            if (passwordField.type === 'password') {
                passwordField.type = 'text';
                eyeIcon.classList.remove('closed');
                eyeIcon.classList.add('opened');
                eyeIcon.setAttribute("title", "Hide Password");
            } else {
                passwordField.type = 'password';
                eyeIcon.classList.remove('opened');
                eyeIcon.classList.add('closed');
                eyeIcon.setAttribute("title", "Show Password");
            }
        });
    </script>
@endsection
