@extends('layouts.app')

@section('title', 'Forgot Password')

@section('content')
<section class="page_title text-center pt-5">
  <h1 class="text-uppercase lh-1 mb-0">Forgot Password</h1>
</section>
<section class="sec form mb-5">
  <div class="container">
    <div class="heading align-items-center d-flex flex-column text-center mb-5">
      <span class="hero-bar d-inline-flex"></span>
    </div>
    <div class="row justify-content-center">
      <div class="col-xl-6">
        @if (session('success'))
        <div class="alert alert-success" role="alert">
          {{session('success')}}
        </div>
        @endif
        <form class="row" action="{{ route('forgotPassword') }}" method="POST">
          @csrf
          <div class="mb-4 col-md-12">
            <label class="form-label text-uppercase" for="email">Email</label>
            <input class="form-control" id="email" type="email" name="email" />
            @if ($errors->any())
            @foreach ($errors->all() as $error)
            <span class="text-danger">{{ $error }}</span>
            @endforeach
            @endif
          </div>
          <div class="col-md-6 mt-5 d-flex justify-content-end">
            <button class="cta py-0">Send Password Reset Link</button>
          </div>
          <div class="col-md-6 mt-5 d-flex">
            <a class="cta py-0" href="{{ route('loginPage') }}">Back to login</a>
          </div>
        </form>
      </div>
    </div>
  </div>
</section>
@endsection