@extends('layouts.app')
@section('title', 'Admin')
@section('content')
    <section class="page_title text-center pt-5">
        <h1 class="text-uppercase lh-1 mb-0"><br>Report</h1>

        @if (session('success'))
            <div id="successMessageForSession">
                <span id="successText">{{ session('success') }}</span>
            </div>
        @endif

        @if (session('error'))
            <div id="errorMessageForSession">
                <span id="errorText">{{ session('error') }}</span>
            </div>
        @endif
    </section>


    <section class="sec admin-welcome">
        <div class="container">

            <div>
                <a class="cta" href="{{ route('admin.reports') }}">Back</a>


            </div>
            <div class="heading align-items-center d-flex flex-column text-center mb-5">
                <span class="hero-bar d-inline-flex"></span>
            </div>

            <form class="form row table-filter justify-content-center" id="filterForm">
                <div class="col-md-auto">
                    <div class="filter-option">
                        <div class="mb-4">
                            <label class="form-label text-uppercase" for="sport-filter">Filter By</label>
                            <div class="select-arrow position-relative">
                                <select class="form-control" id="sport-filter" name="sport-filter">
                                    <option value="" {{ request('sport-filter') == '' ? 'selected' : '' }}>Sport
                                    </option>
                                    <option value="basketball"
                                        {{ request('sport-filter') == 'basketball' ? 'selected' : '' }}>
                                        Basketball</option>
                                    <option value="volleyball"
                                        {{ request('sport-filter') == 'volleyball' ? 'selected' : '' }}>
                                        Volleyball</option>
                                    <option value="pickleball"
                                        {{ request('sport-filter') == 'pickleball' ? 'selected' : '' }}>
                                        Pickleball</option>
                                </select>
                                <span class="arrow"><i class="bi bi-chevron-down"></i></span>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-md-auto">
                    <div class="filter-option">
                        <div class="mb-4">
                            <label class="form-label text-uppercase" for="sport-filter">Filter By</label>
                            <div class="select-arrow position-relative">
                                <select class="form-control" id="age-filter" name="age-filter">
                                    <option value="" {{ request('age-filter') == '' ? 'selected' : '' }}>Age Group
                                    </option>
                                    <option value="adult" {{ request('age-filter') == 'adult' ? 'selected' : '' }}>Adult
                                    </option>
                                    <option value="u18" {{ request('age-filter') == 'u18' ? 'selected' : '' }}>U18
                                    </option>
                                    <option value="u16" {{ request('age-filter') == 'u16' ? 'selected' : '' }}>U16
                                    </option>
                                    <option value="u14" {{ request('age-filter') == 'u14' ? 'selected' : '' }}>U14
                                    </option>
                                    <option value="u12" {{ request('age-filter') == 'u12' ? 'selected' : '' }}>U12
                                    </option>
                                    <option value="u10" {{ request('age-filter') == 'u10' ? 'selected' : '' }}>U10
                                    </option>
                                    <option value="u8" {{ request('age-filter') == 'u8' ? 'selected' : '' }}>U8
                                    </option>
                                </select>
                                <span class="arrow"><i class="bi bi-chevron-down"></i></span>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-md-auto">
                    <div class="filter-option">
                        <div class="mb-4">
                            <label class="form-label text-uppercase" for="date-range-filter">Select Date Range</label>
                            <div class="d-flex">
                                <input type="date" class="form-control me-2" id="start-date" name="start-date"
                                    value="{{ request('start-date') }}" placeholder="Start Date">
                                <span class="text-muted" style="align-self: center;">to</span>
                                <input type="date" class="form-control ms-2" id="end-date" name="end-date"
                                    value="{{ request('end-date') }}" placeholder="End Date">
                            </div>
                        </div>
                    </div>
                </div>




                <div class="col-md-auto">
                    <div class="filter-option">
                        <div class="mb-4">
                            <label class="form-label text-uppercase" for="search-input">Search</label>
                            <div class="select-arrow position-relative">
                                <input class="form-control" id="search-input" name="search-input" type="text"
                                    placeholder="Search" />
                            </div>
                        </div>
                    </div>
                </div>
            </form>


            <div id="programs-container">

                @include('admin.reports.partialProgramReport')

            </div>

            <div id="pagination-container" class="mt-4 d-flex justify-content-center pagination">
                {{ $programs->links('pagination::bootstrap-5') }}
            </div>

            <!--search team -->

            <div class="modal fade" id="searchTeamModal" tabindex="-1" aria-labelledby="searchTeamLabel" aria-hidden="true"
                data-bs-backdrop="static">
                <div class="modal-dialog modal-dialog-centered" style="max-width: 1160px">
                    <div class="modal-content">
                        <div class="modal-body p-5">
                            <span class="modal-close" data-bs-dismiss="modal">&#x2715;</span>
                            <form class="form mb-5" id="addTeamForm">
                                <div id="inviteCoachSuccessMessage" class="alert alert-success d-none" role="alert">
                                </div>
                                <div id="inviteCoachErrorMessage" class="alert alert-danger d-none" role="alert"></div>
                                @csrf
                                <div class="row">
                                    <div class="mb-4 col-md-3">
                                        <label class="text-uppercase form-label" for="firstName"> Coach First Name</label>
                                        <input class="form-control" id="inviteCoachFirstName" type="text" />
                                        <div id="coachFirstNameError" class="text-danger"></div>
                                    </div>
                                    <div class="mb-4 col-md-3">
                                        <label class="text-uppercase form-label" for="lastName"> Coach Last Name</label>
                                        <input class="form-control" id="inviteCoachLastName" type="text" />
                                        <div id="coachLastNameError" class="text-danger"></div>
                                    </div>
                                    <div class="mb-4 col-md-3">
                                        <label class="text-uppercase form-label" for="email"> Coach Email</label>
                                        <input class="form-control" id="inviteCoachEmail" type="email" />
                                        <div id="coachEmailError" class="text-danger"></div>
                                    </div>
                                    <div class="mb-4 col-md-3">
                                        <label class="text-uppercase form-label" for="team">Team Name</label>
                                        <input class="form-control" id="inviteCoachTeam" type="text" />
                                        <div id="coachTeamError" class="text-danger"></div>
                                    </div>
                                </div>
                                <div class="col-12 text-center mt-4">
                                    <button class="cta" id="searchTeamButton" type="button"> <span
                                            id="searchButtonText">
                                            Search</span>
                                        <span id="loaderForSearch">
                                            <img id="loaderForSearchCoach" src="{{ asset('images/loader.svg') }}"
                                                alt="Loading..."
                                                style="display: none; width: 40px; height: 40px; margin-left: 10px;"></span></button>
                                </div>
                            </form>
                            <div class="program-table" id="searchResultsForCoach">

                            </div>

                            <button id="loadMoreTeams" class="submit-btn" style="display:none">Load More</button>
                        </div>
                    </div>
                </div>
            </div>


            <!--search player -->
            <div class="modal fade" id="searchAndAddPlayer" tabindex="-1" aria-labelledby="searchPlayerLabel"
                aria-hidden="true" data-bs-backdrop="static">
                <div class="modal-dialog modal-dialog-centered" style="max-width: 1160px">
                    <div class="modal-content">
                        <div class="modal-body p-5">
                            <span class="modal-close" data-bs-dismiss="modal">&#x2715;</span>
                            <form class="form mb-5" id="addPlayerForm">
                                <div id="addPlayerSuccessMessage" class="alert alert-success d-none" role="alert">
                                </div>
                                <div id="addPlayerErrorMessage" class="alert alert-danger d-none" role="alert"></div>
                                @csrf
                                <div class="row">
                                    <div class="mb-4 col-md-3">
                                        <label class="text-uppercase form-label" for="firstName"> Player First
                                            Name</label>
                                        <input class="form-control" id="addPlayerFirstName" type="text" />
                                        <div id="playerFirstNameError" class="text-danger"></div>
                                    </div>
                                    <div class="mb-4 col-md-3">
                                        <label class="text-uppercase form-label" for="lastName"> Player Last Name</label>
                                        <input class="form-control" id="addPlayerLastName" type="text" />
                                        <div id="playerLastNameError" class="text-danger"></div>
                                    </div>
                                    <div class="mb-4 col-md-3">
                                        <label class="text-uppercase form-label" for="email"> Player Email</label>
                                        <input class="form-control" id="addPlayerEmail" type="email" />
                                        <div id="playerEmailError" class="text-danger"></div>
                                    </div>
                                    <div class="mb-4 col-md-3">
                                        <label class="text-uppercase form-label" for="team">Guardian Email</label>
                                        <input class="form-control" id="addPlayerGuardianMail" type="text" />
                                        <div id="playerGuardianMailError" class="text-danger"></div>
                                    </div>
                                </div>
                                <div class="col-12 text-center mt-4">
                                    <button class="cta" id="searchPlayerButton" type="button"> <span
                                            id="searchButtonTextForPlayers">
                                            Search</span>
                                        <span id="loaderForPlayerSearch">
                                            <img id="loaderForSearchPlayer" src="{{ asset('images/loader.svg') }}"
                                                alt="Loading..."
                                                style="display: none; width: 40px; height: 40px; margin-left: 10px;"></span></button>
                                </div>
                            </form>
                            <div class="program-table" id="searchResultsForPlayer">

                            </div>

                            <button id="loadMorePlayers" class="submit-btn" style="display:none">Load More</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.17.3/xlsx.full.min.js"></script>
    <script>
        document.addEventListener("DOMContentLoaded", function() {
            document.addEventListener("click", function(event) {
                if (event.target.classList.contains("toggle-arrow")) {
                    const arrow = event.target;
                    const programSlug = arrow.getAttribute("data-toggle");
                    const programTable = document.getElementById(
                        `program-table${programSlug}`
                    );
                    const addTeamBtn = document.getElementById(
                        `add-team-btn${programSlug}`
                    );
                    const addPlayerBtn = document.getElementById(
                        `add-player-btn${programSlug}`
                    );

                    if (programTable) {
                        programTable.classList.toggle("d-none");
                    }

                    if (addTeamBtn) {
                        addTeamBtn.classList.toggle("d-none");
                    }

                    if (addPlayerBtn) {
                        addPlayerBtn.classList.toggle("d-none");
                    }

                    if (programTable && programTable.classList.contains("d-none")) {
                        arrow.classList.remove("bi-caret-down-fill");
                        arrow.classList.add("bi-caret-up-fill");
                    } else {
                        arrow.classList.remove("bi-caret-up-fill");
                        arrow.classList.add("bi-caret-down-fill");
                    }
                }
            });

            const sportFilter = document.getElementById("sport-filter");
            const ageFilter = document.getElementById("age-filter");
            const searchInput = document.getElementById("search-input");
            const startDateFilter = document.getElementById("start-date");
            const endDateFilter = document.getElementById("end-date");
            const filterForm = document.getElementById("filterForm");

            const programsContainer = document.getElementById("programs-container");
            const paginationContainer = document.getElementById("pagination-container");


            function getCurrentFilters() {
                const queryParams = new URLSearchParams();


                if (sportFilter.value) queryParams.append("sport-filter", sportFilter.value);
                if (ageFilter.value) queryParams.append("age-filter", ageFilter.value);
                if (startDateFilter.value) queryParams.append("start-date", startDateFilter.value);
                if (endDateFilter.value) queryParams.append("end-date", endDateFilter.value);
                if (searchInput.value) queryParams.append("search-input", searchInput.value);

                return queryParams;
            }

            function filterPrograms(url = null) {
                const queryParams = getCurrentFilters();

                const baseUrl = url || route("admin.programsReport");
                const fullUrl = url ?
                    `${url}${url.includes('?') ? '&' : '?'}${queryParams.toString()}` :
                    `${baseUrl}${queryParams.toString() ? '?' + queryParams.toString() : ''}`;

                fetch(fullUrl, {
                        headers: {
                            'X-Requested-With': 'XMLHttpRequest',
                            'Accept': 'application/json'
                        }
                    })
                    .then(response => response.json())
                    .then(data => {
                        programsContainer.innerHTML = data.programData;
                        paginationContainer.innerHTML = data.pagination;
                        attachPaginationListeners();

                        // Update URL to reflect all current filters
                        window.history.pushState({}, '', fullUrl);
                    })
                    .catch(error => {
                        console.error('Error fetching data:', error);
                    });
            }

            function attachPaginationListeners() {
                const paginationLinks = paginationContainer.querySelectorAll(".pagination a");

                paginationLinks.forEach(link => {
                    link.addEventListener("click", function(event) {
                        event.preventDefault();
                        filterPrograms(this.href);
                    });
                });
            }

            // Debounce function is used to prevent extra ajax calls like if the user types in the search input it will wait for 500ms before making the ajax call
            function debounce(func, wait) {
                let timeout;
                return function executedFunction(...args) {
                    const later = () => {
                        clearTimeout(timeout);
                        func(...args);
                    };
                    clearTimeout(timeout);
                    timeout = setTimeout(later, wait);
                };
            }

            const filters = [sportFilter, ageFilter, startDateFilter, endDateFilter];
            filters.forEach(filter => {
                filter.addEventListener("change", () => filterPrograms());
            });


            searchInput.addEventListener("keyup", debounce(() => filterPrograms(), 500));


            attachPaginationListeners();


            let programSlug;
            document.addEventListener('click', function(event) {
                if (event.target && event.target.classList.contains('openModalButton')) {
                    programSlug = event.target.getAttribute("data-program-id");
                    const modal = new bootstrap.Modal(
                        document.getElementById("searchTeamModal")
                    );
                    document.getElementById("coachFirstNameError").textContent = "";
                    document.getElementById("coachLastNameError").textContent = "";
                    document.getElementById("coachEmailError").textContent = "";
                    document.getElementById("coachTeamError").textContent = "";
                    document.getElementById("searchResultsForCoach").innerHTML = "";
                    document.getElementById("loadMoreTeams").style.display = "none";
                    modal.show();
                }
            });


            document.addEventListener("click", function(event) {
                if (event.target && (event.target.id == "searchTeamButton" || event.target.id ==
                        "searchButtonText")) {

                    event.preventDefault();
                    const firstName = document
                        .getElementById("inviteCoachFirstName")
                        .value.trim();
                    const lastName = document
                        .getElementById("inviteCoachLastName")
                        .value.trim();
                    const email = document.getElementById("inviteCoachEmail").value.trim();
                    const team = document.getElementById("inviteCoachTeam").value.trim();

                    const firstNameError = document.getElementById("coachFirstNameError");
                    const lastNameError = document.getElementById("coachLastNameError");
                    const emailError = document.getElementById("coachEmailError");
                    const teamError = document.getElementById("coachTeamError");

                    firstNameError.textContent = "";
                    lastNameError.textContent = "";
                    emailError.textContent = "";
                    teamError.textContent = "";

                    const emailPattern = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                    if (!firstName && !lastName && !email && !team) {
                        firstNameError.textContent = "Please enter a first name or";
                        lastNameError.textContent = "last name or";
                        emailError.textContent = "email or";
                        teamError.textContent = "team to search";
                        return;
                    }
                    if (email && !emailPattern.test(email)) {
                        emailError.textContent = "Please enter a valid email address.";
                        return;
                    }

                    const loadMoreBtn = document.getElementById("loadMoreTeams");
                    const url = new URL(
                        route("admin.allTeams", {
                            program: programSlug,
                        })
                    );

                    url.searchParams.set("page", 1);

                    if (firstName) url.searchParams.append("coachFirstName", firstName);
                    if (lastName) url.searchParams.append("coachLastName", lastName);
                    if (email) url.searchParams.append("coachEmail", email);
                    if (team) url.searchParams.append("teamName", team);

                    fetchTeams(url, 1, loadMoreBtn);
                    showLoaderForSearch();

                }
                // here was the event.preventDefault
            });

            function fetchTeams(url, currentPage, loadMoreBtn) {

                const firstName = document.getElementById("inviteCoachFirstName").value.trim();
                const lastName = document.getElementById("inviteCoachLastName").value.trim();
                const email = document.getElementById("inviteCoachEmail").value.trim();
                const team = document.getElementById("inviteCoachTeam").value.trim();


                const firstNameError = document.getElementById("coachFirstNameError");
                const lastNameError = document.getElementById("coachLastNameError");
                const emailError = document.getElementById("coachEmailError");
                const teamError = document.getElementById("coachTeamError");

                firstNameError.textContent = "";
                lastNameError.textContent = "";
                emailError.textContent = "";
                teamError.textContent = "";


                const emailPattern = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                if (!firstName && !lastName && !email && !team) {
                    firstNameError.textContent = "Please enter a first name or";
                    lastNameError.textContent = "last name or";
                    emailError.textContent = "email or";
                    teamError.textContent = "team to search";
                    hideLoaderForSearch();
                    return;
                }

                if (email && !emailPattern.test(email)) {
                    emailError.textContent = "Please enter a valid email address.";
                    hideLoaderForSearch();
                    return;
                }


                fetch(url, {
                        method: "GET",
                        headers: {
                            "X-Requested-With": "XMLHttpRequest",
                        },
                    })
                    .then((response) => response.json())
                    .then((data) => {


                        const resultsContainer = document.getElementById("searchResultsForCoach");
                        hideLoaderForSearch();


                        if (currentPage === 1) {
                            resultsContainer.innerHTML = `
                    <div class="program-table">
                        <div class="table-program table-responsive mb-5">
                            <table class="table table-hover" width="100%">
                                <thead>
                                    <tr>
                                        <th class="py-0">Team Name</th>
                                        <th class="py-0">Name</th>
                                        <th class="py-0">Email</th>
                                        <th class="py-0">Assistant Coach Email</th> <!-- New header -->
                                        <th class="py-0" width="100"></th>
                                    </tr>
                                </thead>
                                <tbody></tbody>
                            </table>
                        </div>
                    </div>`;
                        }

                        const tableBody = resultsContainer.querySelector("tbody");
                        const teamsArray = Array.isArray(data.teams) ? data.teams : Object.values(data.teams);


                        if (teamsArray.length > 0) {
                            teamsArray.forEach((team) => {
                                let assistantEmail = "N/A";


                                if (team.primary_coach) {

                                    if (team.assistant_coaches && typeof team.assistant_coaches ===
                                        'object') {

                                        const assistantCoachEmail = Object.values(team
                                            .assistant_coaches)[0];
                                        assistantEmail = assistantCoachEmail || "N/A";
                                    }

                                    const actionContent = team.is_registered ?
                                        `<span class="text-success">Registered</span>` :
                                        `<button type="button" class="cta hover-dark addTeamButton" data-team-id="${team.team_id}" data-coach-id="${team.primary_coach.id}">Add</button>`;

                                    const primaryCoachRow = `
                            <tr>
                                <td class="py-4" valign="middle">${team.team_name}</td>
                                <td class="py-4" valign="middle">${team.primary_coach.firstName || "N/A"} ${team.primary_coach.lastName || "N/A"}</td>
                                <td class="py-4" valign="middle">${team.primary_coach.email || "N/A"}</td>
                                <td class="py-4" valign="middle">${assistantEmail}</td> <!-- Show assistant email -->
                                <td class="py-4" valign="middle">${actionContent}</td>
                            </tr>`;
                                    tableBody.insertAdjacentHTML("beforeend", primaryCoachRow);
                                }


                            });


                            resultsContainer.addEventListener("click", function(event) {
                                if (event.target && event.target.classList.contains("addTeamButton")) {
                                    const teamId = event.target.getAttribute("data-team-id");
                                    const coachId = event.target.getAttribute("data-coach-id");
                                    const button = event.target;

                                    button.innerHTML =
                                        `<span class="spinner-border spinner-border-sm text-primary" role="status" aria-hidden="true"></span>`;
                                    button.style.backgroundColor = "transparent";
                                    button.style.border = "none";
                                    button.disabled = true;

                                    const requestData = {
                                        team_id: teamId,
                                        program_slug: programSlug,
                                        coach_id: coachId,
                                    };

                                    const url = route("admin.addTeamToProgram");


                                    fetch(url, {
                                            method: "POST",
                                            headers: {
                                                "Content-Type": "application/json",
                                                "X-CSRF-TOKEN": document.querySelector(
                                                    'meta[name="csrf-token"]').getAttribute(
                                                    "content"),
                                            },
                                            body: JSON.stringify(requestData),
                                        })
                                        .then((response) => {
                                            if (!response.ok) {
                                                throw new Error("Failed to register team");
                                            }
                                            return response.json();
                                        })
                                        .then((data) => {
                                            if (data.success) {
                                                const span = document.createElement("span");
                                                span.innerText = "Registered";
                                                span.classList.add("text-success");
                                                button.parentNode.replaceChild(span, button);
                                            } else {
                                                button.innerHTML = "Add";
                                                button.style.backgroundColor = "";
                                                button.style.border = "";
                                                button.disabled = false;
                                                showGlobalError(data.message ||
                                                    "An error occurred. Please try again. Error 1"
                                                );
                                                hideLoaderForSearch();
                                            }
                                        })
                                        .catch((error) => {
                                            console.error("Error:", error);
                                            button.innerHTML = "Add";
                                            button.style.backgroundColor = "";
                                            button.style.border = "";
                                            button.disabled = false;
                                            alert("An error occurred. Please try again. error 2");
                                            hideLoaderForSearch();
                                        });
                                }
                            });


                            if (currentPage >= data.last_page) {
                                loadMoreBtn.style.display = "none";
                            } else {
                                loadMoreBtn.style.display = "block";
                            }

                            loadMoreBtn.setAttribute("data-current-page", currentPage);
                        } else {
                            hideLoaderForSearch();
                            if (currentPage === 1) {
                                resultsContainer.innerHTML =
                                    '<p class="text-center text-gray-600">No teams found.</p>';
                                loadMoreBtn.style.display = "none";
                            }
                        }
                    })
                    .catch((error) => {
                        console.error("Error:", error);
                        hideLoaderForSearch();
                        document.getElementById("inviteCoachErrorMessage").classList.remove("d-none");
                        document.getElementById("inviteCoachErrorMessage").textContent =
                            "An error occurred while searching for teams.";
                    });
            }







            function showLoaderForSearch() {

                document.getElementById("searchButtonText").style.display = "none";
                document.getElementById("loaderForSearchCoach").style.display =
                    "inline-block";
                const button = document.getElementById("searchTeamButton");
                button.classList.add("buttonLoader");
            }

            function hideLoaderForSearch() {
                document.getElementById("searchButtonText").style.display = "inline";
                document.getElementById("loaderForSearchCoach").style.display = "none";
                const button = document.getElementById("searchTeamButton");
                button.classList.remove("buttonLoader");
            }


            document.addEventListener('click', function(event) {
                if (event.target && event.target.id === 'loadMoreTeams') {
                    const loadMoreBtn = event.target;
                    const currentPage = parseInt(loadMoreBtn.getAttribute("data-current-page")) || 1;
                    const nextPage = currentPage + 1;

                    const url = new URL(route("admin.allTeams"));
                    url.searchParams.set("page", nextPage);

                    const firstName = document
                        .getElementById("inviteCoachFirstName")
                        .value.trim();
                    const lastName = document
                        .getElementById("inviteCoachLastName")
                        .value.trim();
                    const email = document.getElementById("inviteCoachEmail").value.trim();
                    const team = document.getElementById("inviteCoachTeam").value.trim();

                    if (firstName) url.searchParams.append("coachFirstName", firstName);
                    if (lastName) url.searchParams.append("coachLastName", lastName);
                    if (email) url.searchParams.append("coachEmail", email);
                    if (team) url.searchParams.append("teamName", team);

                    fetchTeams(url, nextPage, loadMoreBtn);
                }
            });


            let individualProgramSlug;
            document.addEventListener('click', function(event) {
                if (event.target && event.target.classList.contains('playerModalButton')) {
                    individualProgramSlug = event.target.getAttribute("data-program-id");
                    const addPlayerModal = new bootstrap.Modal(document.getElementById(
                        "searchAndAddPlayer"));

                    const firstNameError = document.getElementById("playerFirstNameError");
                    const lastNameError = document.getElementById("playerLastNameError");
                    const emailError = document.getElementById("playerEmailError");
                    const guardianMailError = document.getElementById(
                        "playerGuardianMailError"
                    );

                    document
                        .getElementById("addPlayerFirstName")
                        .value = ""
                    document
                        .getElementById("addPlayerLastName")
                        .value = ""
                    document.getElementById("addPlayerEmail").value.trim();
                    document
                        .getElementById("addPlayerGuardianMail")
                        .value = ""


                    document.getElementById("searchResultsForPlayer").innerHTML = "";

                    document.getElementById('loadMorePlayers').style.display = "none";

                    firstNameError.textContent = "";
                    lastNameError.textContent = "";
                    emailError.textContent = "";
                    guardianMailError.textContent = "";
                    addPlayerModal.show();
                }
            });


            document.addEventListener('click', function(event) {
                if (event.target && (event.target.id === 'searchPlayerButton' || event.target.id ==
                        "searchButtonTextForPlayers")) {
                    event.preventDefault();

                    const firstName = document
                        .getElementById("addPlayerFirstName")
                        .value.trim();
                    const lastName = document
                        .getElementById("addPlayerLastName")
                        .value.trim();
                    const email = document.getElementById("addPlayerEmail").value.trim();
                    const guardianMail = document
                        .getElementById("addPlayerGuardianMail")
                        .value.trim();

                    const firstNameError = document.getElementById("playerFirstNameError");
                    const lastNameError = document.getElementById("playerLastNameError");
                    const emailError = document.getElementById("playerEmailError");
                    const guardianMailError = document.getElementById(
                        "playerGuardianMailError"
                    );

                    firstNameError.textContent = "";
                    lastNameError.textContent = "";
                    emailError.textContent = "";
                    guardianMailError.textContent = "";

                    const emailPattern = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                    if (!firstName && !lastName && !email && !guardianMail) {
                        firstNameError.textContent = "Please enter a first name or";
                        lastNameError.textContent = "last name or";
                        emailError.textContent = "email or";
                        guardianMailError.textContent = "Guardian email to search";
                        return;
                    }
                    if (email && !emailPattern.test(email)) {
                        emailError.textContent = "Please enter a valid email address.";
                        return;
                    }

                    const loadMorePlayers = document.getElementById("loadMorePlayers");
                    const url = new URL(
                        route("admin.allPlayers", {
                            program: individualProgramSlug,
                        })
                    );

                    url.searchParams.set("page", 1);
                    if (firstName) url.searchParams.append("playerFirstName", firstName);
                    if (lastName) url.searchParams.append("playerLastName", lastName);
                    if (email) url.searchParams.append("playerEmail", email);
                    if (guardianMail)
                        url.searchParams.append("guardianEmail", guardianMail);

                    fetchPlayers(url, 1, loadMorePlayers);
                    showLoaderForSearchPlayer();
                }
            });


            let loadedPlayerIds = new Set();

            function fetchPlayers(url, currentPage, loadMorePlayers) {
                fetch(url, {
                        method: "GET",
                        headers: {
                            "X-Requested-With": "XMLHttpRequest",
                        },
                    })
                    .then((response) => response.json())
                    .then((data) => {
                        const resultsContainer = document.getElementById("searchResultsForPlayer");
                        if (currentPage === 1) {
                            resultsContainer.innerHTML = `
                    <div class="program-table">
                        <div class="table-program table-responsive mb-5">
                            <table class="table table-hover" width="100%">
                                <tbody>
                                    <tr>
                                        <th class="py-0">First Name</th>
                                        <th class="py-0">Last Name</th>
                                        <th class="py-0">Email</th>
                                        <th class="py-0">Guardian Mail</th>
                                        <th class="py-0" width="100"></th>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>`;
                            loadedPlayerIds.clear();
                        }

                        const tableBody = resultsContainer.querySelector("tbody");
                        const playersArray = Array.isArray(data.players) ? data.players : [];

                        if (playersArray.length > 0) {
                            playersArray.forEach((player) => {
                                if (loadedPlayerIds.has(player.id)) {
                                    return;
                                }

                                loadedPlayerIds.add(player.id);
                                hideLoaderForSearchPlayer()
                                const actionContent = player.is_registered ?
                                    `<span class="text-success">Registered</span>` :
                                    player.is_invited ?
                                    `<span class="text-warning">Invited</span>` :
                                    `<button type="button" class="cta hover-dark addPlayerButton" data-player-id="${player.id}">Add</button>`;

                                const playerRow = `
                        <tr>
                            <td class="py-4" valign="middle">${player.firstName}</td>
                            <td class="py-4" valign="middle">${player.lastName || "N/A"}</td>
                            <td class="py-4" valign="middle">${player.email || "N/A"}</td>
                            <td class="py-4" valign="middle">${player.guardian_email || "N/A"}</td>
                            <td class="py-4" valign="middle">${actionContent}</td>
                        </tr>`;

                                tableBody.insertAdjacentHTML("beforeend", playerRow);
                            });

                            if (currentPage >= data.last_page) {
                                loadMorePlayers.style.display = "none";
                            } else {
                                loadMorePlayers.style.display = "block";
                            }

                            loadMorePlayers.setAttribute("data-current-page", currentPage);
                        } else {
                            hideLoaderForSearchPlayer()
                            if (currentPage === 1) {
                                resultsContainer.innerHTML =
                                    '<p class="text-center text-gray-600">No Players found.</p>';
                                loadMorePlayers.style.display = "none";
                            }
                        }
                    })
                    .catch((error) => {
                        console.error("Error:", error);
                        hideLoaderForSearchPlayer()
                        document.getElementById("inviteCoachErrorMessage").classList.remove("d-none");
                        document.getElementById("inviteCoachErrorMessage").textContent =
                            "An error occurred while searching for players.";
                    });
            }

            // Event delegation for 'Add' button click (add player)
            document.addEventListener('click', function(event) {
                if (event.target && event.target.matches('.addPlayerButton')) {
                    const playerId = event.target.getAttribute("data-player-id");
                    const button = event.target;

                    button.innerHTML =
                        `<span class="spinner-border spinner-border-sm text-primary" role="status" aria-hidden="true"></span>`;
                    button.style.backgroundColor = "transparent";
                    button.style.border = "none";
                    button.disabled = true;

                    const requestData = {
                        player_id: playerId,
                        program_slug: individualProgramSlug,
                    };

                    const url = route("admin.addPlayerToProgram");

                    fetch(url, {
                            method: "POST",
                            headers: {
                                "Content-Type": "application/json",
                                "X-CSRF-TOKEN": document.querySelector('meta[name="csrf-token"]')
                                    .getAttribute("content"),
                            },
                            body: JSON.stringify(requestData),
                        })
                        .then((response) => {
                            if (!response.ok) {
                                throw new Error("Failed to register Player");
                            }
                            return response.json();
                        })
                        .then((data) => {
                            if (data.success) {
                                const span = document.createElement("span");
                                span.innerText = "Invited";
                                span.classList.add("text-warning");

                                button.parentNode.replaceChild(span, button);
                            } else {
                                button.innerHTML = "Add";
                                button.style.backgroundColor = "";
                                button.style.border = "";
                                button.disabled = false;
                                alert(data.message || "An error occurred. Please try again. error 3");
                            }
                        })
                        .catch((error) => {
                            console.error("Error:", error);
                            button.innerHTML = "Add";
                            button.style.backgroundColor = "";
                            button.style.border = "";
                            button.disabled = false;
                            alert("An error occurred. Please try again. error 4");
                        });
                }
            });


            function showLoaderForSearchPlayer() {
                document.getElementById("searchButtonTextForPlayers").style.display =
                    "none";
                document.getElementById("loaderForSearchPlayer").style.display =
                    "inline-block";
                const searchPlayerButton =
                    document.getElementById("searchPlayerButton");
                searchPlayerButton.classList.add("buttonLoader");
            }

            function hideLoaderForSearchPlayer() {
                document.getElementById("searchButtonTextForPlayers").style.display =
                    "inline";
                document.getElementById("loaderForSearchPlayer").style.display = "none";
                const searchPlayerButton =
                    document.getElementById("searchPlayerButton");
                searchPlayerButton.classList.remove("buttonLoader");
            }

            document.addEventListener('click', function(event) {

                if (event.target && event.target.id === 'loadMorePlayers') {
                    const loadMorePlayers = event.target;
                    const currentPage = parseInt(loadMorePlayers.getAttribute("data-current-page")) || 1;
                    const nextPage = currentPage + 1;

                    const url = new URL(route("admin.allPlayers", {
                        program: individualProgramSlug,
                    }));
                    url.searchParams.set("page", nextPage);

                    const firstName = document.getElementById("addPlayerFirstName").value.trim();
                    const lastName = document.getElementById("addPlayerLastName").value.trim();
                    const email = document.getElementById("addPlayerEmail").value.trim();
                    const guardianMail = document.getElementById("addPlayerGuardianMail").value.trim();

                    if (firstName) url.searchParams.append("addPlayerFirstName", firstName);
                    if (lastName) url.searchParams.append("addPlayerLastName", lastName);
                    if (email) url.searchParams.append("addPlayerEmail", email);
                    if (guardianMail) url.searchParams.append("addPlayerGuardianMail", guardianMail);

                    fetchPlayers(url, nextPage, loadMorePlayers);
                }
            });


            //print the program details


            document.body.addEventListener("click", function(event) {
                if (event.target && event.target.classList.contains("print-icon")) {
                    var programSlug = event.target.getAttribute("data-program-slug");
                    printProgramDetails(programSlug);
                }
            });

            function printProgramDetails(programSlug) {
                var programTable = document.getElementById('program-table' + programSlug);
                var editIcons = programTable.querySelectorAll('.action.edit');
                var deleteIcons = programTable.querySelectorAll('.action.delete');

                let emptyColumns = programTable.querySelectorAll('.empty');


                editIcons.forEach(function(icon) {
                    icon.classList.add('hide-while-printing');
                });
                deleteIcons.forEach(function(deleteIcon) {
                    deleteIcon.classList.add('hide-while-printing');
                });

                emptyColumns.forEach(function(column) {
                    column.style.display = "none";
                });


                var printContent = programTable.outerHTML;


                var newWindow = window.open('', '', 'height=700,width=1000');
                newWindow.document.write('<html><head><title>Program Details</title>');
                newWindow.document.write(`
        <style>
            body {
                font-family: Arial, sans-serif;
                margin: 20px;
                padding: 0;
                color: #333;
                line-height: 1.6;
            }
            h1 {
                text-align: center;
                margin-bottom: 20px;
                font-size: 24px;
                color: #444;
            }
            table {
                width: 100%;
                border-collapse: collapse;
                margin-bottom: 20px;
            }
            th, td {
                border: 1px solid #ddd;
                padding: 8px;
                text-align: left;
            }
            th {
                background-color: #f4f4f4;
                font-weight: bold;
            }
            tr:nth-child(even) {
                background-color: #f9f9f9;
            }
            tr:hover {
                background-color: #f1f1f1;
            }
            @media print {
            .hide-while-printing {
                display: none !important;
                }
            td:empty, th:empty {
            display: none !important;
             }
            td:has(.hide-while-printing), th:has(.hide-while-printing) {
            display: none !important;
        }
            }
        </style>
    `);
                newWindow.document.write('</head><body>');


                newWindow.document.write(
                    `<h1 style="text-transform: uppercase; font-family: Arial, sans-serif; text-align: center;">${programSlug}</h1>`
                );

                newWindow.document.write(printContent);


                newWindow.document.write('</body></html>');
                newWindow.document.close();
                newWindow.print();


                editIcons.forEach(function(icon) {
                    icon.classList.remove('hide-while-printing');
                });
                deleteIcons.forEach(function(deleteIcon) {
                    deleteIcon.classList.remove('hide-while-printing');
                });

                emptyColumns.forEach(function(column) {
                    column.style.display = "block";
                });
            }



            document.body.addEventListener('click', function(event) {

                if (event.target && event.target.dataset.action === 'export-xls') {
                    exportToExcel();
                }
            });


            function exportToExcel() {

                var table = document.querySelector(".table");

                if (table) {

                    var wb = XLSX.utils.table_to_book(table, {
                        sheet: "Program Data"
                    });


                    XLSX.writeFile(wb, 'program_data.xlsx');
                } else {
                    alert("Table not found for export.");
                }
            }

            document.addEventListener('click', function(event) {
                if (event.target && event.target.classList.contains('export-xls-icon')) {
                    var programSlug = event.target.closest('td').getAttribute('data-program-slug');
                    exportProgramToExcel(programSlug);
                }
            });

            function exportProgramToExcel(programSlug) {

                var programTable = document.getElementById('program-table' + programSlug);

                if (!programTable) {
                    alert("Program table not found!");
                    return;
                }


                var wb = XLSX.utils.table_to_book(programTable, {
                    sheet: 'Program Data'
                });

                var filename = "Program_" + programSlug + ".xlsx";

                XLSX.writeFile(wb, filename);
            }




            showSessionSuccessMessage();
            showSessionErrorMessage();

        });
    </script>


@endsection
