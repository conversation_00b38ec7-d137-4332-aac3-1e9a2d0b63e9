{{--
@extends('layouts.app')

@section('title', 'payment')


@section('content')

    <section class="page_title text-center pt-5">
        <h1 class="text-uppercase lh-1 mb-0">Payment</h1>
    </section>
    <section class="sec program-welcome">
        <div class="container">
            <div class="heading align-items-center d-flex flex-column text-center">
                <span class="hero-bar d-inline-flex mb-5"></span>
                <div class="d-flex flex-column-reverse">
                    <h3 class="text-uppercase mb-4">Payment Options:</h3>
                </div>
            </div>
            <div class="row justify-content-center">
                <div class="col-xl-10 payment-form">
                    <form class="form" action="" id="paymentForm">
                        <div class="row">
                            <div class="mb-4 col-md-auto">
                                <div class="form-check col-md-auto">
                                    <input class="form-check-input" id="fullAmount" type="radio" value="fullAmount"
                                        name="payment" data-amount="{{ session('amount') }}" />
                                    <label class="form-check-label p text-uppercase" for="fullAmount">I will be paying this
                                        amount in full</label>
                                </div>
                            </div>
                        </div>
                        <div class="row d-flex align-items-center">
                            <div class="mb-4 col-md-auto">
                                <div class="form-check col-md-auto">
                                    <input class="form-check-input" id="specificAmount" type="radio"
                                        value="specificAmount" name="payment" />
                                    <label class="form-check-label p text-uppercase" for="specificAmount">I will be paying a
                                        specific amount</label>
                                </div>
                            </div>
                            <div class="mb-4 col-md-auto">
                                <label class="text-uppercase visually-hidden form-label"
                                    for="specificMoney">specificMoney</label>
                                <input class="form-control" id="specificMoney" type="number" />
                            </div>
                        </div>
                        <div class="row d-flex align-items-center">
                            <div class="mb-4 col-md-auto">
                                <div class="form-check col-md-auto">
                                    <input class="form-check-input" id="differentInvoiceMail" type="radio"
                                        value="differentInvoiceMail" name="payment" />
                                    <label class="form-check-label p text-uppercase" for="differentInvoiceMail">Send invoice
                                        to different email address</label>
                                </div>
                            </div>
                            <div class="mb-4 col-md-auto">
                                <label class="text-uppercase visually-hidden form-label"
                                    for="differentMailInvoice">differentMailInvoice</label>
                                <input class="form-control" id="differentMailInvoice" type="email" />
                            </div>
                        </div>

                        <div class="row d-flex align-items-center">
                            <div class="mb-4 col-md-auto">
                                <div class="form-check col-md-auto">
                                    <input class="form-check-input" id="differentGuardiansRadio" type="radio"
                                        value="differentGuardiansRadio" name="payment" />
                                    <label class="form-check-label p text-uppercase" for="differentGuardiansRadio">
                                        Send invoice to different guardians
                                    </label>
                                </div>
                            </div>

                            <div class="mb-4 col-md-3">
                                <label class="form-label text-uppercase visually-hidden" for="differentGuardians">
                                    differentGuardians
                                </label>
                                <div class="select-arrow position-relative">
                                    <select class="form-control selectpicker" id="differentGuardians" multiple
                                        data-live-search="true">
                                        <option value="guardian1">Guardian 1</option>
                                        <option value="guardian2">Guardian 2</option>
                                        <option value="guardian3">Guardian 3</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <div class="row d-flex align-items-center">
                            <div class="mb-4 col-md-auto">
                                <div class="form-check col-md-auto">
                                    <input class="form-check-input" id="recurringPayments" type="radio"
                                        value="recurringPayments" name="payment" />
                                    <label class="form-check-label p text-uppercase" for="recurringPayments">Set up
                                        recurring payments</label>
                                </div>
                            </div>

                        </div>
                        <div class="row gx-md-5" id='recurringPaymentsInfo'>
                            <div class="col-md">
                                <div class="row mb-4 align-items-center">
                                    <label class="col-md-auto form-col-label text-uppercase" for="amount">Amount</label>
                                    <div class="col-md">
                                        <input class="col-md form-control w-100" id="recurringAmount" type="text" />
                                    </div>
                                </div>
                            </div>
                            <div class="col-md">
                                <div class="row mb-4 align-items-center">
                                    <label class="col-md-auto form-col-label text-uppercase" for="startDate">Start
                                        date</label>
                                    <div class="col-md">
                                        <input class="col-md form-control w-100" id="startDate" type="date" />
                                    </div>
                                </div>
                            </div>
                            <div class="col-md">
                                <div class="row mb-4 align-items-center">
                                    <label class="col-md-auto form-col-label text-uppercase" for="numberofpayments">Number
                                        of payments</label>
                                    <div class="col-md">
                                        <input class="col-md form-control w-100" id="numberOfPayments" type="number"
                                            disabled />
                                    </div>
                                </div>
                            </div>
                        </div>
                </div>
            </div>
        </div>
    </section>
    <section class="sec partition-hr">
        <div class="container">
            <hr class="border-navy opacity-100 my-0" />
        </div>
    </section>
    <section class="sec form mb-5">
        <div class="container">
            <div class="row justify-content-center">
                <div class="col-xl-10 col-xxl-8">
                    <div class="row">
                        <div class="mb-4 col-md-6">
                            <label class="text-uppercase form-label" for="firstName">First Name</label>
                            <input class="form-control" id="firstName" type="text" required />
                        </div>
                        <div class="mb-4 col-md-6">
                            <label class="text-uppercase form-label" for="lastName">Last Name</label>
                            <input class="form-control" id="lastName" type="text" required />
                        </div>
                    </div>

                    <div class="row">
                        <div class="mb-4 col-md-6">
                            <label class="text-uppercase form-label" for="address">Address</label>
                            <input class="form-control" id="address" type="text" required />
                        </div>
                        <div class="mb-4 col-md-3">
                            <label class="form-label" for="state">State</label>
                            <select class="form-control" id="state" required>
                                <option value="">Select</option>
                                <option value="lorem">Lorem</option>
                                <option value="ipsum">Ipsum</option>
                                <option value="dolor">Dolor</option>
                            </select>
                        </div>
                        <div class="mb-4 col-md-3">
                            <label class="text-uppercase form-label">ZIP Code</label>
                            <div id="card-zip" class="form-control stripe-input"></div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="mb-4 col-md-2">
                            <label class="text-uppercase form-label" for="cardType">Card Type</label>
                            <input class="form-control" id="cardType" type="text" disabled />
                        </div>
                        <div class="mb-4 col-md-4">
                            <label class="text-uppercase form-label">Card Number</label>
                            <div id="card-number" class="form-control stripe-input"></div>
                        </div>
                        <div class="mb-4 col-md-3">
                            <label class="text-uppercase form-label">Exp. Date</label>
                            <div id="card-expiry" class="form-control stripe-input"></div>
                        </div>
                        <div class="mb-4 col-md-3">
                            <label class="text-uppercase form-label">CVC</label>
                            <div id="card-cvc" class="form-control stripe-input"></div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-12 d-flex justify-content-center mt-5">
                            <button class="cta py-0" id="paymentButton"> <span id="submitButtonText">Submit
                                </span>
                                <span id="loaderForPayment">
                                    <img id="loaderForPaymentImage" src="{{ asset('images/loader.svg') }}"
                                        alt="Loading..."
                                        style="display: none; width: 40px; height: 40px; margin-left: 10px;"></span></button>
                        </div>
                    </div>
                    </form>
                </div>
            </div>
        </div>
    </section>


    <script src="https://js.stripe.com/v3/"></script>
    <script src="https://cdn.jsdelivr.net/npm/card-validator@latest/dist/card-validator.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/choices.js/public/assets/scripts/choices.min.js"></script>

    <script>
        document.addEventListener('DOMContentLoaded', function() {

            @if (session('program') && session('amount') && session('playerIds'))
                const programPaymentType = @json(session('program')->payment);
                const fullAmountToBePaid = @json(session('amount'));

                const playerIds = @json(session('playerIds', []));
                const programId = @json(session('program')->id);
            @else
                window.location.href = "{{ url()->previous() }}" || route('redirectLogin');
                return;
            @endif


            const amount = document.getElementById('recurringAmount');
            const numPayments = document.getElementById('numberOfPayments');
            numPayments.setAttribute('disabled', true);


            amount.addEventListener('input', () => {
                const recurringAmount = parseFloat(amount.value);

                if (recurringAmount > 0) {
                    const numberOfPayments = Math.ceil(fullAmountToBePaid / recurringAmount)
                    numPayments.value = numberOfPayments;
                } else {
                    numPayments.value = "";
                }
            });

            const fullAmountRadio = document.getElementById('fullAmount');
            const specificAmountRadio = document.getElementById('specificAmount');
            const recurringPaymentRadio = document.getElementById('recurringPayments');
            const specificMoneyInput = document.getElementById('specificMoney');
            const invoiceToOtherMails = document.getElementById('differentInvoiceMail');
            const differentGuardians = document.getElementById("differentGuardiansRadio");

            const recurringPaymentsInfo = document.getElementById("recurringPaymentsInfo");

            if (programPaymentType === 'full') {

                fullAmountRadio.checked = true;
                specificAmountRadio.disabled = true;
                recurringPaymentRadio.disabled = true;
                specificMoneyInput.disabled = true;
                invoiceToOtherMails.disabled = true;
                differentGuardians.disabled = true;
                recurringPaymentsInfo.classList.add('d-none');
            }



            var stripe = Stripe('{{ env('STRIPE_KEY') }}');
            const elements = stripe.elements();

            const cardNumber = elements.create('cardNumber', {
                placeholder: 'Card Number',
                style: {
                    base: {
                        fontSize: '16px',
                        color: '#32325d'
                    }
                },
            });

            const cardExpiry = elements.create('cardExpiry');
            const cardCvc = elements.create('cardCvc');
            const cardZip = elements.create('postalCode');

            cardNumber.mount('#card-number');
            cardExpiry.mount('#card-expiry');
            cardCvc.mount('#card-cvc');
            cardZip.mount('#card-zip');

            const cardTypeInput = document.getElementById('cardType');
            cardNumber.on('change', function(event) {
                cardTypeInput.value = event.brand ? capitalize(event.brand) : '';
                if (event.error) console.error(event.error.message);
            });

            function capitalize(str) {
                return str.charAt(0).toUpperCase() + str.slice(1);
            }

            const form = document.getElementById('paymentForm');
            const csrfToken = document.querySelector('meta[name="csrf-token"]').getAttribute('content');


            form.addEventListener('submit', async function(event) {
                event.preventDefault();
                const selectedAmount = getSelectedAmount();

                if (!selectedAmount) {
                    alert('Please select a valid payment option.');
                    return;
                }




                showLoaderForPayment();

                try {

                    const firstName = document.getElementById('firstName').value;
                    const lastName = document.getElementById('lastName').value;
                    const cardHolderName = `${firstName} ${lastName}`.trim();
                    if (selectedAmount.type === 'recurring') {
                        const paymentMethodResponse = await stripe.createPaymentMethod({
                            type: 'card',
                            card: cardNumber,
                            billing_details: {
                                name: cardHolderName
                                    .value,
                                address: {
                                    postal_code: cardZip.value,
                                },
                            },
                        });

                        if (paymentMethodResponse.error) {
                            console.error('Error creating payment method:', paymentMethodResponse
                                .error);
                            alert('Error creating payment method: ' + paymentMethodResponse.error
                                .message);
                            hideLoaderForPayment();
                            return;
                        }

                        const paymentMethodId = paymentMethodResponse.paymentMethod.id;

                        const response = await fetch(route('setup-recurring-payment'), {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                                'X-CSRF-TOKEN': csrfToken,
                            },
                            body: JSON.stringify({
                                amount: selectedAmount.amount,
                                paymentDate: selectedAmount.startDate,
                                numberOfPayments: selectedAmount.numPayments,
                                paymentMethodId: paymentMethodId, // Pass the payment method ID
                            }),
                        });

                        const result = await response.json();

                        console.log(result);

                        if (result.error) {
                            console.error('Recurring payment setup error:', result.error);
                            alert('Error setting up recurring payments: ' + result.error);
                        } else {

                            const updateResponse = await fetch(route('recurringPayments.status'), {
                                method: 'POST',
                                headers: {
                                    'Content-Type': 'application/json',
                                    'X-CSRF-TOKEN': csrfToken,
                                },

                                body: JSON.stringify({
                                    subscriptionId: result
                                        .subscriptionId,
                                    status: 'active',
                                    amount: selectedAmount.amount,
                                    paymentDate: selectedAmount.startDate,
                                    numberOfPayments: selectedAmount.numPayments,
                                    paymentMethodId: paymentMethodId,
                                    playerIds: playerIds,
                                    programId: programId,
                                    createdAt: new Date()
                                        .toISOString(),

                                }),
                            });

                            const StatusResult = await updateResponse.json();
                            if (!updateResponse.ok) {

                                throw new Error(result.error || 'Failed to update payment status.');
                            } else {


                                window.location.href = route('payment.success');
                            }


                        }
                    } else {
                        // Handle one-time payment
                        const response = await fetch(route('payment.process'), {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                                'X-CSRF-TOKEN': csrfToken,
                            },
                            body: JSON.stringify({
                                amount: selectedAmount.amount,
                            }),
                        });

                        const {
                            clientSecret,
                            error: processError
                        } = await response.json();

                        if (processError) {
                            console.error('Payment processing error:', processError);
                            alert('Payment processing error: ' + processError);
                            hideLoaderForPayment();
                            return;
                        }

                        const {
                            error
                        } = await stripe.confirmCardPayment(clientSecret, {
                            payment_method: {
                                card: cardNumber,
                                billing_details: {
                                    address: {
                                        postal_code: cardZip.value,
                                    },
                                },
                            },
                        });

                        if (error) {
                            console.error('Payment failed:', error.message);
                            alert('Payment failed: ' + error.message);
                        } else {
                            const updateSuccess = await updatePaymentStatus(clientSecret, selectedAmount
                                .amount);
                            if (updateSuccess) {
                                window.location.href = route('payment.success');
                            } else {
                                window.location.href = route('payment.error');

                                console.log('payment failed');
                            }
                        }
                    }
                } catch (err) {
                    console.error('Error during payment process:', err);
                    alert('An error occurred while processing the payment.');
                } finally {
                    hideLoaderForPayment();
                }
            });



            async function updatePaymentStatus(clientSecret, selectedAmount) {
                const data = {
                    paymentIntentId: clientSecret,
                    amount: selectedAmount,
                    playerIds: playerIds,
                    programId: programId,
                    totalAmountToBePaid: fullAmountToBePaid,
                };

                try {
                    const response = await fetch(route('payment.update'), {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'X-CSRF-TOKEN': csrfToken,
                        },
                        body: JSON.stringify(data),
                    });

                    const result = await response.json();
                    if (!response.ok) {
                        throw new Error(result.error || 'Failed to update payment status.');
                    }

                    console.log('Payment status updated successfully:', result);
                    return true;


                } catch (error) {
                    console.error('Error updating payment status:', error);
                    alert('Failed to update payment status: ' + error.message);
                    return false;
                }
            }


            function showLoaderForPayment() {
                document.getElementById("submitButtonText").style.display = "none";
                document.getElementById("loaderForPaymentImage").style.display =
                    "inline-block";
                const button = document.getElementById("paymentButton");
                button.classList.add("buttonLoader");
            }

            function hideLoaderForPayment() {
                document.getElementById("submitButtonText").style.display = "inline";
                document.getElementById("loaderForPaymentImage").style.display =
                    "none";
                const button = document.getElementById("paymentButton");
                button.classList.remove("buttonLoader");
            }

            function getSelectedAmount() {
                const fullAmountRadio = document.getElementById('fullAmount');
                const specificAmountRadio = document.getElementById('specificAmount');
                const recurringPaymentRadio = document.getElementById('recurringPayments');
                const specificMoneyInput = document.getElementById('specificMoney');




                if (fullAmountRadio.checked) return {
                    type: 'one-time',
                    amount: fullAmountToBePaid
                };
                if (specificAmountRadio.checked) return {
                    type: 'specific',
                    amount: specificMoneyInput.value
                };
                if (recurringPaymentRadio.checked) {
                    const amount = document.getElementById('recurringAmount').value;
                    const startDate = document.getElementById('startDate').value;
                    const numPayments = document.getElementById('numberOfPayments').value;

                    return {
                        type: 'recurring',
                        amount,
                        startDate,
                        numPayments
                    };
                }

                return null;
            }


            const differentGuardiansRadio = document.getElementById('differentGuardiansRadio');
            const differentGuardiansSelect = document.getElementById('differentGuardians');
            const choices = new Choices(differentGuardiansSelect, {
                removeItemButton: true,
                placeholderValue: 'Select Guardians',
                searchEnabled: true,
            });

            differentGuardiansRadio.addEventListener('change', () => {
                if (differentGuardiansRadio.checked) fetchGuardians();
            });

            function fetchGuardians() {
                fetch(route('guardian.guardiansForPayment'))
                    .then((response) => {
                        if (!response.ok) throw new Error('Network response was not ok');
                        return response.json();
                    })
                    .then((data) => {
                        populateGuardians(data.guardians || []);
                    })
                    .catch((error) => console.error('Error fetching guardians:', error));
            }

            function populateGuardians(guardians) {
                choices.clearStore();
                guardians.forEach((guardian) => {
                    choices.setValue([{
                        value: guardian.id,
                        label: `${guardian.firstName} ${guardian.lastName}`,
                        selected: false,
                        disabled: false,
                    }]);
                });
            }
        });
    </script>


@endsection
--}}
