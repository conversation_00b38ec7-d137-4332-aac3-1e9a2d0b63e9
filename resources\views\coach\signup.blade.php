@extends('layouts.app')

@section('title', 'Sign up')

@section('content')
    <section class="page_title text-center pt-5">
        <h1 class="text-uppercase lh-1 mb-0">Sign up</h1>
    </section>
    <section class="sec form mb-5">
        <div class="container">
            <div class="heading align-items-center d-flex flex-column text-center mb-5">
                <span class="hero-bar d-inline-flex"></span>
            </div>
            <div class="row justify-content-center">
                <div class="col-xl-10">
                    <form class="row" method="POST" action="{{ route('coach.store') }}">
                        @csrf
                        <div class="mb-4 col-md-6">
                            <label class="form-label text-uppercase" for="first-name">PARENT/GUARDIAN FIRST NAME</label>
                            <input class="form-control" type="text" id="first-name" name="firstName"
                                value="{{ old('firstName') }}" />

                            @if ($errors->has('firstName'))
                                <span class="text-danger">{{ $errors->first('firstName') }}</span>
                            @endif
                        </div>
                        <div class="mb-4 col-md-6">
                            <label class="form-label text-uppercase" for="last-name">PARENT/GUARDIAN LAST NAME</label>
                            <input class="form-control" type="text" id="last-name" name="lastName"
                                value="{{ old('lastName') }}" />

                            @if ($errors->has('lastName'))
                                <span class="text-danger">{{ $errors->first('lastName') }}</span>
                            @endif
                        </div>
                        <div class="mb-4 col-md-6">
                            <label class="form-label text-uppercase" for="email">EMAIL</label>
                            <input class="form-control" type="email" id="email" name="email"
                                value="{{ old('email', @$email ?? '') }}" />

                            <input type="hidden" name="token" value="{{ @$token }}">

                            @if ($errors->has('email'))
                                <span class="text-danger">{{ $errors->first('email') }}</span>
                            @endif
                        </div>
                        <div class="mb-4 col-md-6">
                            <label class="form-label text-uppercase" for="town-program">TOWN/PROGRAM</label>
                            <input class="form-control" type="text" id="town-program" name="program"
                                value="{{ old('program', @$user->town ?? '') }}" />

                            @if ($errors->has('town'))
                                <span class="text-danger">{{ $errors->first('town') }}</span>
                            @endif
                        </div>

                        <div class="mb-4 col-md-6">
                            <label class="form-label text-uppercase" for="team-name">TEAM NAME</label>
                            <input class="form-control" type="text" id="team-name" name="teamName"
                                value="{{ old('team_name', @$user->teamName ?? '') }}" />

                            @if ($errors->has('teamName'))
                                <span class="text-danger">{{ $errors->first('teamName') }}</span>
                            @endif
                        </div>
                        <div class="mb-4 col-md-6 position-relative">
                            <label class="form-label text-uppercase" for="password">PASSWORD</label>
                            <div class="position-relative">
                                <input class="form-control" type="password" id="password" name="password" />
                                <div id="eyeToggle" class="eye-icon closed">
                                    <div class="eye-shape">
                                        <div class="pupil"></div>
                                        <div class="eyelid"></div>
                                    </div>
                                </div>
                            </div>

                            <div id="passwordStrengthIndicator" class="progress mt-2 d-none" style="height: 8px;">
                                <div id="passwordStrengthBar" class="progress-bar" role="progressbar" style="width: 0%;">
                                </div>
                            </div>
                            <small id="passwordStrengthText" class="text-muted d-none"></small>

                            @if ($errors->has('password'))
                                <span class="text-danger">{{ $errors->first('password') }}</span>
                            @endif
                        </div>
                        <div class="col-md-6 d-flex justify-content-end mt-5">
                            <button class="cta py-0" type="submit">SIGN UP</button>
                        </div>
                        <div class="col-md-6 d-flex mt-5">
                            <a class="cta py-0" href="{{ route('loginPage') }}">ALREADY A MEMEBER? LOGIN
                                HERE</a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </section>
    <script>
        document.getElementById('password').addEventListener('input', function() {
            passwordStrengthIndicator.classList.remove("d-none");
            passwordStrengthText.classList.remove("d-none");
            const password = this.value;
            const strengthBar = document.getElementById('passwordStrengthBar');
            const strengthText = document.getElementById('passwordStrengthText');
            let strength = '';
            let strengthClass = '';
            let width = 0;

            if (password == "") {
                passwordStrengthIndicator.classList.add("d-none");
                passwordStrengthText.classList.add("d-none");

            }

            // Determine password strength
            if (password.length < 6) {
                strength = 'Too short';
                strengthClass = 'too-short';
                width = 20;
            } else if (password.length < 8) {
                strength = 'Weak';
                strengthClass = 'weak';
                width = 40;
            } else if (password.length >= 8 && /[A-Z]/.test(password) && /\d/.test(password) && /[\W_]/.test(
                    password)) {
                strength = 'Strong';
                strengthClass = 'strong';
                width = 100;
            } else {
                strength = 'Medium';
                strengthClass = 'medium';
                width = 70;
            }
            strengthBar.style.width = `${width}%`;
            strengthBar.className = `progress-bar ${strengthClass}`;


            strengthText.textContent = `Password strength: ${strength}`;
        });

        const passwordField = document.getElementById('password');
        const eyeIcon = document.getElementById('eyeToggle');


        eyeIcon.addEventListener('click', function() {
            if (passwordField.type === 'password') {
                passwordField.type = 'text';
                eyeIcon.classList.remove('closed');
                eyeIcon.classList.add('opened');
            } else {
                passwordField.type = 'password';
                eyeIcon.classList.remove('opened');
                eyeIcon.classList.add('closed');
            }
        });
    </script>
@endsection
