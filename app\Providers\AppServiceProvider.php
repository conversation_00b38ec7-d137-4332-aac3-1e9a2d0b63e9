<?php

namespace App\Providers;

use App\Events\CoachNotFound;
use App\Events\PlayerInvitedByAdmin;
use App\Models\User;
use App\Policies\UserPolicy;
use Illuminate\Support\Facades\Gate;
use Illuminate\Support\ServiceProvider;
use Illuminate\Support\Facades\Event;
use App\Events\PlayerInvited;
use App\Listeners\SendCoachNotFoundEmail;
use App\Listeners\SendInvitationEmail;
use App\Listeners\sendInvitationEmailToPlayerByAdmin;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     */
   protected $policies=[
        User::class => UserPolicy::class,
     ];

    public function register(): void
    {

    }

    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {
       Gate::define('view-dashboard', function (User $authUser, User $user) {
           return $authUser->id === $user->id;
       });

       // Register event listeners
       Event::listen(
           PlayerInvitedByAdmin::class,
           [sendInvitationEmailToPlayerByAdmin::class, 'handle']
       );
    }
    }

