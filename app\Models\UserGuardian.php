<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class UserGuardian extends Model
{

    protected $fillable = ['user_id', 'guardian_id'];
    use HasFactory;

    public function guardian()
    {
        return $this->belongsTo(User::class, 'guardian_id');
    }

    public function user()
    {
        return $this->belongsTo(User::class, 'user_id');
    }
}
