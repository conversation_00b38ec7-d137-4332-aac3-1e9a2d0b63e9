@extends('layouts.app')

@section('title', 'Log In')

@section('content')

    <section class="page_title text-center pt-5">
        <h1 class="text-uppercase lh-1 mb-0">Login</h1>
    </section>

    <section class="sec form mb-5">
        <div class="container">
            <div class="heading align-items-center d-flex flex-column text-center mb-5"><span
                    class="hero-bar d-inline-flex"></span></div>
            <div class="row justify-content-center">

                <div class="col-xl-6">
                    @if (session('success'))
                        <div id="success-alert" class="alert alert-success alert-dismissible fade show" role="alert">
                            {{ session('success') }}
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
                    @endif


                    <form class="row" action="{{ route('login') }}" method="POST">
                        @csrf
                        <div class="mb-4 col-md-12 email-div">
                            <label class="form-label text-uppercase" for="email">Email</label>
                            <input class="form-control" id="email" type="email" name="email"
                                value="{{ old('email') }}" />
                            <div id="emailError"></div>
                            @error('email')
                                <div id="emailErrorFromBackend" class="text-danger">{{ $message }}</div>
                            @enderror
                        </div>
                        <div class="mb-4 col-md-12">
                            <label class="form-label text-uppercase" for="password">Password</label>
                            <div class="position-relative">
                                <input class="form-control" id="password" type="password" name="password" />
                                <div id="eyeToggle" class="eye-icon closed">
                                    <div class="eye-shape">
                                        <div class="pupil"></div>
                                        <div class="eyelid"></div>
                                    </div>
                                </div>
                            </div>
                            <div id="passwordError"></div>
                            @error('password')
                                <div id="passwordErrorFromBackend" class="text-danger">{{ $message }}</div>
                            @enderror
                        </div>


                        <div class="col-md-12 d-flex justify-content-center align-items-center mt-5">
                            <button id="loginButton" type="submit" class="cta py-0">Login</button>
                            <button type="button" class="cta py-0 ms-3" id="signupUser">New user</button>
                        </div>
                        <div class="col-md-12 mt-5 text-center">
                            <p><a class="navy-text text-decoration-none" href="{{ route('forgotPasswordPage') }}">Forgot
                                    password?</a></p>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </section>


    <div class="modal fade" id="signupModal" tabindex="-1" aria-labelledby="signupModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <section class="page_title text-center pt-5">
                    <span class="modal-close" data-bs-dismiss="modal" id="closeModalButton">&#x2715;</span>
                    <h5 class="text-uppercase lh-1 mb-0" style="color: #062e68">Signup</h1>
                </section>

                <div class="modal-body text-center">
                    <p class=" form-label mb-4">Choose your role to sign up:</p>
                    <div class="d-flex flex-column align-items-center gap-3">
                        <a id="signupGuardianButton" href="{{ route('guardian.signup') }}" class="cta">Sign Up as a
                            Guardian</a>
                        <a id="signupCoachButton" href="{{ route('coach.signup') }}" class="cta">Sign Up as a Coach</a>
                    </div>
                </div>
            </div>
        </div>
    </div>
    @yield('js')
    <script>
        newUserButtonElement = document.getElementById('signupUser').addEventListener('click', function() {
            const signupModal = new bootstrap.Modal(document.getElementById('signupModal'));
            signupModal.show();
        })

        const passwordField = document.getElementById('password');
        const eyeIcon = document.getElementById('eyeToggle');


        eyeIcon.addEventListener('click', function() {
            if (passwordField.type === 'password') {
                passwordField.type = 'text';
                eyeIcon.classList.remove('closed');
                eyeIcon.classList.add('opened');
            } else {
                passwordField.type = 'password';
                eyeIcon.classList.remove('opened');
                eyeIcon.classList.add('closed');
            }
        });

        document.getElementById('email').addEventListener('input', function() {
            const emailField = this;
            const passwordField = document.getElementById('password');
            if (emailField.value.endsWith('.com')) {

                passwordField.focus();
            }
        });


        let validEmail = false;

        let email = document.getElementById('email');
        let emailError = document.getElementById('emailError');
        let loginButton = document.getElementById('loginButton');
        loginButton.disabled = true;

        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;

        let emailErrorFromBackend = document.getElementById("emailErrorFromBackend");
        email.addEventListener('input', function() {

            if (emailErrorFromBackend) {
                emailErrorFromBackend.classList.add("d-none")
            }


            const emailValue = email.value.trim();

            if (emailValue === "") {
                validEmail = false;
                emailError.style.visibility = "visible";

                emailError.innerHTML = "Email can't be empty";
                loginButton.disabled = true;
            } else if (!emailRegex.test(emailValue)) {

                validEmail = false;
                emailError.style.visibility = "visible";

                emailError.innerHTML = "Email is invalid";
                loginButton.disabled = true;
            } else {

                validEmail = true;
                emailError.style.visibility = "hidden";

                emailError.innerHTML = "";
                loginButton.disabled = false;
            }
        });


        let validPassword = false;
        let password = document.getElementById('password');
        let passwordError = document.getElementById('passwordError');

        let passwordErrorFromBackend = document.getElementById("passwordErrorFromBackend")
        password.addEventListener('input', function() {

            if (passwordErrorFromBackend) {
                passwordErrorFromBackend.classList.add("d-none");
            }
            const passwordValue = password.value.trim();

            if (passwordValue === "") {

                validPassword = false;
                passwordError.style.visibility = "visible";
                passwordError.innerHTML = "Password can't be empty";
                loginButton.disabled = true;
            } else if (passwordValue.length === 1) {

                validPassword = true;
                passwordError.style.visibility = "hidden";
                passwordError.innerHTML = "";
                loginButton.disabled = false;
            } else {
                validPassword = true;
                passwordError.style.visibility = "hidden";
                passwordError.innerHTML = "";
                loginButton.disabled = false;
            }
        });



        loginButton.addEventListener('click', function(e) {

            let email = document.getElementById('email');

            let password = document.getElementById('password');

            if (email.value.trim() == "" || password.value.trim() == "") {

                if (email.value.trim() == "") {
                    showGlobalError("Email can't be empty");
                } else {
                    showGlobalError("Password can't be empty");
                }
                return;
                e.preventDefault();
            }
        })
    </script>



@endsection
