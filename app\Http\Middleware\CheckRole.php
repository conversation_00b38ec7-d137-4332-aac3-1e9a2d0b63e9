<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Symfony\Component\HttpFoundation\Response;

class CheckRole
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next, $role): Response
    {

        if (!Auth::check()) {
            return redirect('loginPage');
        }

        $user = Auth::user();

        if(!$user->roles->pluck('name')->contains($role)){
            return redirect('login')->withErrors('You do not have permission to access this page.');
        }

        return $next($request);
    }
}
