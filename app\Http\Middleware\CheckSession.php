<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;
use Illuminate\Support\Facades\Auth;

class CheckSession
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {

        if (!Auth::check()) {
            if ($request->ajax() || $request->wantsJson()) {

                return response()->json(['message' => 'Session expired. Please login again.'], 401);
            }
            return redirect()->route('loginPage')->with('error', 'Session expired. Please log in again.');
        }
        return $next($request);
    }
}
