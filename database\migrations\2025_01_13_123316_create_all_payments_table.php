<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('all_payments', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('user_id');
             $table->string('payment_id')->nullable();
             $table->decimal('paid_amount', 10, 2)->default(0);
             $table->json('program_ids')->nullable();
             $table->json('player_ids')->nullable();
             $table->string('town')->nullable();
             $table->string('payment_type')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('all_payments');
    }
};
