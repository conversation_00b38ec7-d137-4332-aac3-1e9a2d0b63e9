$(function () {
    slider();
    filterTable();
    domSelector();
    bgLogoPlace();
    seasonTabs();
    faqsTabs();
    addPlayerModal();
    addGuardianModal();

    let observer = new IntersectionObserver(
        (entries) => {
            entries.forEach((entry) => {
                if (entry.isIntersecting) {
                    let section = $(entry.target);
                    section.find("[data-counter]").each(function () {
                        counterDigit($(this));
                    });
                    observer.unobserve(entry.target);
                }
            });
        },
        {
            threshold: 0.1,
        }
    );

    $(".number-count").each(function () {
        observer.observe(this);
    });
});

$(window).on("load", function () {
    setTimeout(() => {
        $("body").removeClass("loading");
        $("#loader").fadeOut();
        setTimeout(() => {
            $("#loader").remove();
        }, 200);
    }, 200);
});

function counterDigit(element) {
    let $this = element,
        countTo = +$this.attr("data-counter"),
        countDuration = 2000;

    function formatNumber(num) {
        return num.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",");
    }

    $({ counter: 0 }).animate(
        {
            counter: countTo,
        },
        {
            duration: countDuration,
            easing: "linear",
            step: function () {
                $this.text(formatNumber(Math.floor(this.counter)));
            },
            complete: function () {
                $this.text(formatNumber(this.counter));
            },
        }
    );
}

function domSelector() {
    if ($(".cta").length > 0)
        $(".cta").addClass(
            "d-inline-flex text-decoration-none text-uppercase justify-content-center align-items-center text-center"
        );

    if ($(".hero.hero-x-factor").length > 0) $("body").addClass("x-factor");
}

function slider() {
    if ($(".slider-news").length) {
        $(".slider-news").owlCarousel({
            loop: true,
            margin: 15,
            nav: false,
            dots: false,
            responsive: {
                0: {
                    items: 1,
                },
                600: {
                    items: 2,
                },
                1000: {
                    items: 3,
                },
            },
        });

        $(".slider_meta").owlCarousel({
            loop: true,
            margin: 15,
            nav: false,
            dots: true,
            responsive: {
                0: {
                    items: 1,
                },
                600: {
                    items: 2,
                },
                1000: {
                    items: 3,
                },
            },
        });
    }
}

function filterTable() {
    $(".select-filter .selected").on("click", function () {
        $(".select-filter").removeClass("open");
        $(this).closest(".select-filter").addClass("open");
    });

    $(".heading-select").on("click", function () {
        $(".select-filter").removeClass("open");
    });

    $(".select-filter .options li").on("click", function () {
        let tag = $(this).text().trim();

        $(this).addClass("clicked");
        $(this)
            .closest(".options")
            .find(".bi-check-square")
            .addClass("bi-square")
            .removeClass("bi-check-square");
        $(this).siblings().removeClass("clicked");
        $(this)
            .find(".bi-square")
            .addClass("bi-check-square")
            .removeClass("bi-square");
        $(".select-filter").removeClass("open");

        // let plural = $('.tag').length > 1 ? 's' : '';

        // $('<div class="clear me-3 d-inline-flex">Clear Filter ' + plural + '<span class="remove ms-2"><i class="bi bi-x-circle"></i></span></div>').insertBefore($(this).closest('.table-heading').find('.title-table .copy-link'));
        // $('<div class="tag me-3 d-inline-flex">' + tag + '<span class="remove ms-2"><i class="bi bi-x-circle"></i></span></div>').insertBefore($(this).closest('.table-heading').find('.title-table .copy-link'));
    });
}

function ctaSelectors() {
    if ($(".cta"))
        $(".cta").addClass(
            "d-inline-flex justify-content-center align-items-center text-center"
        );
}

function bgLogoPlace() {
    if ($(".page_title")) {
        let bgLogo = $(".page_title").css("background-image");
        $(".page_title").removeAttr("style");
        $("body").css("background-image", bgLogo).addClass("bg-brand");
    }
}

function toggleTabs(container, itemClass, headClass, contentClass) {
    $(container).on("click", headClass, function () {
        const $this = $(this).closest(itemClass);
        $(container).find(itemClass).removeClass("active");
        $(container).find(contentClass).hide();
        $(container).find(".icon i").addClass("bi-plus").removeClass("bi-dash");
        $this.addClass("active");
        $this.find(".icon i").removeClass("bi-plus").addClass("bi-dash");
        $this.find(contentClass).show();
    });
}

function seasonTabs() {
    $("#seasonTabs .tab").on("click", function () {
        const data = $(this).data("tab");
        $("#seasonTabs .tab").removeClass("active");
        $(this).addClass("active");
        $("#seasonTabs .tab-content").hide();
        $(`#seasonTabs .tab-content[data-tab=${data ?? ""}]`).show();
    });

    toggleTabs("#seasonTabs", ".inner-acc", ".head", ".inner-content");
}

function faqsTabs() {
    toggleTabs("#seasonFaqs", ".faq-box", ".heading", ".content");
}

function addPlayerModal() {
    $(".player-meta__box .add_player").on("click", function () {
        $("#playerAdd").modal("show");
    });
}

function addGuardianModal() {
    $(".player-meta__box .add_guardian").on("click", function () {
        $("#guardianAdd").modal("show");
    });
}

//plain js

function showConfirmation(event, formId) {
    event.preventDefault();

    const confirmationOverlay = document.createElement("div");
    confirmationOverlay.id = "confirmationOverlay";
    confirmationOverlay.style.position = "fixed";
    confirmationOverlay.style.top = "0";
    confirmationOverlay.style.left = "0";
    confirmationOverlay.style.width = "100%";
    confirmationOverlay.style.height = "100%";
    confirmationOverlay.style.backgroundColor = "rgba(0, 0, 0, 0.8)";
    confirmationOverlay.style.zIndex = "1000";
    confirmationOverlay.style.display = "flex";
    confirmationOverlay.style.justifyContent = "center";
    confirmationOverlay.style.alignItems = "center";
    confirmationOverlay.style.color = "#fff";
    confirmationOverlay.style.fontFamily = "Arial, sans-serif";

    confirmationOverlay.innerHTML = `
        <div style="text-align: center; background: #222; padding: 20px; border-radius: 8px;">
            <h2>Are you sure?</h2>

            <button id="confirmYes" style="margin-right: 10px; padding: 10px 20px; background: #d9534f; color: #fff; border: none; border-radius: 5px; cursor: pointer;">Yes</button>
            <button id="confirmNo" style="padding: 10px 20px; background: #0B4499; color: #fff; border: none; border-radius: 5px; cursor: pointer;">No</button>
        </div>
    `;

    document.body.appendChild(confirmationOverlay);

    document.getElementById("confirmYes").addEventListener("click", () => {
        document.getElementById(formId).submit();
    });

    document.getElementById("confirmNo").addEventListener("click", () => {
        document.body.removeChild(confirmationOverlay);
    });
}

function showGlobalError(
    message = "An unexpected error occurred. Please try again."
) {
    if (document.querySelector(".toast.toast-error")) {
        return;
    }

    const toast = document.createElement("div");
    toast.className = "toast toast-error show text-center";
    toast.innerText = message;

    document.body.appendChild(toast);

    requestAnimationFrame(() => {
        toast.classList.add("slide-in");
    });

    setTimeout(() => {
        toast.classList.add("slide-out");
        toast.addEventListener("animationend", () => toast.remove());
    }, 5000);
}

function showGlobalSuccess(message = "Operation completed successfully.") {
    const toast = document.createElement("div");
    toast.className = "toast toast-success show text-center";
    toast.innerText = message;

    document.body.appendChild(toast);

    requestAnimationFrame(() => {
        toast.classList.add("slide-in");
    });

    setTimeout(() => {
        toast.classList.add("slide-out");
        toast.addEventListener("animationend", () => toast.remove());
    }, 5000);
}

function showSessionSuccessMessage() {
    let successMessageDiv = document.getElementById("successMessageForSession");

    if (successMessageDiv) {
        successMessageDiv.classList.add("slide-in");

        setTimeout(() => {
            successMessageDiv.classList.remove("slide-in");
            successMessageDiv.classList.add("slide-out");
        }, 4000);

        successMessageDiv.addEventListener("animationend", (event) => {
            if (event.animationName === "slide-out") {
                successMessageDiv.classList.remove("slide-out");
                successMessageDiv.style.opacity = 0;
            }
        });
    }
}

function showSessionErrorMessage() {
    let errorMessageDiv = document.getElementById("errorMessageForSession");

    if (errorMessageDiv) {
        setTimeout(() => {
            errorMessageDiv.classList.remove("slide-in");
            errorMessageDiv.classList.add("slide-out");
        }, 4000);
        errorMessageDiv.addEventListener("animationend", (event) => {
            if (event.animationName === "slide-out") {
                errorMessageDiv.classList.remove("slide-out");
                errorMessageDiv.style.opacity = 0;
            }
        });
    }
}
let offlineToast;

window.addEventListener("offline", () => {
    const message = "No Internet Connection";

    if (!offlineToast) {
        offlineToast = document.createElement("div");
        offlineToast.className = "toast toast-error show text-center";
        offlineToast.innerText = message;

        document.body.appendChild(offlineToast);

        requestAnimationFrame(() => {
            offlineToast.classList.add("slide-in");
        });
    }
});

window.addEventListener("online", () => {
    if (offlineToast) {
        offlineToast.classList.add("slide-out");
        offlineToast.addEventListener("animationend", () => {
            offlineToast.remove();
            offlineToast = null;
        });
    }
});
