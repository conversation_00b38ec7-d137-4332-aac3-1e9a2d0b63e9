    <div class="tables_heading table-program table-responsive">
        <table class="table custom-table user-management-table">
            <thead>
                <tr>
                    <th style="width: 40px" class="py-4">&nbsp;</th>
                    <th style="width: 200px" class="py-4">First Name</th>
                    <th style="width: 200px" class="py-4">Last Name</th>
                    <th style="width: 200px" class="py-4">Role</th>
                    <th style="width: 150px" class="py-4">Town</th>
                    <th style="width: 100px" class="py-4">Gender</th>
                    <th style="width: 250px" class="py-4">Email</th>
                    <th style="width: 200px" class="py-4"></th>
                </tr>
            </thead>
        </table>
    </div>

    @foreach ($users as $user)
        <div class="tables_collapse_heading table-heading-grey">
            <table class="table table-hover user-management-table">
                <tr>
                    <td style="width: 40px">
                        <div class="icon">
                            <i class="bi bi-caret-up-fill toggle-arrow" style="color:#0B4499; cursor: pointer;"
                                data-toggle="{{ $user->id }}"></i>
                        </div>
                    </td>
                    <td style="width: 200px" class="text-custom">{{ $user->firstName }}</td>
                    <td style="width: 200px" class="text-custom">{{ $user->lastName }}</td>
                    <td style="width: 200px" class="text-custom">
                        {{ implode(', ', $user->roles->pluck('name')->toArray()) }}
                    </td>
                    <td style="width: 150px" class="text-custom">{{ $user->town ?? 'N/A' }}</td>
                    <td style="width: 100px" class="text-custom">{{ $user->gender ?? 'N/A' }}</td>
                    <td style="width: 250px" class="text-custom">{{ $user->email ?? 'N/A' }}</td>


                    <td style="width: 200px">
                        <table class="user-management-actions-table">
                            <tr>
                                <td class="text-center pe-3">
                                    <i class="bi bi-filetype-xls export-xls-icon"data-user-id="{{ $user->id }}"
                                        style="color:#0B4499; font-size: 1.5rem;"></i>
                                </td>
                                <td class="text-center pe-3">
                                    <i class="bi bi-printer-fill print-icon" data-user-id="{{ $user->id }}"
                                        style="color:#0B4499; font-size: 1.5rem;"></i>
                                </td>
                                <td class="text-center pe-3">
                                    <a href="" class="action edit" id="edit-admin">
                                        <img src="{{ asset('images/edit-icon.svg') }}" alt="Edit" width="20"
                                            height="20" />
                                    </a>
                                </td>
                                <td class="text-center">
                                    <form id="deleteUser-{{ $user->id }}"
                                        onsubmit="showConfirmation(event, 'deleteUser-{{ $user->id }}')"
                                        action="{{ route('admin.destroy', ['user' => $user->id]) }}" method="POST"
                                        class="inline-form mb-0">
                                        @csrf
                                        @method('DELETE')
                                        <button type="submit" class="action edit bg-transparent border-0 p-0">
                                            <img src="{{ asset('images/delete-icon.svg') }}" alt="Delete"
                                                width="18" height="20" />
                                        </button>
                                    </form>
                                </td>
                            </tr>
                        </table>
                    </td>
                </tr>
            </table>
        </div>

        <div class="table_collapse_table table-program table-responsive table-grey">
            @if ($user->hasRole('guardian') && $user->hasRole('coach'))
                <table class="table table-hover team-table d-none" width="100%"
                    id="program-table{{ $user->id }}">
                    @if (!empty($childrens[$user->id]) && $childrens[$user->id]->isNotEmpty())
                        <thead>
                            <tr>
                                <th width="40">&nbsp;</th>
                                <th width="200">Child First Name</th>
                                <th width="200">Child Last Name</th>
                                <th width="200">Email</th>
                                <th width="200">Grade</th>
                                <th width="200">Age</th>
                                <th width="200">Gender</th>
                                <th width="200">&nbsp;</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach ($childrens[$user->id] as $child)
                                <tr>
                                    <td>&nbsp;</td>
                                    <td>{{ $child->firstName }}</td>
                                    <td>{{ $child->lastName }}</td>
                                    <td>{{ $child->email ?? 'N/A' }}</td>
                                    <td>{{ $child->grade ?? 'N/A' }}</td>
                                    <td>{{ $child->age ?? 'N/A' }}</td>
                                    <td>{{ $child->gender }}</td>
                                    <td>
                                        <table>
                                            <tr>
                                                <td class="pe-5">
                                                    <a href="#" class="action edit">
                                                        <img src="{{ asset('images/edit-icon.svg') }}" alt="Edit"
                                                            width="20" height="20" />
                                                    </a>
                                                </td>
                                                <td>
                                                    <form action="#" method="POST" style="display: inline;">
                                                        @csrf
                                                        @method('DELETE')
                                                        <button type="submit"
                                                            class="action edit bg-transparent border-0 p-0">
                                                            <img src="{{ asset('images/delete-icon.svg') }}"
                                                                alt="Delete" width="20" height="20" />
                                                        </button>
                                                    </form>
                                                </td>
                                            </tr>
                                        </table>
                                    </td>
                                </tr>
                            @endforeach
                        </tbody>
                    @else
                        <tr>
                            <td colspan="8" class="text-center">No children found</td>
                        </tr>
                    @endif
                </table>

                <table class="table table-hover team-table d-none" width="100%"
                    id="program-tableForCoachAndGuardian{{ $user->id }}">
                    @if (!empty($isPrimary[$user->id]))
                        <thead>
                            <tr>
                                <th width="40">&nbsp;</th>
                                <th width="200">Team Name</th>
                                <th width="200">Coach Type</th>
                                <th width="200">&nbsp;</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach ($isPrimary[$user->id] as $team)
                                <tr>
                                    <td>&nbsp;</td>
                                    <td>{{ $team['name'] ?? 'N/A' }}</td>
                                    <td>{{ $team['is_primary'] }}</td>
                                    <td>
                                        <table>
                                            <tr>
                                                <td class="pe-5">
                                                    <a href="#" class="action edit">
                                                        <img src="{{ asset('images/edit-icon.svg') }}" alt="Edit"
                                                            width="20" height="20" />
                                                    </a>
                                                </td>
                                                <td>
                                                    <form action="#" method="POST" style="display: inline;">
                                                        @csrf
                                                        @method('DELETE')
                                                        <button type="submit"
                                                            class="action edit bg-transparent border-0 p-0">
                                                            <img src="{{ asset('images/delete-icon.svg') }}"
                                                                alt="Delete" width="20" height="20" />
                                                        </button>
                                                    </form>
                                                </td>
                                            </tr>
                                        </table>
                                    </td>
                                </tr>
                            @endforeach
                        </tbody>
                    @else
                        <tr>
                            <td colspan="4" class="text-center">No team found</td>
                        </tr>
                    @endif
                </table>
            @elseif($user->hasRole('guardian'))
                <table class="table table-hover team-table d-none" width="100%"
                    id="program-table{{ $user->id }}">
                    @if ($childrens[$user->id]->isNotEmpty())
                        <thead>
                            <tr>
                                <th width="40">&nbsp;</th>
                                <th width="200">Child First Name</th>
                                <th width="200">Child Last Name</th>
                                <th width="200">Email</th>
                                <th width="200">Grade</th>
                                <th width="200">Age</th>
                                <th width="200">Gender</th>
                                <th width="200">&nbsp;</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach ($childrens[$user->id] as $child)
                                <tr>
                                    <td>&nbsp;</td>
                                    <td>{{ $child->firstName }}</td>
                                    <td>{{ $child->lastName }}</td>
                                    <td>{{ $child->email ?? 'N/A' }}</td>
                                    <td>{{ $child->grade ?? 'N/A' }}</td>
                                    <td>{{ $child->age ?? 'N/A' }}</td>
                                    <td>{{ $child->gender }}</td>
                                    <td>
                                        <table>
                                            <tr>
                                                <td class="pe-5">
                                                    <a href="#" class="action edit">
                                                        <img src="{{ asset('images/edit-icon.svg') }}" alt="Edit"
                                                            width="20" height="20" />
                                                    </a>
                                                </td>
                                                <td>
                                                    <form action="#" method="POST" style="display: inline;">
                                                        @csrf
                                                        @method('DELETE')
                                                        <button type="submit"
                                                            class="action edit bg-transparent border-0 p-0">
                                                            <img src="{{ asset('images/delete-icon.svg') }}"
                                                                alt="Delete" width="20" height="20" />
                                                        </button>
                                                    </form>
                                                </td>
                                            </tr>
                                        </table>
                                    </td>
                                </tr>
                            @endforeach
                        </tbody>
                    @else
                        <tr>
                            <td colspan="8" class="text-center">No children found</td>
                        </tr>
                    @endif
                </table>
            @elseif ($user->hasRole('coach'))
                <table class="table table-hover team-table d-none" width="100%"
                    id="program-table{{ $user->id }}">
                    @if (!empty($isPrimary[$user->id]))
                        <thead>
                            <tr>
                                <th width="40">&nbsp;</th>
                                <th width="200">Team Name</th>
                                <th width="200">Coach Type</th>
                                <th width="200">&nbsp;</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach ($isPrimary[$user->id] as $team)
                                <tr>
                                    <td>&nbsp;</td>
                                    <td>{{ $team['name'] ?? 'N/A' }}</td>
                                    <td>{{ $team['is_primary'] }}</td>
                                    <td>
                                        <a href="#" class="action edit">
                                            <img src="{{ asset('images/edit-icon.svg') }}" alt="Edit"
                                                width="20" height="20" />
                                        </a>
                                    </td>
                                </tr>
                            @endforeach
                        </tbody>
                    @else
                        <tr>
                            <td colspan="4" class="text-center">No team found</td>
                        </tr>
                    @endif
                </table>
            @elseif ($user->hasRole('player'))
                <table class="table table-hover team-table d-none" width="100%"
                    id="program-table{{ $user->id }}">
                    @if (!empty($programDetails[$user->id]) && count($programDetails[$user->id]) > 0)
                        <thead>
                            <tr>
                                <th width="40">&nbsp;</th>
                                <th width="200">Program Name</th>
                                <th width="200">Start Date</th>
                                <th width="200">End Date</th>
                                <th width="200">&nbsp;</th>
                            </tr>
                        </thead>
                        <tbody id="programList{{ $user->id }}">
                            @foreach ($programDetails[$user->id] as $program)
                                <tr>
                                    <td>&nbsp;</td>
                                    <td>{{ $program->name }}</td>
                                    <td>{{ $program->start_date }}</td>
                                    <td>{{ $program->end_date }}</td>
                                    <td>
                                        <a href="#" class="action edit">
                                            <img src="{{ asset('images/edit-icon.svg') }}" alt="Edit"
                                                width="20" height="20" />
                                        </a>
                                    </td>
                                </tr>
                            @endforeach
                        </tbody>
                    @else
                        <tr>
                            <td colspan="5" class="text-center">No program joined</td>
                        </tr>
                    @endif
                </table>

                @if (!empty($programDetails[$user->id]) && $programDetails[$user->id]->hasMorePages())
                    <div id="load-more-container-{{ $user->id }}" class="d-none">
                        <button id="loadMorePrograms{{ $user->id }}"
                            data-page="{{ $programDetails[$user->id]->currentPage() + 1 }}"
                            data-user-id="{{ $user->id }}" class="btn btn-primary">
                            Load More
                        </button>
                    </div>
                @endif
            @endif
        </div>
    @endforeach

    <div class="mt-4 d-flex justify-content-center pagination" style="margin-bottom:5px;">
        {{ $users->onEachSide(1)->links('pagination::bootstrap-5') }}
    </div>
