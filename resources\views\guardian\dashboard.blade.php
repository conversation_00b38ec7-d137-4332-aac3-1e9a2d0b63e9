@extends('layouts.app')

@section('title', 'dashboard')

@section('content')
    <section class="page_title text-center pt-5">
        <h1 class="text-uppercase lh-1 mb-0">Welcome {{ $user->firstName }} {{ $user->lastName }}</h1>
        @if ($errors->any())
            <div class="bg-red-500 text-white p-4 mb-4 text-center">
                <p>{{ $errors->first() }}</p>
            </div>
        @endif


        @if (session('success'))
            <div class="alert alert-success" id="successAlert" role="alert" style="max-width: 600px; margin: 0 auto;">
                {{ session('success') }}
            </div>
        @endif



        @if (@$user->roles()->where('name', 'coach')->exists())
            <form id="roleSwitchForm">

                <button class="cta mt-4" id="switchRoleToCoach" data-guardian-switch-id="{{ $user->id }}">Switch to
                    Coach Mode</button>
            </form>
        @endif
    </section>

    <!-- All Guardians -->

    <section class="sec program-welcome">
        <div class="container">
            <div class="heading align-items-center d-flex flex-column text-center mb-5">
                <span class="hero-bar d-inline-flex mb-5"></span>
                <div class="d-flex">
                    <!-- Outstanding Balance Column -->
                    <div class="d-flex flex-column-reverse me-5">
                        <h2 class="mb-0">${{ number_format(@$totalAmount ?? 0, 2) }}</h2>

                        <h3 class="text-uppercase mb-4">Outstanding Balance:</h3>
                    </div>
                    <!-- Recurring Balance Column -->
                    <div class="d-flex flex-column-reverse">
                        <h2 class="mb-0">${{ number_format($recurringAmount ?? 0, 2) }}</h2>
                        <h3 class="text-uppercase mb-4">Recurring Balance:</h3>
                    </div>


                </div>
                <a class="cta mt-4" href="{{ route('guardian.showPaymentOptions') }}">Payment options</a>
            </div>
            <div class="row player-meta justify-content-center">
                <div class="col-md-6">
                    <div class="heading text-center">
                        <h2 class="fs-6 text-uppercase mb-3">Guardian:</h2>
                    </div>
                    <div class="player-meta__box d-flex flex-column text-center align-items-center justify-content-center">
                        <p>
                            {{ $user->firstName }} {{ $user->lastName }}<br />
                            {{ $user->email }}
                        </p>
                        <div class="edit d-flex" data-guardian-id="{{ $user->id }}"
                            data-loader-guardian-url="{{ asset('images/ripple-loading.svg') }}"><img
                                src="images/edit-icon.svg" alt="" width="20" height="20"
                                id="editIcon-{{ $user->id }}" /></div>
                    </div>
                    @if (!$additionalGuardians->isEmpty())
                        @foreach ($additionalGuardians as $guardian)
                            <div class="player-meta__box d-flex flex-column text-center align-items-center justify-content-center"
                                id="guardians-container">
                                <p>
                                    {{ $guardian->firstName }} {{ $guardian->lastName }}<br />
                                    {{ $guardian->email }}
                                </p>
                                <div class="edit d-flex" data-additionalguardian-id="{{ $guardian->id }}"
                                    id="lastGuardianId">
                                    <!-- <img
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        src="images/edit-icon.svg" alt="" width="20" height="20" /> -->
                                </div>
                            </div>
                        @endforeach
                    @endif
                    <div id="guardians-pagination" class= "text-center align-items-center justify-content-center mt-1">
                        @if ($additionalGuardians->hasMorePages())
                            <button class="cta" id="load-more-guardians"
                                data-url="{{ $additionalGuardians->nextPageUrl() }}">Show More Guardians</button>
                        @endif
                    </div>
                    <div class="player-meta__box d-flex flex-column text-center align-items-center justify-content-center">
                        <button class="cta add_guardian" id="additional-guardian-add-button">Add Additional
                            guardian</button>
                    </div>
                </div>

                <!-- All Players -->

                <div class="col-md-6">
                    <div class="heading text-center">
                        <h2 class="fs-6 text-uppercase mb-3">Player:</h2>
                    </div>


                    @if ($players->isEmpty())
                        <div
                            class="player-meta__box d-flex flex-column text-center align-items-center justify-content-center">

                            <button class="cta add_player">Add Player</button>
                        </div>
                    @else
                        @foreach ($players as $player)
                            <div
                                class="player-meta__box d-flex flex-column text-center align-items-center justify-content-center">
                                <p>
                                    <a href="{{ route('guardian.player.profileShow', ['playerId' => $player->id]) }}"
                                        class="player-profile">
                                        {{ e($player->firstName) }} {{ e($player->lastName) }}
                                    </a><br />
                                    @if ($player->email && $player->email != null)
                                        {{ $player->email }}
                                    @else
                                        N/A
                                    @endif
                                </p>
                                <div class="edit d-flex" data-player-id="{{ $player->id }}"><img
                                        src="images/edit-icon.svg" alt="" width="20" height="20"
                                        id="editIcon-{{ $player->id }}"
                                        data-loader-player-url="{{ asset('images/ripple-loading.svg') }}" /></div>
                            </div>
                        @endforeach
                        <div id="players-pagination" class="text-center align-items-center justify-content-center mt-1">
                            @if ($players->hasMorePages())
                                <button class="cta" id="load-more-players" data-url="{{ $players->nextPageUrl() }}">Show
                                    More Players</button>
                            @endif
                        </div>
                    @endif


                    <div class="player-meta__box d-flex flex-column text-center align-items-center justify-content-center">

                        <button class="cta add_player" id="addNewPlayer">Add Player</button>

                    </div>
                </div>
            </div>
        </div>
    </section>

    <!--Available Program for Players -->

    <section class="sec partition-hr">
        <div class="container">
            <hr class="border-navy opacity-100 my-0" />
        </div>
    </section>
    <section class="sec program-table">
        <div class="container">
            <div class="heading text-center mb-5">
                <h2 class="text-uppercase fs-6 mb-0">Available Programs:</h2>
            </div>
            @if ($availablePrograms->isEmpty())
                <div class="cta-row text-center mt-4 text-uppercase fs-6 mb-0"
                    style="font: 14px / 1 'Montserrat', sans-serif; color: #0b4499;">
                    No Programs Available
                </div>
            @else
                <div class="table-program table-responsive">
                    <table class="table table-hover" width="100%">
                        <tbody>
                            <tr>
                                <th>Name</th>
                                <th>Sub Program</th>
                                <th>Start Date</th>
                                <th>Day of the Week</th>
                                <th>Individual Price</th>
                                <th>Location</th>
                                <th>SIGN UP</th>
                            </tr>
                            @foreach ($availablePrograms as $program)
                                <tr>
                                    <td valign="middle"> <a
                                            href="{{ route('program.show', ['program' => $program->slug]) }}"
                                            class="text-capitalize">{{ $program->name }}</a>
                                    </td>
                                    <td valign="middle"><a href="#" onclick="return false;"
                                            class="text-capitalize">{{ $program->sub_program }}</a>
                                    <td valign="middle">
                                        {{ \Carbon\Carbon::parse($program->start_date)->format('m-d-Y') }}
                                    </td>
                                    <td valign="middle" class="text-capitalize">
                                        @if (is_array($program->frequency_days))
                                            {{ strtoupper(implode(', ', $program->frequency_days)) }}
                                        @else
                                            {{ strtoupper($program->frequency_days) }}
                                        @endif
                                    </td>
                                    <td valign="middle">${{ number_format($program->cost, 2) }}</td>
                                    <td valign="middle" class="text-capitalize">{{ $program->location }}</td>
                                    <td valign="middle"><a
                                            href="{{ route('program.show', ['program' => $program->slug]) }}"
                                            class="cta hover-dark">SIGN UP</a></td>
                                </tr>
                            @endforeach
                        </tbody>
                    </table>
                    <div class="mt-6">
                        {{ $availablePrograms->links('pagination::tailwind') }}
                    </div>
                </div>
            @endif
        </div>
    </section>

    <!--Invitation for Player -->

    <section class="sec program-table">
        <div class="container">
            <div class="heading text-center mb-5">
                <h2 class="text-uppercase fs-6 mb-0">Team Invitation:</h2>
            </div>
            <div class="table-program table-responsive">
                <table class="table table-hover" width="100%">
                    <tbody>
                        <tr>
                            <th class="py-0"></th>
                            <th class="py-0"></th>
                        </tr>
                        @if ($notifications->isEmpty())
                            <div class="cta-row text-center mt-4 text-uppercase fs-6 mb-0"
                                style="font: 14px / 1 'Montserrat', sans-serif; color: #0b4499;">
                                No Invitations
                            </div>
                        @else
                            @foreach (@$notifications as $notification)
                                <tr>
                                    <td class="navy-text" valign="middle">
                                        @if(@$notification->is_post_tryout)
                                            <strong>Admin {{ @$notification->inviter_name }}</strong> has invited
                                            <strong>{{ @$notification->player_name }}</strong> to join the Team
                                            <strong>{{ @$notification->team_name }}</strong>
                                            <br><small class="text-muted">Post-Tryout Program: {{ @$notification->program_name }}</small>
                                        @else
                                            <strong>Coach {{ @$notification->inviter_name }}</strong> has invited
                                            <strong>{{ @$notification->player_name }}</strong> to join the Team
                                            <strong>{{ @$notification->team_name }}</strong>
                                        @endif
                                    </td>
                                    <td valign="middle" width="140">
                                        <div class="d-flex gap-2">
                                            <form action="{{ route('guardian.acceptInvitation') }}" method="POST" style="display: inline;">
                                                @csrf
                                                <input type="hidden" name="invitation_id" value="{{ @$notification->team_id }}">
                                                <input type="hidden" name="player_id" value="{{ @$notification->player_id }}">
                                                <input type="hidden" name="program_id" value="{{ @$notification->program_id }}">
                                                <button class="cta hover-dark text-nowrap btn-sm">Accept</button>
                                            </form>

                                            @if(@$notification->is_post_tryout)
                                                <button class="btn btn-outline-danger btn-sm text-nowrap reject-invitation-btn"
                                                        data-invitation-id="{{ @$notification->id }}"
                                                        data-player-name="{{ @$notification->player_name }}"
                                                        data-team-name="{{ @$notification->team_name }}">
                                                    Reject
                                                </button>
                                            @endif
                                        </div>
                                    </td>
                                </tr>
                            @endforeach
                        @endif
                    </tbody>
                </table>
            </div>
        </div>
    </section>

    <section class="sec program-table">
        <div class="container">
            <div class="heading text-center mb-5">
                <h2 class="text-uppercase fs-6 mb-0">program invites</h2>
            </div>
            <div class="table-program table-responsive">
                <table class="table table-hover" width="100%">
                    <tbody>
                        <tr>
                            <th class="py-0"></th>
                            <th class="py-0"></th>
                        </tr>
                        @if (empty($adminNotifications))
                            <div class="cta-row text-center mt-4 text-uppercase fs-6 mb-0"
                                style="font: 14px / 1 'Montserrat', sans-serif; color: #0b4499;">
                                No Invites
                            </div>
                        @else
                            @foreach ($adminNotifications as $notification)
                                <tr>
                                    <td class="navy-text" valign="middle">
                                        <strong>Player {{ $notification['firstName'] }} {{ $notification['lastName'] }}
                                            has been invited to join the
                                            Program
                                            <strong>{{ $notification['program_slug'] }}</strong></strong>
                                    </td>
                                    <td valign="middle" width="140"><button
                                            id="acceptInviteFor{{ $notification['player_id'] }}"
                                            class="cta hover-dark text-nowrap join-now-btn"
                                            data-program-slug="{{ $notification['program_slug'] }}">Join
                                            Now</button></td>
                                </tr>
                            @endforeach
                        @endif
                    </tbody>
                </table>
            </div>
        </div>
    </section>

    <!--Past Teams -->

    <section class="sec program-table">
        <div class="container">
            <div class="heading text-center mb-5">
                <h2 class="text-uppercase fs-6 mb-0">PAST TEAMS, CAMPS & CLINICS:</h2>
            </div>
            <div class="table-program table-responsive">
                <table class="table table-hover" width="100%">
                    <tbody>
                        @if ($pastPrograms->isEmpty())
                            <div class="cta-row text-center mt-4 text-uppercase fs-6 mb-0"
                                style="font: 14px / 1 'Montserrat', sans-serif; color: #0b4499;">
                                Unable To Find Past Programs
                            </div>
                        @else
                            @foreach ($pastPrograms as $program)
                                <tr>
                                    <td valign="middle">
                                        <a href="{{ route('program.show', ['program' => $program->slug]) }}"
                                            class="text-capitalize">{{ $program->name }}</a>
                                    </td>
                                    <td valign="middle"><a href="#" onclick="return false;"
                                            class="text-capitalize">{{ $program->sub_program }}</a>
                                    </td>
                                    <td valign="middle">{{ $program->start_date }}</td>
                                    <td valign="middle" class="text-capitalize">
                                        @if (is_array($program->frequency_days))
                                            {{ strtoupper(implode(', ', $program->frequency_days)) }}
                                        @else
                                            {{ strtoupper($program->frequency_days) }}
                                        @endif
                                    </td>

                                    <td valign="middle">${{ number_format($program->cost, 2) }}</td>
                                    <td valign="middle text-capitalize">{{ $program->location }}</td>
                                </tr>
                            @endforeach
                        @endif
                    </tbody>
                </table>
            </div>
        </div>
    </section>

    <!--player Add form-->

    <div class="modal fade" id="playerAdd" tabindex="-1" aria-labelledby="playerAddLabel" aria-hidden="true"
        data-bs-backdrop="static">
        <div class="modal-dialog modal-dialog-centered" style="max-width: 1160px">

            <div class="modal-content">
                <div class="modal-body p-5">
                    <span class="modal-close" data-bs-dismiss="modal">&#x2715;</span>

                    <form class="form row" id="submitPlayerData">
                        <div id="successMessage" class="alert alert-success d-none" role="alert"></div>
                        <div id="errorMessage" class="alert alert-danger d-none" role="alert"></div>
                        @csrf
                        <div class="mb-4 col-md-6">
                            <label class="form-label text-uppercase" for="playerFirstName">Player First Name</label>
                            <input class="form-control" id="playerFirstName" type="text" name="firstName" />
                            <div class="invalid-feedback" id="firstNameError"></div>
                        </div>
                        <div class="mb-4 col-md-6">
                            <label class="form-label text-uppercase" for="playerLastName">Player Last Name</label>
                            <input class="form-control" id="playerLastName" type="text" name="lastName" />
                            <div class="invalid-feedback" id="lastNameError"></div>
                        </div>
                        <div class="mb-4 col-md-6">
                            <label class="form-label text-uppercase" for="email">Email (optional)</label>
                            <input class="form-control" id="email" type="text" name="email"
                                autocomplete="email" />
                            <div class="invalid-feedback" id="emailError"></div>
                        </div>

                        <div class="mb-4 col-md-6">
                            <label class="form-label text-uppercase" for="playerGender">Gender</label>
                            <div class="select-arrow position-relative">
                                <select class="form-control" name="gender" id="playerGender">
                                    <option value="">Select Gender</option>
                                    <option value="boy">Boy</option>
                                    <option value="girl">Girl</option>
                                </select>

                                <span class="arrow"><i class="bi bi-chevron-down"></i></span>

                            </div>
                            <div class="invalid-feedback d-none" id="genderError"></div>
                        </div>

                        <div class="mb-4 col-md-6">
                            <label class="form-label text-uppercase" for="birthdate">Birth date</label>
                            <input class="form-control" id="birthdate" type="date" name="birthDate" />
                            <div class="invalid-feedback" id="birthDateError"></div>
                        </div>
                        <div class="mb-4 col-md-6">
                            <label class="form-label text-uppercase" for="gradeEnteringintheFall">Grade (Entering in the
                                Fall)</label>
                            <input class="form-control" id="gradeEnteringintheFall" type="text" name="grade" />
                            <div class="invalid-feedback" id="gradeError"></div>
                        </div>
                        <div class="mb-4 col-md-6">
                            <label class="form-label text-uppercase" for="street">Street</label>
                            <input class="form-control" id="street" type="text" name="street" />
                            <div class="invalid-feedback" id="streetError"></div>
                        </div>

                        <div class="mb-4 col-md-6">
                            <label class="form-label text-uppercase" for="town">Town</label>
                            <input class="form-control" id="town" type="text" name="town" />
                            <div class="invalid-feedback" id="townError"></div>
                        </div>

                        <div class="mb-4 col-md-12">
                            <label class="form-label text-uppercase" for="state">State</label>
                            <input class="form-control" id="state" type="text" name="state" />
                            <div class="invalid-feedback" id="stateError"></div>
                        </div>

                        <div class="col-md-6">
                            <div class="upload-pic d-flex align-items-center">
                                <div class="form-label text-uppercase me-4">Profile Photo (optional)</div>
                                <div class="icon-upload" style="cursor: pointer">
                                    <img src="{{ asset('images/upload-icon.svg') }}" alt="Upload Icon" width="40"
                                        height="50" id="uploadIcon" />
                                </div>
                                <input type="file" id="profilePhoto" name="profilePhoto" accept="image/*"
                                    style="display: none;" />
                                <div class="invalid-feedback" id="profilePhotoError"></div>
                            </div>
                            <div class="file-name mt-2" id="selectedFileName">No file selected</div>
                            <img id="imagePreview" src="" alt="Image Preview" class="mt-2"
                                style="max-width: 100px; max-height: 100px; display: none;" />
                        </div>

                        <!-- if the parent_id of current guardian is null then he is main guardian, if he has parent_id then we will make him the parent but the primary parent will be the main guardian -->
                        @if ($user->parent_id == null)
                            <input type="hidden" name="parent_id" value="{{ $user->id }}" id="guardianIdInput">
                            <input type="hidden" name="primary_parent_id" value="{{ $user->id }}">
                        @else
                            <input type="hidden" name="parent_id" value="{{ $user->id }}" id="guardianIdInput">
                            <input type="hidden" name="primary_parent_id" value="{{ $user->primary_parent_id }}">
                        @endif

                        <div class="col-md-6 d-flex align-items-center justify-content-end" style="min-height: 40px">
                            <button class="cta" id="addPlayerButton"> <span id="addPlayerButtonText"> Add
                                    Player</span>
                                <span id="loaderForAddPlayer">
                                    <img id="loaderForAddPlayerImage" src="{{ asset('images/loader.svg') }}"
                                        alt="Loading..."
                                        style="display: none; width: 40px; height: 40px; margin-left: 10px;"></span></button>


                        </div>
                    </form>

                </div>
            </div>
        </div>
    </div>


    <!--Player Edit form -->

    <div class="modal fade" id="playerEdit" tabindex="-1" aria-labelledby="playerEditLabel" aria-hidden="true"
        data-bs-backdrop="static">

        <div class="modal-dialog modal-dialog-centered" style="max-width: 1160px">

            <div class="modal-content">
                <div class="modal-body p-5">
                    <span class="modal-close" data-bs-dismiss="modal">&#x2715;</span>

                    <!-- Edit Player Form -->
                    <form class="form row" id="editPlayerData">
                        <div id="editSuccessMessage" class="alert alert-success d-none" role="alert"></div>
                        <div id="editErrorMessage" class="alert alert-danger d-none" role="alert"></div>
                        @csrf
                        <input type="hidden" name="player_id" id="playerIdInput">
                        <div class="mb-4 col-md-6">
                            <label class="form-label text-uppercase" for="editFirstName">Player First Name</label>
                            <input class="form-control" id="editFirstName" type="text" name="firstName" />
                            <div class="invalid-feedback" id="editFirstNameError"></div>
                        </div>
                        <div class="mb-4 col-md-6">
                            <label class="form-label text-uppercase" for="editLastName">Player Last Name</label>
                            <input class="form-control" id="editLastName" type="text" name="lastName" />
                            <div class="invalid-feedback" id="editLastNameError"></div>
                        </div>
                        <div class="mb-4 col-md-6">
                            <label class="form-label text-uppercase" for="editEmail">Email (optional)</label>
                            <input class="form-control" id="editEmail" type="text" name="email"
                                autocomplete="editEmail" />
                            <div class="invalid-feedback" id="editEmailError"></div>
                        </div>

                        <div class="mb-4 col-md-6">
                            <label class="form-label text-uppercase" for="playerGender">Gender</label>
                            <div class="select-arrow position-relative">
                                <select class="form-control" name="gender" id="editGender">
                                    <option value="">Select Gender</option>
                                    <option value="boy">Boy</option>
                                    <option value="girl">Girl</option>
                                </select>

                                <span class="arrow"><i class="bi bi-chevron-down"></i></span>

                            </div>
                            <div class="invalid-feedback d-none" id="genderError"></div>
                        </div>




                        <div class="mb-4 col-md-6">
                            <label class="form-label text-uppercase" for="editBirthdate">Birth date</label>
                            <input class="form-control" id="editBirthdate" type="date" name="birthDate" />
                            <div class="invalid-feedback" id="editBirthDateError"></div>
                        </div>
                        <div class="mb-4 col-md-6">
                            <label class="form-label text-uppercase" for="editGrade">Grade (Entering in the Fall)</label>
                            <input class="form-control" id="editGrade" type="text" name="grade" />
                            <div class="invalid-feedback" id="editGradeError"></div>
                        </div>

                        <div class="mb-4 col-md-6">
                            <label class="form-label text-uppercase" for="editStreet">Street</label>
                            <input class="form-control" id="editStreet" type="text" name="street" />
                            <div class="invalid-feedback" id="editStreetError"></div>
                        </div>

                        <div class="mb-4 col-md-6">
                            <label class="form-label text-uppercase" for="editTown">Town</label>
                            <input class="form-control" id="editTown" type="text" name="town" />
                            <div class="invalid-feedback" id="editTownError"></div>
                        </div>

                        <div class="mb-4 col-md-12">
                            <label class="form-label text-uppercase" for="editState">State</label>
                            <input class="form-control" id="editState" type="text" name="state" />
                            <div class="invalid-feedback" id="editStateError"></div>
                        </div>


                        <div class="col-md-6">
                            <div class="upload-pic d-flex align-items-center">
                                <div class="form-label text-uppercase me-4">Profile Photo (optional)</div>
                                <div class="icon-upload" style="cursor: pointer">
                                    <img src="images/upload-icon.svg" alt="Upload Icon" width="40" height="50"
                                        id="editUploadIcon" />
                                </div>
                                <input type="file" id="editProfilePhoto" name="profilePhoto" accept="image/*"
                                    style="display: none;" />
                                <div class="invalid-feedback" id="editProfilePhotoError"></div>
                            </div>
                            <div class="file-name mt-2" id="editSelectedFileName">No New file selected</div>
                            <img id="editImagePreview" src="" alt="Image Preview" class="mt-2"
                                style="max-width: 100px; max-height: 100px; display: none;" />
                        </div>
                        <div class="col-md-6 d-flex align-items-center justify-content-end" style="min-height: 40px">
                            <button class="cta" id="editPlayerButton">Save Changes</button>
                        </div>
                    </form>

                </div>
            </div>
        </div>
    </div>


    <!--Guardian Add form -->
    <div class="modal fade" id="guardianAdd" tabindex="-1" aria-labelledby="guardianAddLabel" aria-hidden="true"
        data-bs-backdrop="static">

        <div class="modal-dialog modal-dialog-centered" style="max-width:640px;">

            <div class="modal-content">
                <div class="modal-body p-5">
                    <span class="modal-close" data-bs-dismiss="modal">&#x2715;</span>
                    <!-- Add Guardian Form -->
                    <form class="form row" id="submitGuardianData">
                        @csrf
                        <div id="guardianSuccessMessage" class="alert alert-success d-none" role="alert"></div>
                        <div id="guardianErrorMessage" class="alert alert-danger d-none" role="alert"></div>
                        <div class="mb-4 col-md-12">
                            <label class="form-label text-uppercase" for="addFirstName">First Name</label>
                            <input class="form-control" id="addFirstName" type="text" name="firstName" />
                            <div class="invalid-feedback" id="addFirstNameError"></div>
                        </div>
                        <div class="mb-4 col-md-12">
                            <label class="form-label text-uppercase" for="addLastName">Last Name</label>
                            <input class="form-control" id="addLastName" type="text" name="lastName" />
                            <div class="invalid-feedback" id="addLastNameError"></div>
                        </div>
                        <div class="mb-4 col-md-12">
                            <label class="form-label text-uppercase" for="editEmail">Email</label>
                            <input class="form-control" id="addEmail" type="text" name="email"
                                autocomplete="addEmail" />
                            <div class="invalid-feedback" id="addEmailError"></div>
                        </div>
                        <div class="col-md-12 d-flex align-items-center justify-content-end" style="min-height: 40px">
                            <button class="cta" id="addGuardianButton"> <span id="inviteGuardianButtonText">Add
                                    Guardian</span>
                                <span id="loaderForInviteGuardian">
                                    <img id="loaderForInviteGuardianImage" src="{{ asset('images/loader.svg') }}"
                                        alt="Loading..."
                                        style="display: none; width: 40px; height: 40px; margin-left: 10px;"></span></button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!--Guardian Edit form -->

    <div class="modal fade" id="guardianEdit" tabindex="-1" aria-labelledby="guardianAddLabel" aria-hidden="true"
        data-bs-backdrop="static">
        <div class="modal-dialog modal-dialog-centered" style="max-width:640px;">

            <div class="modal-content">
                <div class="modal-body p-5">
                    <span class="modal-close" data-bs-dismiss="modal">&#x2715;</span>
                    <!-- Edit Guardian Form -->
                    <form class="form row" id="editGuardianData">
                        @csrf
                        <input type="hidden" name="guardian_id" id="guardianIdInput">
                        <div id="editGuardianSuccessMessage" class="alert alert-success d-none" role="alert"></div>
                        <div id="editGuardianErrorMessage" class="alert alert-danger d-none" role="alert"></div>
                        <div class="mb-4 col-md-12">
                            <label class="form-label text-uppercase" for="addFirstName">First Name</label>
                            <input class="form-control" id="editGuardianFirstName" type="text" name="firstName" />
                            <div class="invalid-feedback" id="addFirstNameError"></div>
                        </div>
                        <div class="mb-4 col-md-12">
                            <label class="form-label text-uppercase" for="addLastName">Last Name</label>
                            <input class="form-control" id="editGuardianLastName" type="text" name="lastName" />
                            <div class="invalid-feedback" id="addLastNameError"></div>
                        </div>
                        <div class="mb-4 col-md-12">
                            <label class="form-label text-uppercase" for="editEmail">Email</label>
                            <input class="form-control" id="editGuardianEmail" type="text" name="email"
                                autocomplete="editGuardianEmail" />
                            <div class="invalid-feedback" id="addEmailError"></div>
                        </div>

                        <div class="mb-4 col-md-12">
                            <label class="form-label text-uppercase" for="editGuardianMobile">Phone</label>
                            <input class="form-control" id="editGuardianMobile" type="text" name="mobile_number" />
                            <div class="invalid-feedback" id="addMobileError"></div>
                        </div>
                        <div class="col-md-12 d-flex align-items-center justify-content-end" style="min-height: 40px">
                            <button class="cta" id="editGuardianButton">Save Changes</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
    <script>
        const csrfToken = "{{ csrf_token() }}";
    </script>
    <script src="{{ asset('js/guardian.dashboard.js') }}"></script>
@endsection
