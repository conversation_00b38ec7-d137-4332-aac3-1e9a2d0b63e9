@extends('layouts.app')
@section('title', 'Admin')
@section('content')
    <section class="page_title text-center pt-5">
        <h1 class="text-uppercase lh-1 mb-0">Report</h1>
    </section>
    <section class="sec admin-welcome">
        <div class="container">
            <div class="heading align-items-center d-flex flex-column text-center">
                <span class="hero-bar d-inline-flex mb-5"></span>
            </div>
            <div class="container">

                @isset($playerData)
                    <div class="tables_heading table-program table-responsive">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th width="40" class="py-4">&nbsp;</th>
                                    <th width="200" class="py-4">FirsName</th>
                                    <th width="200" class="py-4">LastName</th>
                                    <th width="200" class="py-4">Gender</th>
                                    <th width="200" class="py-4">Age</th>
                                    <th width="200" class="py-4">Email</th>
                                    <th width="200">&nbsp;</th>
                                    <th width="200">&nbsp;</th>
                                    <th width="200">&nbsp;</th>
                                </tr>
                            </thead>
                        </table>
                    </div>

                    <div class="tables_collapse_heading table-heading-grey">
                        <table class="table table-hover">
                            <tr>
                                <th width="40">
                                    <div class="icon"><i class="bi bi-caret-up-fill toggle-arrow"
                                            style="color:#0B4499; cursor: pointer;" data-toggle="{{ $player->id }}"></i>
                                    </div>
                                </th>
                                <th width="200"
                                    style="color: #0B4499; font-size: 14px; font-family: 'Montserrat', sans-serif;">
                                    {{ $player->firstName }}
                                </th>
                                <th width="200"
                                    style="color: #0B4499; font-size: 14px; font-family: 'Montserrat', sans-serif;">
                                    {{ $player->lastName }}
                                </th>
                                <th width="200"
                                    style="color: #0B4499; font-size: 14px; font-family: 'Montserrat', sans-serif;">
                                    {{ $player->gender }}
                                </th>
                                <th width="200"
                                    style="color: #0B4499; font-size: 14px; font-family: 'Montserrat', sans-serif;">
                                    {{ $player->age }}
                                </th>

                                <th width="200"
                                    style="color: #0B4499; font-size: 14px; font-family: 'Montserrat', sans-serif;">
                                    {{ $player->email ?? 'N/A' }}

                                </th>
                                <th width="200">&nbsp;</th>
                                <th width="200">&nbsp;</th>
                                <th width="200">
                                    <table>
                                        <tr>
                                            <td class="text-center pe-3">
                                                <i class="bi bi-filetype-xls export-xls-icon"
                                                    style="color: #0B4499; font-size: 1.5rem;"></i>
                                            </td>
                                            <td class="text-center pe-3">
                                                <i class="bi bi-printer-fill print-icon"
                                                    style="color: #0B4499; font-size: 1.5rem;"></i>
                                            </td>
                                            <td class="text-center pe-3">
                                                <a href="" class="action edit" id="edit-admin">
                                                    <img src="{{ asset('images/edit-icon.svg') }}" alt="Edit" width="20"
                                                        height="20" />
                                                </a>
                                            </td>
                                            <td class="text-center">
                                                <form id="" action="#" method="POST" style="display: inline;">
                                                    @csrf
                                                    @method('DELETE')
                                                    <button type="submit"
                                                        style="border: none; background: none; padding: 0; cursor: pointer;">
                                                        <span class="action delete">
                                                            <img src="{{ asset('images/delete-icon.svg') }}" alt="Delete"
                                                                width="20" height="20" />
                                                        </span>
                                                    </button>
                                                </form>
                                            </td>
                                        </tr>
                                    </table>
                                </th>
                            </tr>
                        </table>
                    </div>


                    <div class="table_collapse_table table-program table-responsive table-grey">
                        <table class="table table-hover team-table d-none" width="100%" id="program-table{{ $player->id }}">
                            <tr>
                                <th width="40">
                                    &nbsp;</th>
                                <th width="200">Program</th>
                                <th width="200">Start Date</th>
                                <th width="250">End Date</th>
                                <th width="250"></th>
                                <th width="200"></th>
                                <th width="200"></th>
                                <th width="200">&nbsp;</th>
                            </tr>
                            @foreach ($programsOfPlayer as $program)
                                <tr>
                                    <td>&nbsp;</td>
                                    <td>{{ $program->name }}</td>
                                    <td>{{ $program->start_date }}</td>

                                    <td>{{ $program->end_date }}</td>
                                    <td></td>
                                    <td class="pl-1"></td>
                                    <td></td>
                                    <td>
                                        <table>
                                            <tr>
                                                <td class="pe-5">
                                                    <a href="#" class="action edit">
                                                        <img src="{{ asset('images/edit-icon.svg') }}" alt="Edit"
                                                            width="20" height="20" />
                                                    </a>
                                                </td>
                                                <td>
                                                    <form id="removeTeamFromProgram" action="" method="POST"
                                                        style="display: inline;">
                                                        @csrf
                                                        @method('DELETE')
                                                        <button type="submit" class="btn btn-link p-0 border-0"
                                                            style="background: none;">
                                                            <img src="{{ asset('images/delete-icon.svg') }}" alt="Delete"
                                                                width="20" height="20" />
                                                        </button>
                                                    </form>
                                                </td>

                                            </tr>
                                        </table>
                                    </td>
                                </tr>
                            @endforeach
                        </table>
                    </div>
                @endisset

                @isset($programs)
                    <div class="tables_heading table-program table-responsive">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th width="40" class="py-4">&nbsp;</th>
                                    <th width="200" class="py-4">Program Name</th>
                                    <th width="200" class="py-4">Sport</th>
                                    <th width="200" class="py-4">Start Date</th>
                                    <th width="200" class="py-4">End Date</th>
                                    <th width="200" class="py-4">Frequency</th>
                                    <th width="200" class="py-4">Payment</th>
                                    <th width="200" class="py-4">Players Enrolled</th>


                                    <th width="200">&nbsp;</th>
                                    <th width="200">&nbsp;</th>
                                    <th width="200">&nbsp;</th>
                                </tr>
                            </thead>
                        </table>
                    </div>
                    @foreach ($programs as $program)
                        <div class="tables_collapse_heading table-heading-grey">
                            <table class="table table-hover">
                                <tr>
                                    <th width="40">
                                        <div class="icon"><i class="bi bi-caret-up-fill toggle-arrow"
                                                style="color:#0B4499; cursor: pointer;"
                                                data-toggle="{{ $program->id }}"></i>
                                        </div>
                                    </th>
                                    <th width="200"
                                        style="color: #0B4499; font-size: 14px; font-family: 'Montserrat', sans-serif;">
                                        {{ $program->name }}
                                    </th>
                                    <th width="200"
                                        style="color: #0B4499; font-size: 14px; font-family: 'Montserrat', sans-serif;">
                                        {{ $program->sport }}
                                    </th>
                                    <th width="200"
                                        style="color: #0B4499; font-size: 14px; font-family: 'Montserrat', sans-serif;">
                                        {{ $program->start_date }}
                                    </th>
                                    <th width="200"
                                        style="color: #0B4499; font-size: 14px; font-family: 'Montserrat', sans-serif;">
                                        {{ $program->end_date }}
                                    </th>

                                    <th width="200"
                                        style="color: #0B4499; font-size: 14px; font-family: 'Montserrat', sans-serif;">
                                        {{ $program->frequency }}
                                    </th>

                                    <th width="200"
                                        style="color: #0B4499; font-size: 14px; font-family: 'Montserrat', sans-serif;">
                                        {{ $program->payment }}
                                    </th>

                                    <th width="200"
                                        style="color: #0B4499; font-size: 14px; font-family: 'Montserrat', sans-serif;">
                                        {{ $program->registrations->count() }}
                                    </th>
                                    <th width="200">&nbsp;</th>
                                    <th width="200">&nbsp;</th>
                                    <th width="200">
                                        <table>
                                            <tr>
                                                <td class="text-center pe-3">
                                                    <i class="bi bi-filetype-xls export-xls-icon"
                                                        style="color: #0B4499; font-size: 1.5rem;"></i>
                                                </td>
                                                <td class="text-center pe-3">
                                                    <i class="bi bi-printer-fill print-icon"
                                                        style="color: #0B4499; font-size: 1.5rem;"></i>
                                                </td>
                                                <td class="text-center pe-3">
                                                    <a href="" class="action edit" id="edit-admin">
                                                        <img src="{{ asset('images/edit-icon.svg') }}" alt="Edit"
                                                            width="20" height="20" />
                                                    </a>
                                                </td>
                                                <td class="text-center">
                                                    <form id="" action="#" method="POST"
                                                        style="display: inline;">
                                                        @csrf
                                                        @method('DELETE')
                                                        <button type="submit"
                                                            style="border: none; background: none; padding: 0; cursor: pointer;">
                                                            <span class="action delete">
                                                                <img src="{{ asset('images/delete-icon.svg') }}"
                                                                    alt="Delete" width="20" height="20" />
                                                            </span>
                                                        </button>
                                                    </form>
                                                </td>
                                            </tr>
                                        </table>
                                    </th>
                                </tr>
                            </table>

                            <div class="table_collapse_table table-program table-responsive table-grey">
                                <table class="table table-hover team-table d-none" width="100%"
                                    id="program-table{{ $program->id }}">
                                    <tr>
                                        <th width="40">&nbsp;</th>
                                        <th width="200">First Name</th>
                                        <th width="200">Last Name</th>
                                        <th width="250">Gender</th>
                                        <th width="250">Age</th>
                                        <th width="200">Email</th>
                                        <th width="200"></th>
                                        <th width="200">&nbsp;</th>
                                    </tr>
                                    @foreach ($program->registrations as $registration)
                                        <tr>
                                            <td>&nbsp;</td>
                                            <td>{{ $registration->player->firstName }}</td>
                                            <td>{{ $registration->player->lastName }}</td>
                                            <td>{{ $registration->player->gender }}</td>
                                            <td>{{ $registration->player->age }}</td>
                                            <td>{{ $registration->player->email ?? 'N/A' }}</td>
                                            <td>
                                                <table>
                                                    <tr>
                                                        <td class="pe-5">
                                                            <a href="#" class="action edit">
                                                                <img src="{{ asset('images/edit-icon.svg') }}" alt="Edit"
                                                                    width="20" height="20" />
                                                            </a>
                                                        </td>
                                                        <td>
                                                            <form id="removeTeamFromProgram" action="" method="POST"
                                                                style="display: inline;">
                                                                @csrf
                                                                @method('DELETE')
                                                                <button type="submit" class="btn btn-link p-0 border-0"
                                                                    style="background: none;">
                                                                    <img src="{{ asset('images/delete-icon.svg') }}"
                                                                        alt="Delete" width="20" height="20" />
                                                                </button>
                                                            </form>
                                                        </td>
                                                    </tr>
                                                </table>
                                            </td>
                                        </tr>
                                    @endforeach
                                </table>
                            </div>
                        </div>
                    @endforeach
                @endisset




                @isset($singleProgramData)
                    <div class="tables_heading table-program table-responsive">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th width="40" class="py-4">&nbsp;</th>
                                    <th width="200" class="py-4">Program Name</th>
                                    <th width="200" class="py-4">Sport</th>
                                    <th width="200" class="py-4">Start Date</th>
                                    <th width="200" class="py-4">End Date</th>
                                    <th width="200" class="py-4">Frequency</th>
                                    <th width="200" class="py-4">Payment</th>
                                    <th width="200" class="py-4">Players Enrolled</th>


                                    <th width="200">&nbsp;</th>
                                    <th width="200">&nbsp;</th>
                                    <th width="200">&nbsp;</th>
                                </tr>
                            </thead>
                        </table>
                    </div>
                    <div class="tables_collapse_heading table-heading-grey">
                        <table class="table table-hover">
                            <tr>
                                <th width="40">
                                    <div class="icon"><i class="bi bi-caret-up-fill toggle-arrow"
                                            style="color:#0B4499; cursor: pointer;" data-toggle="{{ $program->id }}"></i>
                                    </div>
                                </th>
                                <th width="200"
                                    style="color: #0B4499; font-size: 14px; font-family: 'Montserrat', sans-serif;">
                                    {{ $program->name }}
                                </th>
                                <th width="200"
                                    style="color: #0B4499; font-size: 14px; font-family: 'Montserrat', sans-serif;">
                                    {{ $program->sport }}
                                </th>
                                <th width="200"
                                    style="color: #0B4499; font-size: 14px; font-family: 'Montserrat', sans-serif;">
                                    {{ $program->start_date }}
                                </th>
                                <th width="200"
                                    style="color: #0B4499; font-size: 14px; font-family: 'Montserrat', sans-serif;">
                                    {{ $program->end_date }}
                                </th>

                                <th width="200"
                                    style="color: #0B4499; font-size: 14px; font-family: 'Montserrat', sans-serif;">
                                    {{ $program->frequency }}
                                </th>

                                <th width="200"
                                    style="color: #0B4499; font-size: 14px; font-family: 'Montserrat', sans-serif;">
                                    {{ $program->payment }}
                                </th>

                                <th width="200"
                                    style="color: #0B4499; font-size: 14px; font-family: 'Montserrat', sans-serif;">
                                    {{ $registrationCount }}
                                </th>
                                <th width="200">&nbsp;</th>
                                <th width="200">&nbsp;</th>
                                <th width="200">
                                    <table>
                                        <tr>
                                            <td class="text-center pe-3">
                                                <i class="bi bi-filetype-xls export-xls-icon"
                                                    style="color: #0B4499; font-size: 1.5rem;"></i>
                                            </td>
                                            <td class="text-center pe-3">
                                                <i class="bi bi-printer-fill print-icon"
                                                    style="color: #0B4499; font-size: 1.5rem;"></i>
                                            </td>
                                            <td class="text-center pe-3">
                                                <a href="" class="action edit" id="edit-admin">
                                                    <img src="{{ asset('images/edit-icon.svg') }}" alt="Edit"
                                                        width="20" height="20" />
                                                </a>
                                            </td>
                                            <td class="text-center">
                                                <form id="" action="#" method="POST" style="display: inline;">
                                                    @csrf
                                                    @method('DELETE')
                                                    <button type="submit"
                                                        style="border: none; background: none; padding: 0; cursor: pointer;">
                                                        <span class="action delete">
                                                            <img src="{{ asset('images/delete-icon.svg') }}" alt="Delete"
                                                                width="20" height="20" />
                                                        </span>
                                                    </button>
                                                </form>
                                            </td>
                                        </tr>
                                    </table>
                                </th>
                            </tr>
                        </table>

                        <div class="table_collapse_table table-program table-responsive table-grey">
                            <table class="table table-hover team-table d-none" width="100%"
                                id="program-table{{ $program->id }}">
                                <tr>
                                    <th width="40">&nbsp;</th>
                                    <th width="200">First Name</th>
                                    <th width="200">Last Name</th>
                                    <th width="250">Gender</th>
                                    <th width="250">Age</th>
                                    <th width="200">Email</th>
                                    <th width="200"></th>
                                    <th width="200">&nbsp;</th>
                                </tr>
                                @foreach ($singleProgramData as $registration)
                                    <tr>
                                        <td>&nbsp;</td>
                                        <td>{{ $registration->player->firstName }}</td>
                                        <td>{{ $registration->player->lastName }}</td>
                                        <td>{{ $registration->player->gender }}</td>
                                        <td>{{ $registration->player->age }}</td>
                                        <td>{{ $registration->player->email ?? 'N/A' }}</td>
                                        <td>
                                            <table>
                                                <tr>
                                                    <td class="pe-5">
                                                        <a href="#" class="action edit">
                                                            <img src="{{ asset('images/edit-icon.svg') }}" alt="Edit"
                                                                width="20" height="20" />
                                                        </a>
                                                    </td>
                                                    <td>
                                                        <form id="removeTeamFromProgram" action="" method="POST"
                                                            style="display: inline;">
                                                            @csrf
                                                            @method('DELETE')
                                                            <button type="submit" class="btn btn-link p-0 border-0"
                                                                style="background: none;">
                                                                <img src="{{ asset('images/delete-icon.svg') }}"
                                                                    alt="Delete" width="20" height="20" />
                                                            </button>
                                                        </form>
                                                    </td>
                                                </tr>
                                            </table>
                                        </td>
                                    </tr>
                                @endforeach
                            </table>
                        </div>
                    </div>
                @endisset



                @isset($programsByGender)
                    <div class="tables_heading table-program table-responsive">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th width="40" class="py-4">&nbsp;</th>
                                    <th width="200" class="py-4">Program Name</th>
                                    <th width="200" class="py-4">Sport</th>
                                    <th width="200" class="py-4">Start Date</th>
                                    <th width="200" class="py-4">End Date</th>
                                    <th width="200" class="py-4">Frequency</th>
                                    <th width="200" class="py-4">Payment</th>
                                    <th width="200" class="py-4">Players Enrolled</th>


                                    <th width="200">&nbsp;</th>
                                    <th width="200">&nbsp;</th>
                                    <th width="200">&nbsp;</th>
                                </tr>
                            </thead>
                        </table>
                    </div>
                    @foreach ($programsByGender as $program)
                        <div class="tables_collapse_heading table-heading-grey">
                            <table class="table table-hover">
                                <tr>
                                    <th width="40">
                                        <div class="icon"><i class="bi bi-caret-up-fill toggle-arrow"
                                                style="color:#0B4499; cursor: pointer;"
                                                data-toggle="{{ $program->id }}"></i>
                                        </div>
                                    </th>
                                    <th width="200"
                                        style="color: #0B4499; font-size: 14px; font-family: 'Montserrat', sans-serif;">
                                        {{ $program->name }}
                                    </th>
                                    <th width="200"
                                        style="color: #0B4499; font-size: 14px; font-family: 'Montserrat', sans-serif;">
                                        {{ $program->sport }}
                                    </th>
                                    <th width="200"
                                        style="color: #0B4499; font-size: 14px; font-family: 'Montserrat', sans-serif;">
                                        {{ $program->start_date }}
                                    </th>
                                    <th width="200"
                                        style="color: #0B4499; font-size: 14px; font-family: 'Montserrat', sans-serif;">
                                        {{ $program->end_date }}
                                    </th>

                                    <th width="200"
                                        style="color: #0B4499; font-size: 14px; font-family: 'Montserrat', sans-serif;">
                                        {{ $program->frequency }}
                                    </th>

                                    <th width="200"
                                        style="color: #0B4499; font-size: 14px; font-family: 'Montserrat', sans-serif;">
                                        {{ $program->payment }}
                                    </th>

                                    <th width="200"
                                        style="color: #0B4499; font-size: 14px; font-family: 'Montserrat', sans-serif;">
                                        {{ $program->registrations->count() }}
                                    </th>
                                    <th width="200">&nbsp;</th>
                                    <th width="200">&nbsp;</th>
                                    <th width="200">
                                        <table>
                                            <tr>
                                                <td class="text-center pe-3">
                                                    <i class="bi bi-filetype-xls export-xls-icon"
                                                        style="color: #0B4499; font-size: 1.5rem;"></i>
                                                </td>
                                                <td class="text-center pe-3">
                                                    <i class="bi bi-printer-fill print-icon"
                                                        style="color: #0B4499; font-size: 1.5rem;"></i>
                                                </td>
                                                <td class="text-center pe-3">
                                                    <a href="" class="action edit" id="edit-admin">
                                                        <img src="{{ asset('images/edit-icon.svg') }}" alt="Edit"
                                                            width="20" height="20" />
                                                    </a>
                                                </td>
                                                <td class="text-center">
                                                    <form id="" action="#" method="POST"
                                                        style="display: inline;">
                                                        @csrf
                                                        @method('DELETE')
                                                        <button type="submit"
                                                            style="border: none; background: none; padding: 0; cursor: pointer;">
                                                            <span class="action delete">
                                                                <img src="{{ asset('images/delete-icon.svg') }}"
                                                                    alt="Delete" width="20" height="20" />
                                                            </span>
                                                        </button>
                                                    </form>
                                                </td>
                                            </tr>
                                        </table>
                                    </th>
                                </tr>
                            </table>

                            <div class="table_collapse_table table-program table-responsive table-grey">
                                <table class="table table-hover team-table d-none" width="100%"
                                    id="program-table{{ $program->id }}">
                                    <tr>
                                        <th width="40">&nbsp;</th>
                                        <th width="200">First Name</th>
                                        <th width="200">Last Name</th>
                                        <th width="250">Gender</th>
                                        <th width="250">Age</th>
                                        <th width="200">Email</th>
                                        <th width="200"></th>
                                        <th width="200">&nbsp;</th>
                                    </tr>
                                    @foreach ($program->registrations as $registration)
                                        <tr>
                                            <td>&nbsp;</td>
                                            <td>{{ $registration->player->firstName }}</td>
                                            <td>{{ $registration->player->lastName }}</td>
                                            <td>{{ $registration->player->gender }}</td>
                                            <td>{{ $registration->player->age }}</td>
                                            <td>{{ $registration->player->email ?? 'N/A' }}</td>
                                            <td>
                                                <table>
                                                    <tr>
                                                        <td class="pe-5">
                                                            <a href="#" class="action edit">
                                                                <img src="{{ asset('images/edit-icon.svg') }}" alt="Edit"
                                                                    width="20" height="20" />
                                                            </a>
                                                        </td>
                                                        <td>
                                                            <form id="removeTeamFromProgram" action="" method="POST"
                                                                style="display: inline;">
                                                                @csrf
                                                                @method('DELETE')
                                                                <button type="submit" class="btn btn-link p-0 border-0"
                                                                    style="background: none;">
                                                                    <img src="{{ asset('images/delete-icon.svg') }}"
                                                                        alt="Delete" width="20" height="20" />
                                                                </button>
                                                            </form>
                                                        </td>
                                                    </tr>
                                                </table>
                                            </td>
                                        </tr>
                                    @endforeach
                                </table>
                            </div>
                        </div>
                    @endforeach
                @endisset



                @isset($programsByAge)
                    <div class="tables_heading table-program table-responsive">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th width="40" class="py-4">&nbsp;</th>
                                    <th width="200" class="py-4">Program Name</th>
                                    <th width="200" class="py-4">Sport</th>
                                    <th width="200" class="py-4">Start Date</th>
                                    <th width="200" class="py-4">End Date</th>
                                    <th width="200" class="py-4">Frequency</th>
                                    <th width="200" class="py-4">Payment</th>
                                    <th width="200" class="py-4">Players Enrolled</th>


                                    <th width="200">&nbsp;</th>
                                    <th width="200">&nbsp;</th>
                                    <th width="200">&nbsp;</th>
                                </tr>
                            </thead>
                        </table>
                    </div>
                    @foreach ($programsByAge as $program)
                        <div class="tables_collapse_heading table-heading-grey">
                            <table class="table table-hover">
                                <tr>
                                    <th width="40">
                                        <div class="icon"><i class="bi bi-caret-up-fill toggle-arrow"
                                                style="color:#0B4499; cursor: pointer;"
                                                data-toggle="{{ $program->id }}"></i>
                                        </div>
                                    </th>
                                    <th width="200"
                                        style="color: #0B4499; font-size: 14px; font-family: 'Montserrat', sans-serif;">
                                        {{ $program->name }}
                                    </th>
                                    <th width="200"
                                        style="color: #0B4499; font-size: 14px; font-family: 'Montserrat', sans-serif;">
                                        {{ $program->sport }}
                                    </th>
                                    <th width="200"
                                        style="color: #0B4499; font-size: 14px; font-family: 'Montserrat', sans-serif;">
                                        {{ $program->start_date }}
                                    </th>
                                    <th width="200"
                                        style="color: #0B4499; font-size: 14px; font-family: 'Montserrat', sans-serif;">
                                        {{ $program->end_date }}
                                    </th>

                                    <th width="200"
                                        style="color: #0B4499; font-size: 14px; font-family: 'Montserrat', sans-serif;">
                                        {{ $program->frequency }}
                                    </th>

                                    <th width="200"
                                        style="color: #0B4499; font-size: 14px; font-family: 'Montserrat', sans-serif;">
                                        {{ $program->payment }}
                                    </th>

                                    <th width="200"
                                        style="color: #0B4499; font-size: 14px; font-family: 'Montserrat', sans-serif;">
                                        {{ $program->registrations->count() }}
                                    </th>
                                    <th width="200">&nbsp;</th>
                                    <th width="200">&nbsp;</th>
                                    <th width="200">
                                        <table>
                                            <tr>
                                                <td class="text-center pe-3">
                                                    <i class="bi bi-filetype-xls export-xls-icon"
                                                        style="color: #0B4499; font-size: 1.5rem;"></i>
                                                </td>
                                                <td class="text-center pe-3">
                                                    <i class="bi bi-printer-fill print-icon"
                                                        style="color: #0B4499; font-size: 1.5rem;"></i>
                                                </td>
                                                <td class="text-center pe-3">
                                                    <a href="" class="action edit" id="edit-admin">
                                                        <img src="{{ asset('images/edit-icon.svg') }}" alt="Edit"
                                                            width="20" height="20" />
                                                    </a>
                                                </td>
                                                <td class="text-center">
                                                    <form id="" action="#" method="POST"
                                                        style="display: inline;">
                                                        @csrf
                                                        @method('DELETE')
                                                        <button type="submit"
                                                            style="border: none; background: none; padding: 0; cursor: pointer;">
                                                            <span class="action delete">
                                                                <img src="{{ asset('images/delete-icon.svg') }}"
                                                                    alt="Delete" width="20" height="20" />
                                                            </span>
                                                        </button>
                                                    </form>
                                                </td>
                                            </tr>
                                        </table>
                                    </th>
                                </tr>
                            </table>

                            <div class="table_collapse_table table-program table-responsive table-grey">
                                <table class="table table-hover team-table d-none" width="100%"
                                    id="program-table{{ $program->id }}">
                                    <tr>
                                        <th width="40">&nbsp;</th>
                                        <th width="200">First Name</th>
                                        <th width="200">Last Name</th>
                                        <th width="250">Gender</th>
                                        <th width="250">Age</th>
                                        <th width="200">Email</th>
                                        <th width="200"></th>
                                        <th width="200">&nbsp;</th>
                                    </tr>
                                    @foreach ($program->registrations as $registration)
                                        <tr>
                                            <td>&nbsp;</td>
                                            <td>{{ $registration->player->firstName }}</td>
                                            <td>{{ $registration->player->lastName }}</td>
                                            <td>{{ $registration->player->gender }}</td>
                                            <td>{{ $registration->player->age }}</td>
                                            <td>{{ $registration->player->email ?? 'N/A' }}</td>
                                            <td>
                                                <table>
                                                    <tr>
                                                        <td class="pe-5">
                                                            <a href="#" class="action edit">
                                                                <img src="{{ asset('images/edit-icon.svg') }}" alt="Edit"
                                                                    width="20" height="20" />
                                                            </a>
                                                        </td>
                                                        <td>
                                                            <form id="removeTeamFromProgram" action="" method="POST"
                                                                style="display: inline;">
                                                                @csrf
                                                                @method('DELETE')
                                                                <button type="submit" class="btn btn-link p-0 border-0"
                                                                    style="background: none;">
                                                                    <img src="{{ asset('images/delete-icon.svg') }}"
                                                                        alt="Delete" width="20" height="20" />
                                                                </button>
                                                            </form>
                                                        </td>
                                                    </tr>
                                                </table>
                                            </td>
                                        </tr>
                                    @endforeach
                                </table>
                            </div>
                        </div>
                    @endforeach
                @endisset



                @isset($playersByTown)
                    <div class="tables_heading table-program table-responsive">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th width="40" class="py-4">&nbsp;</th>
                                    <th width="200" class="py-4">FirstName</th>
                                    <th width="200" class="py-4">LastName</th>
                                    <th width="200" class="py-4">Gender</th>
                                    <th width="200" class="py-4">Age</th>
                                    <th width="200" class="py-4">Email</th>
                                    <th width="200">&nbsp;</th>
                                    <th width="200">&nbsp;</th>
                                    <th width="200">&nbsp;</th>
                                </tr>
                            </thead>
                        </table>
                    </div>

                    @foreach ($playersByTown as $player)
                        <div class="tables_collapse_heading table-heading-grey">
                            <table class="table table-hover">
                                <tr>
                                    <th width="40">
                                        <div class="icon">
                                            <i class="bi bi-caret-up-fill toggle-arrow"
                                                style="color:#0B4499; cursor: pointer;"
                                                data-toggle="{{ $player->id }}"></i>
                                        </div>
                                    </th>
                                    <th width="200"
                                        style="color: #0B4499; font-size: 14px; font-family: 'Montserrat', sans-serif;">
                                        {{ $player->firstName }}
                                    </th>
                                    <th width="200"
                                        style="color: #0B4499; font-size: 14px; font-family: 'Montserrat', sans-serif;">
                                        {{ $player->lastName }}
                                    </th>
                                    <th width="200"
                                        style="color: #0B4499; font-size: 14px; font-family: 'Montserrat', sans-serif;">
                                        {{ $player->gender }}
                                    </th>
                                    <th width="200"
                                        style="color: #0B4499; font-size: 14px; font-family: 'Montserrat', sans-serif;">
                                        {{ $player->age }}
                                    </th>
                                    <th width="200"
                                        style="color: #0B4499; font-size: 14px; font-family: 'Montserrat', sans-serif;">
                                        {{ $player->email ?? 'N/A' }}
                                    </th>
                                    <th width="200">&nbsp;</th>
                                    <th width="200">&nbsp;</th>
                                    <th width="200">
                                        <table>
                                            <tr>
                                                <td class="text-center pe-3">
                                                    <i class="bi bi-filetype-xls export-xls-icon"
                                                        style="color: #0B4499; font-size: 1.5rem;"></i>
                                                </td>
                                                <td class="text-center pe-3">
                                                    <i class="bi bi-printer-fill print-icon"
                                                        style="color: #0B4499; font-size: 1.5rem;"></i>
                                                </td>
                                                <td class="text-center pe-3">
                                                    <a href="" class="action edit" id="edit-admin">
                                                        <img src="{{ asset('images/edit-icon.svg') }}" alt="Edit"
                                                            width="20" height="20" />
                                                    </a>
                                                </td>
                                                <td class="text-center">
                                                    <form id="" action="#" method="POST"
                                                        style="display: inline;">
                                                        @csrf
                                                        @method('DELETE')
                                                        <button type="submit"
                                                            style="border: none; background: none; padding: 0; cursor: pointer;">
                                                            <span class="action delete">
                                                                <img src="{{ asset('images/delete-icon.svg') }}"
                                                                    alt="Delete" width="20" height="20" />
                                                            </span>
                                                        </button>
                                                    </form>
                                                </td>
                                            </tr>
                                        </table>
                                    </th>
                                </tr>
                            </table>
                        </div>

                        <div class="table_collapse_table table-program table-responsive table-grey">
                            <table class="table table-hover team-table d-none" width="100%"
                                id="program-table{{ $player->id }}">
                                <tr>
                                    <th width="40">&nbsp;</th>
                                    <th width="200">Program</th>
                                    <th width="200">Start Date</th>
                                    <th width="250">End Date</th>
                                    <th width="250"></th>
                                    <th width="200"></th>
                                    <th width="200"></th>
                                    <th width="200">&nbsp;</th>
                                </tr>

                                @foreach ($player->programs as $program)
                                    <tr>
                                        <td>&nbsp;</td>
                                        <td>{{ $program->name }}</td>
                                        <td>{{ $program->start_date }}</td>
                                        <td>{{ $program->end_date }}</td>
                                        <td></td>
                                        <td class="pl-1"></td>
                                        <td></td>
                                        <td>
                                            <table>
                                                <tr>
                                                    <td class="pe-5">
                                                        <a href="#" class="action edit">
                                                            <img src="{{ asset('images/edit-icon.svg') }}" alt="Edit"
                                                                width="20" height="20" />
                                                        </a>
                                                    </td>
                                                    <td>
                                                        <form id="removeTeamFromProgram" action="" method="POST"
                                                            style="display: inline;">
                                                            @csrf
                                                            @method('DELETE')
                                                            <button type="submit" class="btn btn-link p-0 border-0"
                                                                style="background: none;">
                                                                <img src="{{ asset('images/delete-icon.svg') }}"
                                                                    alt="Delete" width="20" height="20" />
                                                            </button>
                                                        </form>
                                                    </td>
                                                </tr>
                                            </table>
                                        </td>
                                    </tr>
                                @endforeach
                            </table>
                        </div>
                    @endforeach
                @endisset
            </div>
    </section>
@endsection
<script>
    document.addEventListener("click", function(event) {
        if (event.target.classList.contains("toggle-arrow")) {
            const arrow = event.target;
            const userId = arrow.getAttribute("data-toggle");

            const programTable = document.getElementById(`program-table${userId}`);

            if (programTable) {
                programTable.classList.toggle("d-none");

                if (programTable.classList.contains("d-none")) {
                    arrow.classList.remove("bi-caret-down-fill");
                    arrow.classList.add("bi-caret-up-fill");
                } else {
                    arrow.classList.remove("bi-caret-up-fill");
                    arrow.classList.add("bi-caret-down-fill");
                }
            } else {
                console.warn(`No table found for user ID: ${userId}`);
            }
        }
    });
</script>
