.header {
    position: relative;
    z-index: 1000;
}
.header .container {
    max-width: 1600px;
    width: 100%;
}
.header .brand {
    margin-block: -10px -15px;
}
.header .header_meta {
    background: #f1f1f1;
    padding-block: 0.5rem;
}
.header .header_meta h2 {
    color: #0b4499;
    font: 900 14px "Montserrat", sans-serif;
    letter-spacing: 2px;
}
.header .header_meta .social li {
    line-height: 1;
    width: 20px;
}
.header .header_meta .social li a {
    background: none;
    color: #0b4499;
    font-size: 20px;
    height: auto;
    text-decoration: none;
    width: auto;
}
.header .header_meta .social li.login {
    width: auto;
}
.header .header_meta .social li.login a {
    font: 900 14px "Montserrat", sans-serif;
    letter-spacing: 2px;
}
.header .header_meta .social li + li {
    margin-left: 1rem;
}
.header .header_meta .logout {
    color: #0b4499;
    font: 900 14px "Montserrat", sans-serif;
    letter-spacing: 2px;
}
.header .site_nav li {
    font: 900 18px/1 "Poppins", sans-serif;
}
@media (min-width: 992px) {
    .header .site_nav li {
        font-size: 13px;
    }
}
@media (min-width: 1024px) {
    .header .site_nav li {
        font-size: 14px;
    }
}
@media (min-width: 1200px) {
    .header .site_nav li {
        font-size: 16px;
    }
}
@media (min-width: 1400px) {
    .header .site_nav li {
        font-size: 18px;
        margin: 5px;
    }
}
.header .site_nav li a {
    display: block;
}
@media (max-width: 991px) {
    .header .site_nav li a {
        padding: 15px;
    }
}
@media (min-width: 992px) {
    .header .site_nav li a {
        padding: 5px;
    }
}
@media (min-width: 1400px) {
    .header .site_nav li a {
        padding: 10px;
    }
}
.header .site_nav li a:hover {
    color: #c1b05c;
}
.header .site_nav li.has_sub {
    position: relative;
}
@media (min-width: 992px) {
    .header .site_nav li.has_sub {
        padding-right: 20px;
    }
    .header .site_nav li.has_sub a {
        padding-right: 0;
    }
}
.header .site_nav li.has_sub .more {
    position: absolute;
    color: #0b4499;
    line-height: 1;
    cursor: pointer;
}
@media (max-width: 991px) {
    .header .site_nav li.has_sub .more {
        top: 5px;
        right: 15px;
        font-size: 32px;
    }
}
@media (min-width: 992px) {
    .header .site_nav li.has_sub .more {
        top: 50%;
        -webkit-transform: translateY(-50%);
        transform: translateY(-50%);
        right: 0;
        font-size: 18px;
    }
}
.header .site_nav li.has_sub .sub_nav {
    background: #f1f1f1;
    display: none;
}
@media (min-width: 992px) {
    .header .site_nav li.has_sub .sub_nav {
        left: 0;
        min-width: 200px;
        position: absolute;
        top: 100%;
    }
}
.header .site_nav li.has_sub .sub_nav li {
    font-size: 15px;
    margin: 0;
}
.header .site_nav li.has_sub .sub_nav li a {
    padding: 15px;
}
.header .site_nav li.has_sub .sub_nav li a.active {
    color: #c1b05c;
}
@media (min-width: 992px) {
    .header .site_nav li.has_sub .sub_nav li a:hover {
        background: #fff;
        color: #c1b05c;
    }
}
.header .site_nav li.has_sub .sub_nav li + li {
    border-top: 1px solid #c1b05c;
}
@media (min-width: 992px) {
    .header .site_nav li.has_sub:hover > a {
        color: #c1b05c;
    }
    .header .site_nav li.has_sub:hover > a + .more {
        color: #c1b05c;
    }
    .header .site_nav li.has_sub:hover .sub_nav {
        display: block;
    }
}
.header .site_nav li.active > a {
    color: #c1b05c;
}
.header .site_nav li.active > a + .more {
    color: #c1b05c;
}
@media (min-width: 992px) {
    .header .site_nav li:last-child {
        margin-right: 0;
    }
    .header .site_nav li:last-child a {
        padding-right: 0;
    }
    .header .site_nav li:last-child .sub_nav {
        right: 0;
        left: auto;
    }
}
.header .site_nav a {
    color: #0b4499;
    text-decoration: none;
}
.header .call_nav {
    background: #fff;
    border-radius: 0.8rem;
    height: 60px;
    position: absolute;
    right: 15px;
    top: 65%;
    -webkit-transform: translateY(-50%);
    transform: translateY(-50%);
    -webkit-transform-origin: center right;
    transform-origin: center right;
    width: 60px;
}
.header .call_nav .line {
    background-color: #062e69;
    border-radius: 50px;
    display: block;
    height: 5px;
    -webkit-transition: all 0.3s ease-in-out;
    transition: all 0.3s ease-in-out;
    width: 35px;
}
.header .call_nav .line + .line {
    margin-top: 6px;
}
.header .call_nav.is-active .line:nth-child(1) {
    -webkit-transform: translateY(8px) rotate(45deg);
    transform: translateY(8px) rotate(45deg);
}
.header .call_nav.is-active .line:nth-child(2) {
    opacity: 0;
}
.header .call_nav.is-active .line:nth-child(3) {
    margin-top: 0;
    -webkit-transform: translateY(-8px) rotate(-45deg);
    transform: translateY(-8px) rotate(-45deg);
}
.header .call_nav:hover {
    cursor: pointer;
}
.hero {
    isolation: isolate;
    overflow: hidden;
    position: relative;
}
.hero .img-slider,
.hero .video-hero {
    height: 100%;
    left: 0;
    position: absolute;
    top: 0;
    width: 100%;
}
.hero .img-slider .carousel,
.hero .img-slider .carousel-inner,
.hero .img-slider .carousel-item,
.hero .video-hero .carousel,
.hero .video-hero .carousel-inner,
.hero .video-hero .carousel-item {
    height: 100%;
}
.hero .img-slider .carousel-item img,
.hero .video-hero .carousel-item img {
    height: 100%;
    -o-object-fit: cover;
    object-fit: cover;
    -o-object-position: center;
    object-position: center;
    width: 100%;
}
.hero .img-slider video,
.hero .video-hero video {
    -o-object-fit: cover;
    object-fit: cover;
}
.hero .text-over {
    background-color: rgba(0, 0, 0, 0.53);
    min-height: 80vh;
    padding: 100px 40px 150px;
    position: relative;
    z-index: 1;
}
.hero .text-over h1 {
    font: 900 52px/1 "Poppins", sans-serif;
    letter-spacing: 2px;
}
@media (min-width: 992px) {
    .hero .text-over h1 {
        font-size: 92px;
    }
}
.hero .text-over h2 {
    letter-spacing: 3px;
}
.hero .text-over p {
    font: 24px "Didact Gothic", sans-serif;
}
.hero + .courses-select {
    margin-top: -100px;
}
.hero_inner {
    height: 235px;
    isolation: isolate;
    position: relative;
}
.hero_inner .page_img {
    background-position: center center;
    background-size: cover;
    height: 100%;
    left: 0;
    position: absolute;
    top: 0;
    width: 100%;
}
.hero_inner .page_img:after {
    background: -webkit-gradient(
        linear,
        left bottom,
        left top,
        from(#062e69),
        to(rgba(11, 68, 153, 0))
    );
    background: linear-gradient(0deg, #062e69 0%, rgba(11, 68, 153, 0) 100%);
    content: "";
    height: 100%;
    position: absolute;
    width: 100%;
}
.hero_inner .page_title {
    position: relative;
    z-index: 1;
}
.hero_inner .page_title h1 {
    -webkit-text-stroke-color: #c1b05c;
    -webkit-text-stroke-width: 3px;
    -webkit-text-stroke-width: 1.5px;
}
.hero_inner .page_title:before,
.hero_inner .page_title:after {
    content: "";
    height: 0.5rem;
    left: 0;
    position: absolute;
    width: 100%;
}
.hero_inner .page_title:before {
    background: -webkit-gradient(
        linear,
        left top,
        right top,
        from(#c1b05c),
        to(rgba(193, 176, 92, 0))
    );
    background: linear-gradient(
        to right,
        #c1b05c 0%,
        rgba(193, 176, 92, 0) 100%
    );
    bottom: 0;
}
.hero_inner .page_title:after {
    background: -webkit-gradient(
        linear,
        right top,
        left top,
        from(#c1b05c),
        to(rgba(193, 176, 92, 0))
    );
    background: linear-gradient(
        to left,
        #c1b05c 0%,
        rgba(193, 176, 92, 0) 100%
    );
    top: 0;
}
.page_title h1 {
    color: #062e69;
    font: 900 calc(16px + 4.5vw) / 1 "Poppins", sans-serif;
}
.hero-link li {
    color: #c1b05c;
    font: 900 16px/1 "Montserrat", sans-serif;
    letter-spacing: 2px;
    margin-right: 10px;
}
.hero-link li a {
    color: inherit;
    text-decoration: none;
}
.hero-link li + li {
    list-style: disc;
    margin-left: 15px;
}
.hero-link li:last-child {
    margin-right: 0;
}
.hero-bar {
    background: #062e69;
    height: 2px;
    width: 80px;
}
.courses-select .course_box {
    position: relative;
}
.courses-select .course_box .label {
    isolation: isolate;
    left: 0;
    position: absolute;
    top: 50%;
}
.courses-select .course_box .label .bg-img {
    left: 0;
    position: absolute;
    top: 0;
    z-index: -1;
}
.courses-select .course_box .label .text h2 {
    font: 700 24px "Caveat", cursive;
}
.courses-select .course_box .label .text p {
    font: 900 14px "Montserrat", sans-serif;
}
.courses-select .course_box .img {
    border-radius: 10px;
    height: 100%;
    left: 0;
    overflow: hidden;
    position: absolute;
    top: 0;
    width: 100%;
}
.courses-select .course_box .img img {
    height: 100%;
    -o-object-fit: cover;
    object-fit: cover;
    -o-object-position: center;
    object-position: center;
}
.courses-select .course_box:before {
    content: "";
    display: block;
    padding-top: 130%;
}
.table-program th {
    color: #194795;
    font: 700 14px/1 "Montserrat", sans-serif;
    padding-block: 0.8rem;
}
.table-program td {
    font: 14px/1 "Montserrat", sans-serif;
}
.table-program td a {
    color: #0b4499;
    text-decoration: none;
}
.table-program .cta {
    color: #fff;
    font-size: 12px;
    font-weight: 400;
}
.table-program .cta:hover {
    color: #fff;
}
.table-program .action {
    cursor: pointer;
}
.program-select .program_box {
    background: #0b4499;
    border-radius: 10px;
    overflow: hidden;
    padding: 10px;
}
.program-select .program_box .label {
    padding-block: 20px 10px;
}
.program-select .program_box .img {
    border-radius: 5px;
    overflow: hidden;
}
@media (min-width: 768px) {
    .program-select .program_box .img {
        position: relative;
    }
    .program-select .program_box .img:before {
        content: "";
        display: block;
        padding-top: 70%;
    }
    .program-select .program_box .img img {
        height: 100%;
        left: 0;
        -o-object-fit: cover;
        object-fit: cover;
        position: absolute;
        top: 0;
        width: 100%;
        z-index: 2;
    }
}
.program-select .program_box h2 {
    font: 900 21px/1 "Montserrat", sans-serif;
}
.program-select .program_box .copy p {
    color: #fff;
}
.program-select .program_box.bg-dark-green {
    background: #006a3a;
}
.program-select .program_box.bg-dark-blue {
    background: #062e69;
}
.program-select .program_box.bg-gold {
    background: #c1b05c;
}
.program-select .program_box.bg-yellow {
    background: #ddcf72;
}
.program-meta .heading h2 {
    color: #c1b05c;
    font-size: 16px;
    letter-spacing: 2px;
}
.program-meta .program-box {
    border-bottom: 2px solid #062e69;
    border-top: 2px solid #062e69;
}
.program-meta .program-box .box h2,
.program-meta .program-box .box p {
    font-size: 16px;
    letter-spacing: 2px;
}
.program-meta .program-box .box h2 {
    color: #c1b05c;
}
.program-meta .program-box .box p {
    color: #062e69;
}
.program-meta .program-box .cta {
    font: 700 14px/1 "Montserrat", sans-serif;
}
.program-welcome .heading h2 {
    color: #c1b05c;
}
.program-welcome .heading h3 {
    color: #c1b05c;
    font-weight: 900;
}
.program-welcome .cta {
    font: 700 14px "Montserrat", sans-serif;
    min-width: 300px;
}
.player-meta__box {
    border: 1px solid #999;
    margin-top: 10px;
    min-height: 140px;
    position: relative;
}
.player-meta__box p {
    font: 16px/1.65 "Montserrat", sans-serif;
    letter-spacing: 2px;
}
.player-meta__box .cta {
    font: 700 14px "Montserrat", sans-serif;
    min-width: 300px;
}
.player-meta__box .edit {
    cursor: pointer;
    position: absolute;
    right: 15px;
    top: 15px;
}
.table-filter .filter-option .form-label {
    text-align: center;
    width: 100%;
}
.table-filter .filter-option .form-control {
    min-width: 220px;
}
.program-table .heading h2 {
    color: #c1b05c;
}
.program-table .heading .cta {
    font: 700 14px "Montserrat", sans-serif;
    min-width: 300px;
}
.program-table .heading:has(.cta-table) {
    position: relative;
}
.program-table .heading:has(.cta-table) .cta {
    position: absolute;
    right: 0;
    top: 50%;
    -webkit-transform: translateY(-50%);
    transform: translateY(-50%);
}
.program-table .cta-row .cta {
    font: 700 14px "Montserrat", sans-serif;
    min-width: 300px;
}
.invitations-list {
    border-top: 1px solid #d9d9d9;
}
.invitations-list--list {
    border-bottom: 1px solid #d9d9d9;
}
.invitations-list--list p {
    color: #0b4499;
    font: 14px "Montserrat", sans-serif;
    margin-bottom: 0;
}
.invitations-list--list a {
    color: inherit;
    text-decoration: none;
}
.invitations-list--list .cta {
    color: #fff;
    font-size: 12px;
    font-weight: 400;
}
.coach-tab__head .tab {
    background: rgba(6, 46, 105, 0.4);
    border-radius: 15px 15px 0 0;
    cursor: pointer;
    font: 700 14px "Montserrat", sans-serif;
}
.coach-tab__head .tab.active {
    background: #062e69;
}
.coach-tab .program-table {
    display: none;
}
.coach-tab .program-table select {
    background: #0b4499;
    border-radius: 20px;
    color: #fff;
    height: 40px;
    padding-inline: 0.5rem;
    width: 150px;
}
.news-bar {
    background: #f1f1f1;
}
.news-bar .heading {
    border-bottom: 2px solid #c1b05c;
}
.news-bar .slider-news .item {
    background: #fff;
}
.news-bar .slider-news .item .img {
    position: relative;
}
.news-bar .slider-news .item .img:before {
    content: "";
    display: block;
    padding-top: 70%;
}
.news-bar .slider-news .item .img img {
    height: 100%;
    left: 0;
    -o-object-fit: cover;
    object-fit: cover;
    position: absolute;
    top: 0;
    width: 100%;
}
.news-bar .slider-news .item .label {
    padding: 15px;
}
.news-bar .slider-news .item .label h2 {
    color: #0b4499;
    font: 900 22px/1 "Montserrat", sans-serif;
}
.news-bar .slider-news .item .label p {
    color: #828282;
    font: 700 16px "Poppins", sans-serif;
}
.news-bar .slider-news .owl-nav button {
    background: #0b4499;
    color: #fff;
    font-size: 14px;
    height: 25px;
    line-height: 1;
    position: absolute;
    top: 50%;
    -webkit-transform: translateY(-50%);
    transform: translateY(-50%);
    width: 25px;
}
.news-bar .slider-news .owl-nav button.owl-next {
    left: 100%;
}
.news-bar .slider-news .owl-nav button.owl-prev {
    right: 100%;
}
.news-bar .slider-news .owl-nav button.disabled {
    display: none;
}
.explore-more {
    position: relative;
}
.explore-more__box {
    background-color: #000;
    isolation: isolate;
    overflow: hidden;
    position: relative;
}
.explore-more__box .img {
    height: 120%;
    left: 0;
    opacity: 1;
    position: absolute;
    top: 0;
    -webkit-transition: opacity 0.35s;
    transition: opacity 0.35s;
    width: 120%;
    z-index: 1;
}
.explore-more__box .img img {
    height: 100%;
    -o-object-fit: cover;
    object-fit: cover;
    -webkit-transform: translateX(-40px);
    transform: translateX(-40px);
    -webkit-transition: -webkit-transform 0.35s;
    transition: -webkit-transform 0.35s;
    transition: transform 0.35s;
    transition: transform 0.35s, -webkit-transform 0.35s;
    width: 100%;
}
.explore-more__box .label {
    bottom: 0;
    left: 0;
    position: absolute;
    -webkit-transform: translateY(0);
    transform: translateY(0);
    -webkit-transition-delay: 0.05s;
    transition-delay: 0.05s;
    -webkit-transition-duration: 0.35s;
    transition-duration: 0.35s;
    width: 100%;
    z-index: 3;
}
.explore-more__box .label h2 {
    font: 900 28px/1 "Montserrat", sans-serif;
}
.explore-more__box .label .arrow {
    color: #c1b05c;
    font-size: 24px;
}
.explore-more__box:before {
    background: -webkit-gradient(
        linear,
        left bottom,
        left top,
        from(rgba(0, 0, 0, 0.95)),
        color-stop(30%, rgba(0, 0, 0, 0.1))
    ) !important;
    background: linear-gradient(
        0deg,
        rgba(0, 0, 0, 0.95) 0%,
        rgba(0, 0, 0, 0.1) 30%
    ) !important;
    bottom: 0;
    content: "";
    height: 100%;
    left: 0;
    position: absolute;
    width: 100%;
    z-index: 2;
}
.explore-more__box:after {
    content: "";
    display: block;
    padding-top: 115%;
}
.explore-more__box:hover .img {
    opacity: 0.6;
}
.explore-more__box:hover .img img {
    -webkit-transform: translateX(0);
    transform: translateX(0);
}
.explore-more__box:hover .label {
    -webkit-transform: translateY(-40px);
    transform: translateY(-40px);
}
.explore-more:after {
    background: #f1f1f1;
    bottom: 0;
    content: "";
    height: 100px;
    left: 0;
    position: absolute;
    width: 100%;
    z-index: -1;
}
.meta-window {
    background-position: center;
}
@media (min-width: 992px) {
    .meta-window .heading h2 {
        font-size: 68px;
    }
}
.meta-window .count-numbers .meta h2 {
    font-size: 36px;
}
.meta-window .count-numbers .meta .bar {
    background: #c1b05c;
    height: 2px;
    width: 96px;
}
.meta-window__slider {
    position: relative;
}
.meta-window__slider .owl-dots {
    text-align: center;
}
.meta-window__slider .owl-dots button {
    background: rgba(0, 0, 0, 0.25);
    border-radius: 50%;
    height: 5px;
    width: 5px;
}
.meta-window__slider .owl-dots button.active {
    background: #000;
}
.meta-window__slider .owl-dots button + button {
    margin-left: 5px;
}
.meta-window__slider:before {
    background: #fff;
    bottom: 0;
    content: "";
    height: 200px;
    left: 0;
    position: absolute;
    width: 100%;
}
.meta-window.cover-bg {
    background-position: center center;
    background-size: cover;
    isolation: isolate;
    position: relative;
}
.meta-window.cover-bg:before {
    background: -webkit-gradient(
        linear,
        left top,
        left bottom,
        from(rgba(0, 0, 0, 0.67)),
        to(rgba(0, 0, 0, 0.67))
    );
    background: linear-gradient(
        180deg,
        rgba(0, 0, 0, 0.67) 0%,
        rgba(0, 0, 0, 0.67) 100%
    );
    content: "";
    height: 100%;
    left: 0;
    position: absolute;
    top: 0;
    width: 100%;
    z-index: -1;
}
.league-table {
    background: #fff;
}
.league-table .table-heading {
    font: 16px/1 "Montserrat", sans-serif;
}
.league-table .table-heading .copy-link,
.league-table .table-heading .search {
    color: #194795;
    cursor: pointer;
}
.league-table .table-heading .select-filter {
    position: relative;
}
.league-table .table-heading .select-filter .selected {
    border: 1px solid #0b4499;
    border-radius: 20px;
    cursor: pointer;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    width: 120px;
}
.league-table .table-heading .select-filter .selected .text {
    font: 14px/1 "Montserrat", sans-serif;
}
.league-table .table-heading .select-filter .selected .arrow {
    font-size: 14px;
}
.league-table .table-heading .select-filter .selected:hover {
    background: #0b4499;
    color: #fff;
}
.league-table .table-heading .select-filter .options {
    background-color: #fff;
    border: 1px solid #0b4499;
    border-radius: 20px;
    display: none;
    left: 0;
    overflow: hidden;
    position: absolute;
    top: 0;
    width: 100%;
}
.league-table .table-heading .select-filter .options li {
    color: #0b4499;
    cursor: pointer;
    font: 600 14px/1 "Montserrat", sans-serif;
}
.league-table .table-heading .select-filter .options li .check {
    border: 1px solid #0b4499;
    border-radius: 4px;
    height: 16px;
    width: 16px;
}
.league-table .table-heading .select-filter .options li.clicked {
    background: rgba(0, 0, 0, 0.04);
}
.league-table .table-heading .select-filter .options li:hover {
    background: rgba(0, 0, 0, 0.04);
}
.league-table .table-heading .select-filter .options li + li {
    color: rgba(0, 0, 0, 0.8);
}
.league-table .table-heading .select-filter.open .selected {
    opacity: 0;
    visibility: hidden;
}
.league-table .table-heading .select-filter.open .options {
    display: block;
}
.season-tabs .tab {
    background: #f1f1f1;
    border-top: 4px solid #f1f1f1;
    color: #0b4499;
    cursor: pointer;
    font: 900 18px/1 "Montserrat", sans-serif;
    isolation: isolate;
    position: relative;
    -webkit-transition: all 150ms ease;
    transition: all 150ms ease;
}
.season-tabs .tab:before {
    background: #0b4499;
    bottom: 0;
    content: "";
    height: 4px;
    left: 0;
    position: absolute;
    -webkit-transition: all 150ms ease;
    transition: all 150ms ease;
    width: 100%;
    z-index: -1;
}
.season-tabs .tab.active {
    color: #fff;
}
.season-tabs .tab.active:before {
    height: 100%;
}
.season-tabs .tab.bg-dark-green {
    color: #006a3a;
}
.season-tabs .tab.bg-dark-green:before {
    background-color: #006a3a;
}
.season-tabs .tab.bg-dark-green.active {
    color: #fff;
}
.season-tabs .tab.bg-dark-blue {
    color: #062e69;
}
.season-tabs .tab.bg-dark-blue:before {
    background-color: #062e69;
}
.season-tabs .tab.bg-dark-blue.active {
    color: #fff;
}
.season-tabs .tab.bg-gold {
    color: #c1b05c;
}
.season-tabs .tab.bg-gold:before {
    background-color: #c1b05c;
}
.season-tabs .tab.bg-gold.active {
    color: #fff;
}
.season-tabs .tab.bg-yellow .tab {
    color: #ddcf72;
}
.season-tabs .tab.bg-yellow .tab:before {
    background-color: #ddcf72;
}
.season-tabs .tab.bg-yellow .tab.active {
    color: #fff;
}
.season-tabs .tab-content {
    border: 1px solid #b3b3b3;
    display: none;
}
.season-tabs .tab-content h2 {
    color: #062e69;
    font-size: 24px;
}
.season-tabs .tab-content h3 {
    color: #062e69;
    font-size: 18px;
}
.season-tabs .tab-content h4,
.season-tabs .tab-content h5 {
    font-size: 20px;
}
.season-tabs .tab-content h3,
.season-tabs .tab-content h4,
.season-tabs .tab-content h5 {
    font-size: 20px;
    font-weight: 400;
}
.inner-acc .head {
    background: #0b4499;
    color: #fff;
    cursor: pointer;
}
.inner-acc .inner-content {
    background: #f1f1f1;
    display: none;
}
.inner-acc + .inner-acc {
    margin-top: 8px;
}
.plan-copy p {
    margin-bottom: 0;
}
.league-faqs .faq-box .heading {
    background: #0b4499;
    border-bottom: 2px solid #c1b05c;
    cursor: pointer;
}
.league-faqs .faq-box .heading h2 {
    font-size: 18px;
}
.league-faqs .faq-box .heading h2,
.league-faqs .faq-box .heading .icon {
    color: #fff;
}
.league-faqs .faq-box .content {
    background: #f1f1f1;
    border-bottom: 1px solid #c1b05c;
    border-left: 1px solid #c1b05c;
    border-right: 1px solid #c1b05c;
    display: none;
}
.league-faqs .faq-box + .faq-box {
    margin-top: 15px;
}
.league-faqs .faq-box.bg-dark-green .heading {
    background: #006a3a;
}
.league-faqs .faq-box.bg-dark-blue .heading {
    background: #062e69;
}
.league-faqs .faq-box.bg-gold .heading {
    background: #c1b05c;
}
.league-faqs .faq-box.bg-yellow .heading {
    background: #ddcf72;
}
.footer .foot-row {
    border-top: 1px solid #5c5051;
}
@media (min-width: 992px) {
    .footer .bod-start {
        border-left: 1px solid #5c5051;
    }
}
.footer .contact {
    background: #062e69;
    font: 900 18px/1 "Poppins", sans-serif;
    height: 100%;
}
.footer .map {
    position: relative;
}
.footer .map iframe {
    height: 100%;
    left: 0;
    position: absolute;
    top: 0;
    width: 100%;
}
.footer .location {
    height: 100%;
}
.footer h2 {
    color: #877b41;
    font: 900 18px/1 "Montserrat", sans-serif;
}
.footer p,
.footer li {
    font: 16px "Poppins", sans-serif;
}
.footer a {
    color: #0b4499;
    text-decoration: none;
}
.footer li + li {
    margin-top: 0.4rem;
}
.footer .social li {
    margin-top: 0;
}
.footer .copyright p {
    color: #999;
}
.footer .copyright p a {
    color: inherit;
}
.footer .designed-by p {
    color: #999;
    font-size: 14px;
    font-weight: 300;
}
.form .form-label {
    margin-bottom: 0.875rem;
}
.form .form-label,
.form .form-check-label,
.form .form-col-label {
    color: #c1b05c;
    font: 900 16px/1 "Montserrat", sans-serif;
    letter-spacing: 2px;
}
.form .form-check-input:not(:checked) {
    background-color: #d9d9d9;
}
.form .form-control {
    background: #d9d9d9;
    border-radius: 14px;
    font: 500 16px/1 "Montserrat", sans-serif;
    height: 48px;
}
.form .form-check-label,
.form .form-col-label {
    color: #000;
    font-weight: 400;
}
.form .select-arrow select {
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
}
.form .select-arrow .arrow {
    color: #062e69;
    pointer-events: none;
    position: absolute;
    right: 10px;
    top: 50%;
    -webkit-transform: translateY(-50%);
    transform: translateY(-50%);
}
.form .form-sub-label {
    color: #000;
    font: 16px/1 "Montserrat", sans-serif;
    letter-spacing: 2px;
}
.form input::-webkit-outer-spin-button,
.form input::-webkit-inner-spin-button {
    -webkit-appearance: none;
    margin: 0;
}
.form input[type="number"] {
    -webkit-appearance: textfield;
    -moz-appearance: textfield;
    appearance: textfield;
}
.form .cta {
    font: 700 14px/1 "Montserrat", sans-serif;
    height: 40px;
    width: 252px;
}
.admin-welcome .cta-admin {
    background: #d9d9d9;
    border-radius: 1rem;
    color: #0b4499;
    font: 900 16px/1 "Montserrat", sans-serif;
    letter-spacing: 2px;
    min-height: 110px;
}
.report-generate .heading h3 {
    color: #c1b05c;
    font-weight: 900;
}
.report-generate .report-ctas .cta-report {
    background: #d9d9d9;
    border-radius: 1rem;
    color: #0b4499;
    font: 900 16px/1 "Montserrat", sans-serif;
    letter-spacing: 2px;
    min-height: 90px;
}
.report-generate .report-ctas .cta-generated {
    background: #fff;
    border: 1px solid #0b4499;
    border-radius: 1rem;
    color: #0b4499;
    font: 900 16px/1 "Montserrat", sans-serif;
    letter-spacing: 2px;
}
.report-generate .report-ctas .cta-generated.active {
    background: #0b4499;
    color: #fff;
}
.cta-check-list .form-check {
    margin-bottom: 0;
    min-height: 10px;
}
.cta-check-list .form-check .form-check-label {
    font-size: 14px;
    position: relative;
    top: -2px;
}
.heading h2 {
    color: #062e69;
    font: 900 36px/1 "Montserrat", sans-serif;
}
@media (min-width: 992px) {
    .heading h2 {
        font-size: 48px;
    }
}
.heading h3 {
    color: #676767;
    font: 16px/1 "Poppins", sans-serif;
    letter-spacing: 3px;
}
.sec {
    padding-block: 3rem;
}
.cta {
    background: #0b4499;
    border: 1px solid #0b4499;
    border-radius: 5rem;
    color: #fff;
    font: 900 18px/1 "Poppins", sans-serif;
    letter-spacing: 0.34px;
    line-height: 1.2;
    padding: 0.75rem 1.5rem;
    text-transform: uppercase;
}
.cta.gold {
    background: #c1b05c;
    border-color: #c1b05c;
    color: #0b4499;
}
.cta.gold:hover {
    background: #0b4499;
    border-color: #0b4499;
    color: #fff;
}
.cta:hover {
    background: #fff;
    color: #0b4499;
}
.cta.hover-blue-yellow:hover {
    background: #decf73;
    border-color: #decf73;
    color: #062e69;
}
.cta.hover-dark:hover {
    background: #062e68;
    border-color: #062e68;
}
.cta:active {
    position: relative;
    top: 1px;
}
.social-sec {
    background: #f1f1f1;
}
.social-sec .head {
    position: relative;
}
.social-sec .head h2 {
    background: #f1f1f1;
    position: relative;
    z-index: 1;
}
.social-sec .head:after {
    background: #c1b05c;
    content: "";
    height: 2px;
    height: 2px;
    left: 0;
    position: absolute;
    top: 50%;
    -webkit-transform: translateY(-50%);
    transform: translateY(-50%);
    width: 100%;
}
.social li {
    margin: 0;
}
.social li + li {
    margin-left: 0.8rem;
}
.social a {
    background: #062e69;
    color: #fff;
    font-size: 20px;
    height: 40px;
    width: 40px;
}
.welcome .heading h2 {
    color: #062e69;
    font-size: 36px;
}
.welcome .heading h3 {
    color: #c1b05c;
    font: 900 16px "Montserrat", sans-serif;
}
.welcome .h3 {
    color: #062e69;
    font-size: 20px;
}
.modal .modal-close {
    color: #062e69;
    cursor: pointer;
    font-size: 24px;
    position: absolute;
    right: 20px;
    top: 15px;
}
.cta-row .cta + .cta {
    margin-left: 0.8rem;
}
@media (min-width: 1600px) {
    .ps-xxl-5 {
        padding-left: 0 !important;
    }
}
.border-navy {
    border-color: #062e69;
    border-width: 2px;
}
.bar-bg {
    overflow: hidden;
}
.bar-bg h2 {
    position: relative;
}
.bar-bg h2:before,
.bar-bg h2:after {
    background: #c1b05c;
    content: "";
    height: 2px;
    position: absolute;
    top: 50%;
    -webkit-transform: translateY(-50%);
    transform: translateY(-50%);
    width: 10000%;
}
.bar-bg h2:before {
    left: 100%;
}
.bar-bg h2:after {
    right: 100%;
}
.bar-bg.bg-dark-green h2:before,
.bar-bg.bg-dark-green h2:after {
    background: #006a3a;
}
.bar-bg.bg-dark-blue h2:before,
.bar-bg.bg-dark-blue h2:after {
    background: #062e69;
}
.bar-bg.bg-gold h2:before,
.bar-bg.bg-gold h2:after {
    background: #c1b05c;
}
.bar-bg.bg-yellow h2:before,
.bar-bg.bg-yellow h2:after {
    background: #ddcf72;
}
@media (min-width: 768px) {
    .sec-party .party-meta .box .dot-row {
        isolation: isolate;
        position: relative;
    }
}
.sec-party .party-meta .box .dot-row .dot {
    background: #062e69;
    border: 6px solid #fff;
    border-radius: 50%;
    height: 50px;
    width: 50px;
}
@media (min-width: 768px) {
    .sec-party .party-meta .box .dot-row:before {
        background: rgba(6, 46, 105, 0.24);
        content: "";
        height: 1px;
        left: 0;
        position: absolute;
        top: 50%;
        -webkit-transform: translateY(-50%);
        transform: translateY(-50%);
        width: 100%;
        z-index: -1;
    }
}
.sec-party .party-meta .box h2 {
    font-size: 22px;
    letter-spacing: normal;
}
@media (min-width: 768px) {
    .sec-party .party-meta .box:first-child .dot-row:before {
        left: 50%;
        width: 50%;
    }
    .sec-party .party-meta .box:last-child .dot-row:before {
        width: 50%;
    }
}
.booking-email .meta {
    background: #062e69;
    border-radius: 15px 0 0 15px;
    border-right: 2px solid #decf73;
    padding: 0.8rem 1.5rem;
}
.booking-email .meta h2 {
    font-size: 28px;
    letter-spacing: 1px;
}
.booking-email .meta p {
    font-size: 12px;
}
.booking-email .cta-col {
    background: #0b4499;
    border-radius: 0 15px 15px 0;
    height: 100%;
    overflow: hidden;
}
.booking-email .cta-col a {
    font: 900 18px "Poppins", sans-serif;
    height: 100%;
    padding: 0.8rem 1.5rem;
}
.booking-email .cta-col a:hover {
    background: #062e69;
}
.sec-specs .spec-wrap .box .dot-wrap {
    isolation: isolate;
    margin-right: 20px;
    position: relative;
}
.sec-specs .spec-wrap .box .dot-wrap .dot {
    background: #decf73;
    border: 6px solid #fff;
    border-radius: 50%;
    height: 50px;
    width: 50px;
}
.sec-specs .spec-wrap .box .dot-wrap .dot:before {
    background: rgba(6, 46, 105, 0.24);
    content: "";
    height: 100%;
    left: 50%;
    position: absolute;
    top: 0;
    -webkit-transform: translateX(-50%);
    transform: translateX(-50%);
    width: 1px;
    z-index: -2;
}
.sec-specs .spec-wrap .box .text {
    padding-bottom: 30px;
}
.sec-specs .spec-wrap .box .text h2 {
    color: #062e69;
    font-size: 22px;
    letter-spacing: normal;
}
.sec-specs .spec-wrap .box:last-child .dot-wrap .dot:before {
    display: none;
}
.sec-contact {
    background: linear-gradient(280deg, #0b4499 0%, #062e69 100%);
}
.sec-contact .contact-box {
    border-radius: 20px;
    padding: 20px;
}
@media (min-width: 768px) {
    .sec-contact .contact-box {
        height: 100%;
    }
}
.sec-contact .contact-box h2 {
    color: #062e69;
    font-size: 18px;
    font-weight: 900;
    letter-spacing: normal;
}
.sec-contact .contact-box .bar {
    background: #c1b05c;
    height: 2px;
    width: 70px;
}
.sec-contact .contact-box p {
    color: #000;
    font-size: 16px;
    line-height: 1.5;
    margin-bottom: 0;
}
.sec-contact .contact-box a {
    color: inherit;
    text-decoration: none;
}
@media (min-width: 768px) {
    .sec-gallery .img-gallery-magnific {
        -webkit-columns: 2;
        -moz-columns: 2;
        columns: 2;
        gap: 1rem;
    }
}
@media (min-width: 992px) {
    .sec-gallery .img-gallery-magnific {
        -webkit-columns: 3;
        -moz-columns: 3;
        columns: 3;
    }
}
.sec-gallery .img-gallery-magnific .magnific-img {
    overflow: hidden;
}
.sec-gallery .img-gallery-magnific .magnific-img a {
    background-position: center;
    background-size: cover;
    cursor: -webkit-zoom-in;
    cursor: zoom-in;
    position: relative;
    -webkit-transition: -webkit-transform 0.3s ease;
    transition: -webkit-transform 0.3s ease;
    transition: transform 0.3s ease;
    transition: transform 0.3s ease, -webkit-transform 0.3s ease;
}
.sec-gallery .img-gallery-magnific .magnific-img a img {
    opacity: 0;
}
.sec-gallery .img-gallery-magnific .magnific-img a:before {
    background: rgba(11, 68, 153, 0.4);
    content: "";
    height: 100%;
    left: 0;
    opacity: 0;
    position: absolute;
    top: 0;
    -webkit-transition: opacity 0.3s ease;
    transition: opacity 0.3s ease;
    width: 100%;
}
.sec-gallery .img-gallery-magnific .magnific-img a:hover {
    -webkit-transform: scale(1.1);
    transform: scale(1.1);
}
.sec-gallery .img-gallery-magnific .magnific-img a:hover:before {
    opacity: 1;
}
@media (max-width: 1601px) {
    .container {
        max-width: 97%;
    }
}
h2,
.h2 {
    font: 900 48px/1 "Montserrat", sans-serif;
    letter-spacing: 2px;
}
h3,
.h3 {
    font: 900 42px/1 "Montserrat", sans-serif;
}
h4,
.h4 {
    font: 900 36px/1 "Montserrat", sans-serif;
}
h5,
.h5 {
    font: 900 30px/1 "Montserrat", sans-serif;
}
p,
.p,
li {
    color: #0a0a0a;
    font: 500 20px/1.5 "Didact Gothic", sans-serif;
}
p:last-of-type {
    margin-bottom: 0;
}
p.note {
    font-size: 16px;
}
.navy-text {
    color: #062e69 !important;
}
.text-blue {
    color: #0b4499 !important;
}
.text-dark-green {
    color: #006a3a !important;
}
.text-dark-blue {
    color: #062e69 !important;
}
.text-gold {
    color: #c1b05c !important;
}
.text-yellow {
    color: #ddcf72 !important;
}
.loader {
    background: #fff;
    height: 100%;
    position: fixed;
    width: 100%;
    z-index: 10000;
}
.loader .progress {
    -webkit-animation: spin 1s linear infinite;
    animation: spin 1s linear infinite;
    background: inherit;
    border: 5px solid rgba(6, 46, 105, 0.2);
    border-radius: 50%;
    border-top-color: #062e69;
    height: 5rem;
    width: 5rem;
}
.loader h2 {
    font-family: system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI",
        Roboto, Oxygen, Ubuntu, Cantarell, "Open Sans", "Helvetica Neue",
        sans-serif;
    text-transform: uppercase;
}
.cta-response {
    -webkit-animation: spin 1s linear infinite;
    animation: spin 1s linear infinite;
    border: 3px solid rgba(255, 255, 255, 0.35);
    border-radius: 50%;
    border-top-color: #fff;
    height: 20px;
    width: 20px;
}
@-webkit-keyframes spin {
    0% {
        -webkit-transform: rotate(0deg);
        transform: rotate(0deg);
    }
    100% {
        -webkit-transform: rotate(360deg);
        transform: rotate(360deg);
    }
}
@keyframes spin {
    0% {
        -webkit-transform: rotate(0deg);
        transform: rotate(0deg);
    }
    100% {
        -webkit-transform: rotate(360deg);
        transform: rotate(360deg);
    }
}
.x-factor {
    background: #111;
}
.x-factor .loader {
    background: #111;
}
.x-factor .loader .progress {
    border: 5px solid rgba(193, 176, 92, 0.2);
    border-top-color: #c1b05c;
}
.x-factor .loader h2 {
    color: #c1b05c;
}
.x-factor .header .header_meta {
    background: #000;
}
.x-factor .header .header_meta h2 {
    color: #c1b05c;
}
.x-factor .header .header_meta .social li a {
    color: #c1b05c;
}
.x-factor .header .site_nav li a,
.x-factor .header .site_nav li .more {
    color: #fff;
}
.x-factor .header .site_nav li.active > a {
    color: #c1b05c;
}
.x-factor .header .site_nav li .sub_nav {
    background: #111;
}
.x-factor .header .site_nav li .sub_nav .more {
    color: #c1b05c;
}
.x-factor .header .site_nav li .sub_nav li a {
    color: #c1b05c;
}
.x-factor .header .site_nav li .sub_nav li a:hover {
    background: #000;
}
.x-factor .heading h2 {
    color: #c1b05c;
}
.x-factor .social a {
    background: #383a3c;
    color: #c1b05c;
}
.x-factor li,
.x-factor p {
    color: #fff;
}
.x-factor .cta {
    background: #c1b05c;
    border-color: #c1b05c;
}
.x-factor .cta:hover {
    background: #062e69;
    border-color: #062e69;
    color: #fff;
}
.x-factor .footer .contact {
    background: #c1b05c;
}
.x-factor .footer .social a {
    background: #c1b05c;
    color: #111;
}
.x-factor .footer a {
    color: #fff;
}
body {
    font: 500 20px/1.5 "Didact Gothic", sans-serif;
    text-wrap: pretty;
}
body.loading {
    overflow: hidden;
}
body.bg-brand {
    background-position: 200% 100px;
    background-repeat: no-repeat;
} /*# sourceMappingURL=app.min.css.map */
.pagination {
    margin: 0;
    padding: 0;
    display: flex;
    justify-content: center;
}

.pagination .page-item {
    margin: 0 0.25rem;
}

.pagination .page-link {
    border-radius: 0.375rem;
    padding: 0.5rem 0.75rem;
    color: #062e69;
    border: 1px solid #dee2e6;
    background-color: #fff;
}

.pagination .page-item.active .page-link {
    background-color: #062e69;
    color: #fff;
    border-color: #062e69;
}

.pagination .page-item.disabled .page-link {
    color: #6c757d;
    border-color: #dee2e6;
    background-color: #e9ecef;
}

.pagination .page-link:hover {
    color: #0056b3;
    text-decoration: none;
}

.pagination .page-link:focus,
.pagination .page-link:focus:hover {
    box-shadow: none;
}

button#sendInviteButton {
    margin-top: 1rem;
}

.player-profile {
    text-decoration: none;
}
.submit-btn {
    background: #0b4499;
    border: 1px solid #0b4499;
    border-radius: 5rem;
    color: #fff;
    font: 700 14px / 1 "Montserrat", sans-serif;
    letter-spacing: 0.34px;
    line-height: 1.2;
    padding: 0.75rem 1.5rem;
    text-transform: uppercase;
}

.submit-btn:hover {
    background: #fff;
    color: #0b4499;
}

#signupAsCoach {
    text-decoration: none;
    display: inline-block;
    margin-left: 8rem;
    margin-top: 2rem;
}

#signupAsGuardian {
    text-decoration: none;
    display: inline-block;
    margin-left: 8rem;
    margin-top: 2rem;
}
.team-select {
    color: #333;
    padding: 0 8px;
}

.team-select option:hover {
    background-color: #0b4499;
    color: white;
}

.buttonLoader {
    pointer-events: none;
}

.custom-toggle {
    width: 120px;
    height: 50px;
    position: relative;
}

.custom-toggle input[type="checkbox"] {
    display: none;
}

.custom-slider {
    position: absolute;
    cursor: pointer;
    width: 100%;
    height: 100%;
    background-color: #ccc;
    border-radius: 50px;
    transition: 0.4s;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 10px;
}

.custom-slider:before {
    content: "Guardian";
    position: absolute;
    left: 10px;
    color: white;
    font-size: 18px;
}

.custom-slider:after {
    content: "Coach";
    position: absolute;
    right: 10px;
    color: white;
    font-size: 18px;
}

.custom-slider:after {
    height: 40px;
    width: 40px;
    background-color: white;
    border-radius: 50%;
    transition: 0.4s;
}

input:checked + .custom-slider {
    background-color: #0d6efd;
}

input:checked + .custom-slider:after {
    transform: translateX(70px);
}
.custom-select-dropdown {
    appearance: none;
    -webkit-appearance: none;
    -moz-appearance: none;
    background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="white" class="bi bi-caret-down-fill" viewBox="0 0 16 16"><path d="M7.247 11.14 2.451 6.344a.5.5 0 0 1 .708-.708l4.096 4.096 4.096-4.096a.5.5 0 1 1 .708.708l-4.796 4.796a.5.5 0 0 1-.708 0z"/></svg>');
    background-repeat: no-repeat;
    background-position: right 10px center;
    background-size: 16px 16px;
    padding-right: 30px;
}
.team-select{
    color:white;
}
.spinner {
    display: inline-block;
    width: 24px;
    height: 24px;
    border: 3px solid rgba(0, 0, 0, 0.1);
    border-top-color: #0b4499;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    vertical-align: middle;
    margin
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}

.email-checkbox-wrapper {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
}

.styled-checkbox {
    margin-right: 10px;
}

.styled-label,
.styled-email {
    font-size: 14px;
}

.styled-checkbox-placeholder {
    display: inline-block;
    width: 16px;
    height: 16px;
    margin-right: 10px;
}

.styled-checkbox {
    width: 16px;
    height: 16px;
    border-radius: 3px;
    border: 1px solid #ccc;
    appearance: none;
    cursor: pointer;
    transition: background-color 0.2s ease-in-out;
    position: relative;
}

.styled-checkbox:checked {
    background-color: #0b4499;
    border-color: #0b4499;
}

.styled-checkbox:checked::after {
    content: "✓";
    color: white;
    font-size: 14px;
    position: absolute;
    top: -1px;
    left: 2px;
}

.styled-checkbox:focus {
    outline: none;
    box-shadow: 0 0 3px rgba(0, 123, 255, 0.5);
}

.text-warning{
    color: #c1b05c !important;
    font: 900 16px / 1 "Montserrat", sans-serif;
    letter-spacing: 2px
}

.text-success{
    color: #0b4499 !important;
    font: 900 16px / 1 "Montserrat", sans-serif;
    letter-spacing: 2px
}
.backButtonforAddPrograms{
    position:relative;
    top:3rem;
    margin-left:7rem;
    display:flex;
    justify-content:flex-start;
}

    #differentGuardians {
        display: block;
        width: 100%;
    }

    .choices__list--multiple {
        display: flex !important;
        flex-wrap: wrap !important;
        margin-top: 5px !important;
    }

    .choices__list--multiple .choices__item {
        background-color:#0b4499 !important;
    }
.choices__input{
    background-color:#d9d9d9 !important;
    min-width: 200px;
    padding: 8px;
    font: 500 16px / 1 "Montserrat", sans-serif !important;
}

.choices__inner{
    border-radius:12.5px !important;
      background-color:#d9d9d9 !important;
}

.choices__input::placeholder {
   font:500 16px / 1 "Montserrat", sans-serif;
}


 /* custom css */

.stripe-input {
        padding: 10px 12px;
        border: 1px solid #ced4da;
        border-radius: 4px;
        font-size: 16px;
        width: 100%;
        box-sizing: border-box;
        margin-bottom: 16px;
    }


    .stripe-input.focused {
        border-color: #5e72e4;
        box-shadow: 0 0 8px rgba(94, 114, 228, 0.1);
    }

    #errorMessage{
    display: none;
    position: fixed;
    top: 10px;
    left: 50%;
    transform: translateX(-50%);
    background-color: #f8d7da;
    color: #721c24;
    padding: 10px 20px;
    border-radius: 5px;
    font-weight: bold;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    z-index: 1000;
    }


#successMessageForSession {
    position: fixed;
    top: -100px;
    left: 50%;
    transform: translateX(-50%);
    z-index: 1000;
    padding: 15px 25px;
    color: #ffffff;
    font-size: 18px;
    border-radius: 8px;
    box-shadow: 0px 4px 8px rgba(0, 0, 0, 0.2);
    opacity: 0;
    transition: opacity 0.3s ease;
    background-color: #28a745;
}

#successMessageForSession.slide-in {
    top: 20px;
    opacity: 1;
    animation: slide-in 0.5s ease-out;
}


#successMessageForSession.slide-out {
    animation: slide-out 0.5s ease-in forwards;
}


@keyframes slide-in {
    from {
        top: -100px;
        opacity: 0;
    }
    to {
        top: 20px;
        opacity: 1;
    }
}


@keyframes slide-out {
    from {
        top: 20px;
        opacity: 1;
    }
    to {
        top: -100px;
        opacity: 0;
    }
}






#errorMessageForSession {
    position: fixed;
    top: -100px;
    left: 50%;
    transform: translateX(-50%);
    z-index: 1000;
    padding: 15px 25px;
    color: #ffffff;
    font-size: 18px;
    border-radius: 8px;
    box-shadow: 0px 4px 8px rgba(0, 0, 0, 0.2);
    opacity: 0;
    transition: opacity 0.3s ease;
    background-color: #e74c3c;
}


#errorMessageForSession.slide-in {
    top: 20px;
    opacity: 1;
    animation: slide-in 0.5s ease-out;
}


#errorMessageForSession.slide-out {
    animation: slide-out 0.5s ease-in forwards;
}


@keyframes slide-in {
    from {
        top: -100px;
        opacity: 0;
    }
    to {
        top: 20px;
        opacity: 1;
    }
}


@keyframes slide-out {
    from {
        top: 20px;
        opacity: 1;
    }
    to {
        top: -100px;
        opacity: 0;
    }
}
#noPaymentMessage {
    font-size: 1.2em;
    font-weight: bold;
    color: #6c757d;
    margin-top: 10px;
}


 .loaderDiv {
            display: none;
            position: fixed;
            margin: 0px;
            padding: 0px;
            right: 0px;
            top: 0px;
            width: 100%;
            height: 100%;
            background-color: #fff;
            z-index: 30001;
            opacity: 0.8;
        }.loaderImg {
            position: absolute;
            top: 0;
            bottom: 0;
            left: 0;
            right: 0;
            margin: auto;
        }
 #passwordStrengthIndicator {
        background-color: #e9ecef;
        border-radius: 5px;
    }
    #passwordStrengthBar {
        transition: width 0.3s ease;
        border-radius: 5px;
    }
  .weak { background-color: #ff4d4d; }
    .medium { background-color: #ffcc00; }
    .strong { background: linear-gradient(90deg, #00e676, #00b0ff); }
    .too-short { background-color: #ff6666; }

.eye-icon {
    position: absolute;
    top: 50%;
    right: 10px;
    transform: translateY(-50%);
    cursor: pointer;
    width: 30px;
    height: 20px;
}

/* Eye shape with rounded appearance */
.eye-shape {
    position: relative;
    width: 100%;
    height: 100%;
    border-radius: 50%;
    border: 2px solid #555;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
    background: #ffffff;
}

/* Pupil */
.pupil {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background-color: #333;
    transition: opacity 0.3s ease;
}

.eyelid {
    position: absolute;
    top: 50%;
    width: 100%;
    height: 100%;
    background: #ffffff;
    border-top: 2px solid #555;
    border-radius: 50%;
    transition: transform 0.3s ease;
    transform: translateY(-50%) scaleY(0);
}

.eye-icon.closed .pupil {
    opacity: 0;
}

.eye-icon.closed .eyelid {
    transform: translateY(-50%) scaleY(1);
}

.eye-icon.opened .pupil {
    opacity: 1;
}

.eye-icon.opened .eyelid {
    transform: translateY(-50%) scaleY(0);
}


    #passwordErrorMessage {
    display: none;
    color: rgba(220, 53, 69, 1);
    margin-top: 5px;
}

#passwordStrengthIndicator {
    margin-top: 10px;
}

#passwordStrengthText {
    margin-top: 5px;
}

.modal-table-program {
    max-height: 400px;
    overflow-y: auto;
    display: block;
}
.modal-table-program {
    padding-bottom: 100px;
}
td {
    color: #0B4499 !important;
}


.openModalButton, .playerModalButton {
    transition: background-color 0.3s ease, color 0.3s ease;
}

.openModalButton:hover, .playerModalButton:hover {
    background-color: white;
    color: #0b4499 !important;
}

.payments-table{
    background: #D9D9D9;
    height: max-content;
     /* overflow: scroll; */
}

.main-table{
     /* border-bottom: 81px #f80000dd; */
    border-bottom: 1px solid #f7ececdd;
    background: #D9D9D9;
}

#confirmationBar button {
    border-radius: 5px;
    font-weight: bold;
    transition: background-color 0.3s ease;
}

#confirmationBar button:hover {
    opacity: 0.9;
}

/* Toast base styles */
.toast {
    position: fixed;
    top: -100px; /* Start above the viewport */
    left: 50%;
    transform: translateX(-50%);
    z-index: 10000;
    padding: 15px 25px;
    color: white;
    font-size: 16px;
    border-radius: 8px;
    box-shadow: 0px 4px 6px rgba(0, 0, 0, 0.1);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.toast.slide-in {
    top: 20px;
    opacity: 1;
    animation: slide-in 0.5s ease-out;
}

.toast-success {
    background-color: #28a745;
}

.toast-error {
    background-color: #e74c3c;
}


.toast.slide-out {
    animation: slide-out 0.5s ease-in forwards;
}


@keyframes slide-in {
    from {
        top: -100px;
        opacity: 0;
    }
    to {
        top: 20px;
        opacity: 1;
    }
}

@keyframes slide-out {
    from {
        top: 20px;
        opacity: 1;
    }
    to {
        top: -100px;
        opacity: 0;
    }
}



@media print {
    .hide-while-printing {
        display: none;
    }
}
.email-div #emailError, #passwordError {
     visibility: hidden;
     height: 5px !important;
    font-size: 0.9rem !important;
    color: #0b4499 !important;
    line-height: 15px !important;
    font-weight: normal !important;
    margin-top: 5px !important;
}



.date-range-wrapper {
    display: flex;
    flex-direction: column;
    gap: 8px;
    padding: 8px;
}

.date-range-wrapper label {
    margin-bottom: 4px;
}

.date-range-wrapper input {
    padding: 4px;
    border-radius: 4px;
    border: 1px solid #ccc;
}
.hidden{
    display: none !important;
}



.table td, .table th {
    vertical-align: middle !important;

}
.text-custom {
    color: #0B4499 !important;
    font-size: 14px !important;
    font-family: 'Montserrat', sans-serif !important;
}

.user-management-table {
    table-layout: fixed;
}

.user-management-table td,
.user-management-table th {
    padding: 0.75rem;
    vertical-align: middle;
    box-sizing: border-box;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

/* Action buttons table styles */
.user-management-actions-table {
    width: 100%;
    table-layout: fixed;
}

.user-management-actions-table td {
    padding: 0 4px;
}

/* Ensure text doesn't wrap in cells */
.user-management-table .text-custom {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}
.user-management-table th{
color: #194795;
    font: 700 14px / 1 "Montserrat", sans-serif;
    padding-block: 0.8rem

}
@media (min-width: 576px) {
    .d-none.flex-sm-fill.d-sm-flex.align-items-sm-center.justify-content-sm-between {
        margin-bottom: 2rem;
    }
}
#success-alert .btn-close:focus {
    box-shadow: none;
    outline: none;
}

/* Table updating blur effect */
.table-updating {
    opacity: 0.6;
    filter: blur(0.5px);
    transition: opacity 0.2s ease, filter 0.2s ease;
    pointer-events: none;
}

/* Loading overlay with spinner */
.table-program.table-updating::after {
    content: "";
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 20px;
    height: 20px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #3498db;
    border-radius: 50%;
    animation: table-spin 1s linear infinite;
    z-index: 10;
}

/* Table spinner animation */
@keyframes table-spin {
    0% { transform: translate(-50%, -50%) rotate(0deg); }
    100% { transform: translate(-50%, -50%) rotate(360deg); }
}

/* Ensure smooth transition back to normal state */
.table-program {
    transition: opacity 0.2s ease, filter 0.2s ease;
    position: relative;
}



.cta-button-popup{
    background: #0b4499;
    border: 1px solid #0b4499;
    border-radius: 5rem;
    color: #fff;
    font: 900 18px / 1 "Poppins", sans-serif;
    letter-spacing: 0.34px;
    line-height: 1.2;
    padding: 0.75rem 1.5rem;
    text-transform: uppercase;

}
.cta-button-popup:hover{
    background-color: #fff !important;
    color: #0b4499 !important;
}
