<div>
    <div class="container">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <div class="heading">
                <h2 class="text-uppercase fs-6 mb-0">Tryout Programs</h2>
            </div>
            <div class="d-flex gap-2">
                <button wire:click="togglePostTryouts" class="cta">
                    <i class="bi bi-clock-history me-2"></i>
                    <?php echo e($showPostTryouts ? 'Hide' : 'Show'); ?> Post-Tryout Programs
                </button>
                <a href="<?php echo e(route('admin.program.add')); ?>" class="cta">
                    <i class="bi bi-plus-circle me-2"></i> Add New Program
                </a>
            </div>
        </div>

        
        <div class="form row table-filter justify-content-center">
            <div class="col-md-auto">
                <div class="filter-option">
                    <div class="mb-4">
                        <label class="form-label text-uppercase" for="filter">Filter By</label>
                        <div class="select-arrow position-relative">
                            <select class="form-control" id="filter" wire:model.live="filter">
                                <option value="">All</option>
                                <!--[if BLOCK]><![endif]--><?php $__currentLoopData = $this->getAvailableFilters(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $filterOption): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <option value="<?php echo e($filterOption); ?>"><?php echo e($filterOption); ?></option>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->
                            </select>
                            <span class="arrow"><i class="bi bi-chevron-down"></i></span>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-auto">
                <div class="filter-option">
                    <div class="mb-4">
                        <label class="form-label text-uppercase" for="search">Search</label>
                        <div class="flex items-center">
                            <input class="form-control" id="search" type="text"
                                wire:model.live.debounce.500ms="search" placeholder="Search programs..." />
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-auto">
                <div class="filter-option">
                    <div class="mb-4">
                        <label class="form-label text-uppercase" for="perPage">Per Page</label>
                        <div class="select-arrow position-relative">
                            <select class="form-control" id="perPage" wire:model.live="perPage">
                                <option value="5">5</option>
                                <option value="10">10</option>
                                <option value="25">25</option>
                                <option value="50">50</option>
                            </select>
                            <span class="arrow"><i class="bi bi-chevron-down"></i></span>
                        </div>
                    </div>
                </div>
            </div>
        </div>






        
        <div class="program-table">
            <div class="table-program table-responsive" wire:loading.class="table-updating" wire:target="updatedSearch,updatedFilter,updatedPerPage,togglePostTryouts">
                <table class="table table-hover" width="100%" id="user-table">
                    <thead>
                        <tr>
                            <th class="py-4">Name</th>
                            <th class="py-4">Sport</th>
                            <th class="py-4">Gender</th>
                            <th class="py-4">Age</th>
                            <th class="py-4">Dates</th>
                            <th class="py-4">Days</th>
                            <th class="py-4">Enrollment</th>
                            <th class="py-4">Cost</th>
                            <th class="py-4">Payment</th>
                            <th class="py-4">Status</th>
                            <th class="py-4" width="20"></th>
                            <th class="py-4" width="20"></th>
                            <th class="py-4" width="100">Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <!--[if BLOCK]><![endif]--><?php $__empty_1 = true; $__currentLoopData = $programs; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $program): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                            <tr wire:key="program-<?php echo e($program->id); ?>">



                                <td class="py-4" valign="middle"><?php echo e($program->name); ?></td>
                                <td class="py-4" valign="middle"><?php echo e($program->sport); ?></td>
                                <td class="py-4" valign="middle"><?php echo e(ucfirst($program->gender)); ?></td>



                                <td class="py-4" valign="middle">
                                    <?php echo e($program->age_restriction_from); ?>-<?php echo e($program->age_restriction_to); ?></td>
                                <td class="py-4" valign="middle"><?php echo e($program->start_date); ?> -
                                    <?php echo e($program->end_date); ?></td>
                                <td class="py-4 text-capitalize" valign="middle">
                                    <?php echo e(implode(', ', $program->frequency_days)); ?><br><?php echo e($program->frequency); ?>

                                </td>
                                <td class="py-4" valign="middle"><?php echo e($program->number_of_registers); ?></td>
                                <td class="py-4" valign="middle">$<?php echo e(number_format($program->cost, 2)); ?></td>
                                <td class="py-4" valign="middle"><?php echo e(ucfirst($program->payment)); ?></td>
                                <td class="py-4" valign="middle"><?php echo e(ucfirst($program->status)); ?></td>
                                <td class="py-4" valign="middle">
                                    <span class="action edit">
                                        <a href="<?php echo e(route('admin.program.edit', $program->slug)); ?>">
                                            <img src="<?php echo e(asset('images/edit-icon.svg')); ?>" alt=""
                                                width="20" height="20" />
                                        </a>
                                    </span>
                                </td>
                                <td class="py-4" valign="middle">
                                    <form id="removeProgram-<?php echo e($program->slug); ?>"
                                        action="<?php echo e(route('admin.program.destroy', $program->slug)); ?>" method="POST"
                                        onsubmit="showConfirmation(event, 'removeProgram-<?php echo e($program->slug); ?>')"
                                        style="display: inline;">
                                        <?php echo csrf_field(); ?>
                                        <?php echo method_field('DELETE'); ?>
                                        <button type="submit"
                                            style="border: none; background: none; padding: 0; cursor: pointer;">
                                            <span class="action delete">
                                                <img src="<?php echo e(asset('images/delete-icon.svg')); ?>" alt=""
                                                    width="18" height="20" />
                                            </span>
                                        </button>
                                    </form>
                                </td>
                                <td class="py-4" valign="middle">
                                    <div class="d-flex gap-2">
                                        <button wire:click="toggleProgramExpansion(<?php echo e($program->id); ?>)"
                                                class="cta" style="font-size: 12px; padding: 0.5rem 1rem;"
                                                wire:loading.attr="disabled" wire:target="toggleProgramExpansion">
                                            <!--[if BLOCK]><![endif]--><?php if(in_array($program->id, $expandedPrograms)): ?>
                                                <i class="bi bi-chevron-up me-1"></i> Hide Players
                                            <?php else: ?>
                                                <i class="bi bi-people me-1"></i> View Players
                                            <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                                        </button>
                                        <button onclick="openRegularInviteModal(<?php echo e($program->id); ?>)"
                                                class="cta" style="font-size: 12px; padding: 0.5rem 1rem; background: #c1b05c; border-color: #c1b05c;">
                                            <i class="bi bi-envelope-plus me-1"></i> Invite Player
                                        </button>
                                    </div>
                                </td>
                            </tr>

                            
                            <!--[if BLOCK]><![endif]--><?php if(in_array($program->id, $expandedPrograms)): ?>
                                <tr wire:key="program-players-<?php echo e($program->id); ?>">
                                    <td colspan="13" class="p-0">
                                        <div style="background: #f8f9fa; border-left: 4px solid #0b4499;">
                                            <div class="p-3">
                                                <h6 class="mb-3 navy-text" style="font: 700 16px/1 'Montserrat', sans-serif;">
                                                    <i class="bi bi-people me-2"></i>Registered Players
                                                </h6>

                                                <?php
                                                    $registeredPlayers = $this->getRegisteredPlayersForRegularProgram($program->id);
                                                ?>

                                                <!--[if BLOCK]><![endif]--><?php if($registeredPlayers->count() > 0): ?>
                                                    <div class="table-responsive">
                                                        <table class="table table-sm table-hover" style="margin-bottom: 0;">
                                                            <thead>
                                                                <tr>
                                                                    <th style="color: #194795; font: 700 12px/1 'Montserrat', sans-serif;">Player Name</th>
                                                                    <th style="color: #194795; font: 700 12px/1 'Montserrat', sans-serif;">Email</th>
                                                                    <th style="color: #194795; font: 700 12px/1 'Montserrat', sans-serif;">Invitation Status</th>
                                                                    <th style="color: #194795; font: 700 12px/1 'Montserrat', sans-serif;">Actions</th>
                                                                </tr>
                                                            </thead>
                                                            <tbody>
                                                                <!--[if BLOCK]><![endif]--><?php $__currentLoopData = $registeredPlayers; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $player): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                                    <tr>
                                                                        <td style="font: 14px/1 'Montserrat', sans-serif;"><?php echo e($player['name']); ?></td>
                                                                        <td style="font: 14px/1 'Montserrat', sans-serif;"><?php echo e($player['email']); ?></td>
                                                                        <td>
                                                                            <!--[if BLOCK]><![endif]--><?php switch($player['invitation_status']):
                                                                                case ('not_invited'): ?>
                                                                                    <span class="badge" style="background: #6c757d; color: white; font: 700 10px/1 'Montserrat', sans-serif; padding: 0.3rem 0.6rem;">Not Invited</span>
                                                                                    <?php break; ?>
                                                                                <?php case ('pending'): ?>
                                                                                    <span class="badge" style="background: #c1b05c; color: #062e69; font: 700 10px/1 'Montserrat', sans-serif; padding: 0.3rem 0.6rem;">Pending</span>
                                                                                    <?php break; ?>
                                                                                <?php case ('accepted'): ?>
                                                                                    <span class="badge" style="background: #28a745; color: white; font: 700 10px/1 'Montserrat', sans-serif; padding: 0.3rem 0.6rem;">Accepted</span>
                                                                                    <?php break; ?>
                                                                                <?php case ('rejected'): ?>
                                                                                    <span class="badge" style="background: #dc3545; color: white; font: 700 10px/1 'Montserrat', sans-serif; padding: 0.3rem 0.6rem;">Rejected</span>
                                                                                    <?php break; ?>
                                                                            <?php endswitch; ?><!--[if ENDBLOCK]><![endif]-->
                                                                        </td>
                                                                        <td>
                                                                            <!--[if BLOCK]><![endif]--><?php if($player['invitation_status'] === 'not_invited'): ?>
                                                                                <button onclick="openRegularInviteModal(<?php echo e($program->id); ?>)"
                                                                                        class="btn btn-sm" style="background: #c1b05c; color: #062e69; font: 700 10px/1 'Montserrat', sans-serif; padding: 0.3rem 0.6rem; border: none;">
                                                                                    <i class="bi bi-envelope me-1"></i> Invite
                                                                                </button>
                                                                            <?php else: ?>
                                                                                <span class="text-muted" style="font: 12px/1 'Montserrat', sans-serif;">-</span>
                                                                            <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                                                                        </td>
                                                                    </tr>
                                                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->
                                                            </tbody>
                                                        </table>
                                                    </div>
                                                <?php else: ?>
                                                    <p class="text-muted mb-0" style="font: 14px/1 'Montserrat', sans-serif;">No players registered for this program yet.</p>
                                                <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                                            </div>
                                        </div>
                                    </td>
                                </tr>
                            <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                                <tr>
                                    <td colspan="13" class="text-center py-4">
                                        <!--[if BLOCK]><![endif]--><?php if($search || $filter): ?>
                                            <p class="mb-0">No programs found matching your search criteria.</p>
                                            <button class="btn btn-link p-0" wire:click="clearFilters">Clear
                                                filters</button>
                                        <?php else: ?>
                                            <p class="mb-0">No programs found.</p>
                                        <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                                    </td>
                                </tr>
                            <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                        </tbody>
                    </table>

                    
                    <div class="mt-4 d-flex justify-content-center">
                        <?php echo e($programs->onEachSide(1)->links('custom-pagination')); ?>

                    </div>
                </div>
            </div>
        </div>

        
        <!--[if BLOCK]><![endif]--><?php if($showPostTryouts): ?>
            <div class="mt-5">
                <div class="heading text-center mb-4">
                    <h2 class="text-uppercase fs-6 mb-0 navy-text">Post-Tryout Programs</h2>
                    <p class="text-muted small">Programs where tryout period has ended</p>
                </div>

                <!--[if BLOCK]><![endif]--><?php $__empty_1 = true; $__currentLoopData = $postTryoutPrograms; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $program): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                    <div class="program-table mb-4">
                        <div class="table-program">
                            
                            <div class="d-flex justify-content-between align-items-center p-3" style="background: #d9d9d9; border-radius: 10px 10px 0 0;">
                                <div>
                                    <h5 class="mb-1 navy-text" style="font: 900 18px/1 'Montserrat', sans-serif;"><?php echo e($program->name); ?></h5>
                                    <small class="text-muted" style="font: 500 14px/1 'Montserrat', sans-serif;">
                                        <?php echo e($program->sport); ?> • <?php echo e(ucfirst($program->gender)); ?> •
                                        Ages <?php echo e($program->age_restriction_from); ?>-<?php echo e($program->age_restriction_to); ?> •
                                        Ended: <?php echo e(\Carbon\Carbon::parse($program->end_date)->format('M d, Y')); ?>

                                    </small>
                                </div>
                                <span class="badge text-white" style="background: #0b4499; font: 700 12px/1 'Montserrat', sans-serif; padding: 0.5rem 1rem;">
                                    <?php echo e($program->registrations->count()); ?> Players
                                </span>
                            </div>

                            
                            <!--[if BLOCK]><![endif]--><?php if($program->registrations->count() > 0): ?>
                                <div class="table-responsive">
                                    <table class="table table-hover" style="margin-bottom: 0;">
                                        <thead>
                                            <tr>
                                                <th class="py-4" style="color: #194795; font: 700 14px/1 'Montserrat', sans-serif;">Player Name</th>
                                                <th class="py-4" style="color: #194795; font: 700 14px/1 'Montserrat', sans-serif;">Email</th>
                                                <th class="py-4" style="color: #194795; font: 700 14px/1 'Montserrat', sans-serif;">Invitation Status</th>
                                                <th class="py-4" style="color: #194795; font: 700 14px/1 'Montserrat', sans-serif;">Team</th>
                                                <th class="py-4" style="color: #194795; font: 700 14px/1 'Montserrat', sans-serif;">Actions</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <!--[if BLOCK]><![endif]--><?php $__currentLoopData = $this->getPlayersForProgram($program->id); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $player): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                <tr>
                                                    <td class="py-4" valign="middle" style="font: 14px/1 'Montserrat', sans-serif;"><?php echo e($player['name']); ?></td>
                                                    <td class="py-4" valign="middle" style="font: 14px/1 'Montserrat', sans-serif;"><?php echo e($player['email']); ?></td>
                                                    <td class="py-4" valign="middle">
                                                        <!--[if BLOCK]><![endif]--><?php switch($player['invitation_status']):
                                                            case ('not_invited'): ?>
                                                                <span class="badge" style="background: #6c757d; color: white; font: 700 11px/1 'Montserrat', sans-serif; padding: 0.4rem 0.8rem;">Not Invited</span>
                                                                <?php break; ?>
                                                            <?php case ('pending'): ?>
                                                                <span class="badge" style="background: #c1b05c; color: #062e69; font: 700 11px/1 'Montserrat', sans-serif; padding: 0.4rem 0.8rem;">Pending</span>
                                                                <?php break; ?>
                                                            <?php case ('accepted'): ?>
                                                                <span class="badge" style="background: #28a745; color: white; font: 700 11px/1 'Montserrat', sans-serif; padding: 0.4rem 0.8rem;">Accepted</span>
                                                                <?php break; ?>
                                                            <?php case ('rejected'): ?>
                                                                <span class="badge" style="background: #dc3545; color: white; font: 700 11px/1 'Montserrat', sans-serif; padding: 0.4rem 0.8rem;">Rejected</span>
                                                                <?php break; ?>
                                                            <?php case ('completed'): ?>
                                                                <span class="badge" style="background: #0b4499; color: white; font: 700 11px/1 'Montserrat', sans-serif; padding: 0.4rem 0.8rem;">In Team</span>
                                                                <?php break; ?>
                                                        <?php endswitch; ?><!--[if ENDBLOCK]><![endif]-->
                                                    </td>
                                                    <td class="py-4" valign="middle" style="font: 14px/1 'Montserrat', sans-serif;"><?php echo e($player['invited_team'] ?? '-'); ?></td>
                                                    <td class="py-4" valign="middle">
                                                        <!--[if BLOCK]><![endif]--><?php if($player['invitation_status'] === 'not_invited'): ?>
                                                            <button wire:click="showInviteModal(<?php echo e($player['id']); ?>, <?php echo e($program->id); ?>)"
                                                                    class="cta" style="font-size: 12px; padding: 0.5rem 1rem;"
                                                                    wire:loading.attr="disabled" wire:target="showInviteModal">
                                                                <i class="bi bi-envelope me-1"></i> Invite
                                                            </button>
                                                        <?php elseif($player['invitation_status'] === 'accepted'): ?>
                                                            <button wire:click="movePlayerToTeam(<?php echo e($player['id']); ?>, <?php echo e($player['invitation_id']); ?>)"
                                                                    class="cta gold" style="font-size: 12px; padding: 0.5rem 1rem;"
                                                                    wire:confirm="Are you sure you want to move this player to the team?"
                                                                    wire:loading.attr="disabled" wire:target="movePlayerToTeam">
                                                                <span wire:loading.remove wire:target="movePlayerToTeam">
                                                                    <i class="bi bi-arrow-right me-1"></i> Move to Team
                                                                </span>
                                                                <span wire:loading wire:target="movePlayerToTeam">
                                                                    <i class="bi bi-arrow-clockwise spin me-1"></i> Moving...
                                                                </span>
                                                            </button>
                                                        <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                                                    </td>
                                                </tr>
                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->
                                        </tbody>
                                    </table>
                                </div>
                            <?php else: ?>
                                <div class="p-4 text-center" style="background: white; border-radius: 0 0 10px 10px;">
                                    <p class="text-muted mb-0" style="font: 500 16px/1 'Montserrat', sans-serif;">No players registered for this program.</p>
                                </div>
                            <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                        </div>
                    </div>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                    <div class="text-center py-4">
                        <p class="text-muted mb-0" style="font: 500 16px/1 'Montserrat', sans-serif;">No post-tryout programs found.</p>
                    </div>
                <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
            </div>
        <?php endif; ?><!--[if ENDBLOCK]><![endif]-->

        
        <!--[if BLOCK]><![endif]--><?php if($inviteModalOpen): ?>
            <div class="modal fade show" style="display: block;" tabindex="-1">
                <div class="modal-dialog">
                    <div class="modal-content" style="border-radius: 20px; border: none; box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);">
                        <div class="modal-header" style="border-bottom: 2px solid #d9d9d9;">
                            <h5 class="modal-title navy-text" style="font: 900 18px/1 'Montserrat', sans-serif;">Invite Player to Team</h5>
                            <button type="button" class="btn-close" wire:click="closeInviteModal"></button>
                        </div>
                        <div class="modal-body" style="padding: 2rem;">
                            <div class="mb-3">
                                <label for="teamSelect" class="form-label text-uppercase" style="color: #194795; font: 700 14px/1 'Montserrat', sans-serif; letter-spacing: 1px;">Select Team</label>
                                <div class="select-arrow position-relative">
                                    <select class="form-control" id="teamSelect" wire:model.live="selectedTeamId" style="background-color: #d9d9d9; border-radius: 12px; border: none; padding: 12px 15px; font: 500 16px/1 'Montserrat', sans-serif;">
                                        <option value="">Choose a team...</option>
                                        <!--[if BLOCK]><![endif]--><?php $__currentLoopData = $this->getAvailableTeams(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $team): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <option value="<?php echo e($team->id); ?>"><?php echo e($team->name); ?></option>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->
                                    </select>
                                    <span class="arrow"><i class="bi bi-chevron-down"></i></span>
                                </div>
                            </div>

                            <div class="mb-3">
                                <label for="assignedBalance" class="form-label text-uppercase" style="color: #194795; font: 700 14px/1 'Montserrat', sans-serif; letter-spacing: 1px;">Assign Balance Amount</label>
                                <div class="input-group">
                                    <span class="input-group-text" style="background-color: #d9d9d9; border: none; border-radius: 12px 0 0 12px; font: 500 16px/1 'Montserrat', sans-serif;">$</span>
                                    <input type="number" class="form-control <?php $__errorArgs = ['assignedBalance'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" id="assignedBalance" wire:model.live="assignedBalance"
                                           placeholder="0.00" step="0.01" min="0" max="9999.99"
                                           style="background-color: #d9d9d9; border: none; border-radius: 0 12px 12px 0; padding: 12px 15px; font: 500 16px/1 'Montserrat', sans-serif;">
                                </div>
                                <!--[if BLOCK]><![endif]--><?php $__errorArgs = ['assignedBalance'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <div class="invalid-feedback d-block" style="font: 400 12px/1 'Montserrat', sans-serif;"><?php echo e($message); ?></div>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?><!--[if ENDBLOCK]><![endif]-->
                                <small class="text-muted" style="font: 400 12px/1 'Montserrat', sans-serif;">Enter the balance amount to assign to this player (optional)</small>
                            </div>
                        </div>
                        <div class="modal-footer" style="border-top: 2px solid #d9d9d9; padding: 1.5rem 2rem;">
                            <button type="button" class="cta" style="background: #6c757d; border-color: #6c757d; margin-right: 1rem;" wire:click="closeInviteModal">Cancel</button>
                            <button type="button" class="cta" wire:click="invitePlayerToTeam"
                                    wire:loading.attr="disabled" wire:target="invitePlayerToTeam"
                                    <?php if(!$selectedTeamId): ?> disabled style="opacity: 0.6; cursor: not-allowed;" <?php endif; ?>>
                                <span wire:loading.remove wire:target="invitePlayerToTeam">Send Invitation</span>
                                <span wire:loading wire:target="invitePlayerToTeam">
                                    <i class="bi bi-arrow-clockwise spin me-1"></i> Sending...
                                </span>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-backdrop fade show"></div>
        <?php endif; ?><!--[if ENDBLOCK]><![endif]-->

        
        <div class="modal fade" id="regularInviteModal" tabindex="-1" aria-labelledby="regularInviteModalLabel" aria-hidden="true">
            <div class="modal-dialog">
                <div class="modal-content" style="border-radius: 20px; border: none; box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);">
                    <div class="modal-header" style="border-bottom: 2px solid #d9d9d9;">
                        <h5 class="modal-title navy-text" id="regularInviteModalLabel" style="font: 900 18px/1 'Montserrat', sans-serif;">Invite Player to Program</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close" onclick="resetRegularInviteModal()"></button>
                    </div>
                    <div class="modal-body" style="padding: 2rem;">
                        
                        <div wire:loading wire:target="loadPlayersForInvitation" class="text-center py-4">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">Loading...</span>
                            </div>
                            <p class="mt-2 text-muted" style="font: 500 14px/1 'Montserrat', sans-serif;">Loading available players...</p>
                        </div>

                        
                        <div wire:loading.remove wire:target="loadPlayersForInvitation">
                            <div class="mb-3">
                                <label for="playerSelect" class="form-label text-uppercase" style="color: #194795; font: 700 14px/1 'Montserrat', sans-serif; letter-spacing: 1px;">Select Player</label>
                                <div class="select-arrow position-relative">
                                    <select class="form-control" id="playerSelect" wire:model.live="selectedPlayerId" style="background-color: #d9d9d9; border-radius: 12px; border: none; padding: 12px 15px; font: 500 16px/1 'Montserrat', sans-serif;">
                                        <option value="">Choose a player...</option>
                                        <!--[if BLOCK]><![endif]--><?php $__currentLoopData = $availablePlayers; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $player): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <option value="<?php echo e($player['id']); ?>"><?php echo e($player['name']); ?> (<?php echo e($player['email']); ?>)</option>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->
                                    </select>
                                    <span class="arrow"><i class="bi bi-chevron-down"></i></span>
                                </div>
                                <!--[if BLOCK]><![endif]--><?php if(empty($availablePlayers) && !$loadingPlayers): ?>
                                    <small class="text-muted" style="font: 400 12px/1 'Montserrat', sans-serif;">All players have either registered or been invited to this program.</small>
                                <?php elseif(!$loadingPlayers): ?>
                                    <small class="text-muted" style="font: 400 12px/1 'Montserrat', sans-serif;">Select a player to invite to this program</small>
                                <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer" style="border-top: 2px solid #d9d9d9; padding: 1.5rem 2rem;">
                        <button type="button" class="cta" style="background: #6c757d; border-color: #6c757d; margin-right: 1rem;" data-bs-dismiss="modal" onclick="resetRegularInviteModal()">Cancel</button>
                        <button type="button" class="cta" wire:click="invitePlayerToRegularProgram"
                                wire:loading.attr="disabled" wire:target="invitePlayerToRegularProgram"
                                <?php if(!$selectedPlayerId || empty($availablePlayers)): ?> disabled style="opacity: 0.6; cursor: not-allowed;" <?php endif; ?>>
                            <span wire:loading.remove wire:target="invitePlayerToRegularProgram">Send Invitation</span>
                            <span wire:loading wire:target="invitePlayerToRegularProgram">
                                <i class="bi bi-arrow-clockwise spin me-1"></i> Sending...
                            </span>
                        </button>
                    </div>
                </div>
            </div>
        </div>

        
        <script>
            let regularInviteModalInstance = null;

            // Open regular invite modal with instant display
            function openRegularInviteModal(programId) {
                console.log('Opening modal for program:', programId);

                try {
                    // Get or create modal instance
                    const modalElement = document.getElementById('regularInviteModal');
                    if (!modalElement) {
                        console.error('Modal element not found');
                        return;
                    }

                    // Create new modal instance if needed
                    if (!regularInviteModalInstance) {
                        regularInviteModalInstance = new bootstrap.Modal(modalElement, {
                            backdrop: 'static',
                            keyboard: false
                        });
                    }

                    // Show modal instantly
                    regularInviteModalInstance.show();
                    console.log('Modal shown, now loading players...');

                    // Load players data via Livewire (using test method first)
                    setTimeout(() => {
                        window.Livewire.find('<?php echo e($_instance->getId()); ?>').call('testMethod', programId);
                    }, 100);

                } catch (error) {
                    console.error('Error opening modal:', error);
                }
            }

            // Reset modal data
            function resetRegularInviteModal() {
                console.log('Resetting modal data');
                try {
                    window.Livewire.find('<?php echo e($_instance->getId()); ?>').call('resetRegularInviteModal');
                } catch (error) {
                    console.error('Error resetting modal:', error);
                }
            }

            // Listen for Livewire events
            document.addEventListener('livewire:init', () => {
                Livewire.on('close-regular-invite-modal', () => {
                    console.log('Closing modal via Livewire event');
                    try {
                        if (regularInviteModalInstance) {
                            regularInviteModalInstance.hide();
                        }
                    } catch (error) {
                        console.error('Error closing modal:', error);
                    }
                });
            });

            // Clean up modal backdrop if it gets stuck
            document.addEventListener('DOMContentLoaded', function() {
                const modalElement = document.getElementById('regularInviteModal');
                if (modalElement) {
                    modalElement.addEventListener('hidden.bs.modal', function () {
                        // Remove any stuck backdrops
                        const backdrops = document.querySelectorAll('.modal-backdrop');
                        backdrops.forEach(backdrop => backdrop.remove());
                        document.body.classList.remove('modal-open');
                        document.body.style.overflow = '';
                        document.body.style.paddingRight = '';
                    });
                }
            });
        </script>
    </div>
<?php /**PATH C:\Users\<USER>\Mass-Premier-Courts\resources\views/livewire/admin/tryout-programs.blade.php ENDPATH**/ ?>