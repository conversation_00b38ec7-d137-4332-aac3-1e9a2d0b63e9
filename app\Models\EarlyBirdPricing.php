<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class EarlyBirdPricing extends Model
{
    use HasFactory;

     protected $fillable = [
        'program_id',
        'from',
        'to',
        'price_before',
        'price_on_or_after',
    ];
    protected $table = 'early_bird_pricing';

    public function program()
    {
        return $this->belongsTo(Program::class, 'program_id');
    }
}
