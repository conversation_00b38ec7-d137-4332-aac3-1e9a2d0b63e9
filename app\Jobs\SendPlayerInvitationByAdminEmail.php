<?php

namespace App\Jobs;

use App\Mail\PlayerInvitationByAdminEmail;
use App\Mail\PlayerInvitationByAdminEmailForPlayer;
use App\Models\Program;
use App\Models\User;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Foundation\Queue\Queueable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Mail;

class SendPlayerInvitationByAdminEmail implements ShouldQueue
{
    use Queueable;

    /**
     * Create a new job instance.
     */

     protected $player;
     protected $guardian;
     protected $program;
    public function __construct(User $guardian, User $player, Program $program)
    {
        $this->guardian=$guardian;
        $this->player=$player;
        $this->program=$program;
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {

        if($this->player->email){

            Mail::to($this->player->email)->send(new PlayerInvitationByAdminEmailForPlayer($this->player, $this->program));

        }
         Mail::to($this->guardian->email)->send(new PlayerInvitationByAdminEmail($this->guardian, $this->player, $this->program));
    }
}
