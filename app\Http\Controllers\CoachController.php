<?php

namespace App\Http\Controllers;


use App\Mail\SendInvitationToPlayer;
use App\Mail\SendInvitationToCoach;
use App\Mail\GuardianInvitationMail;
use App\Models\PlayerInvitation;
use App\Models\CoachInvitation;
use App\Models\GuardianInvitationByCoach;
use App\Models\PlayerProgram;
use App\Models\Program;
use App\Models\TeamPlayer;
use App\Models\TeamProgram;
use App\Models\Role;
use App\Models\Team;
use App\Models\User;
use Exception;
 use Illuminate\Support\Facades\Validator;
use Illuminate\Http\Request;
use Illuminate\Database\Eloquent\Relations\Relation;
use Illuminate\Support\Str;
use App\Notifications\Inivitation;
use Illuminate\Support\Facades\Hash;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Crypt;
use Illuminate\Support\Facades\Log;
use App\Events\PlayerInvited;
use App\Mail\CoachNotFound;
use App\Models\InvitationForNotFoundCoaches;
use App\Models\TeamCoach;

class CoachController extends Controller
{



    public function coachSignup(){
        return view('coach.signup');
    }


    public function invitedCoachSignup( Request $request, $user, $email, $token)
{


    $user = User::where('slug', $user)->firstOrFail();

    return view('coach.signup', compact('user', 'email', 'token'));
}


    public function coachStore(Request $request){
       $validatedData = $request->validate([


        'firstName'=>'required|string',
        'lastName'=>'required|string',
        'program'=>'required|string',
        'teamName'=>'required|string',
        'email'=>'required|unique:users,email',
        'password'=>'required|min:6',
        'token'=>'nullable',
       ]);

       $isInvited=null;
       if(isset($validatedData['token'])){

       $isInvited=InvitationForNotFoundCoaches::where('email', $validatedData['email'])->where('invitation_token', $validatedData['token'])->first();
       }


       if($isInvited){

         $user=User::create([
       'firstName'=>$validatedData['firstName'],
       'lastName'=>$validatedData['lastName'],
       'town'=>$validatedData['program'],
       'teamName'=>$validatedData['teamName'],
       'email'=> $validatedData['email'],
       'password'=> Hash::make($validatedData['password']),
       'is_joined'=>true,
       'is_coach'=>true,
       'current_role'=>'coach',
       ]);

       $user->roles()->attach(Role::COACH);

       $isInvited->update([
        'is_joined'=>true,
       ]);

       CoachInvitation::create([
        'coach_id'=>$isInvited->invited_by,
        'user_id'=>$user->id,
        'team_id'=>$isInvited->team_id,
        'invitation_status'=>'accepted'
       ]);

       TeamCoach::create([
        'coach_id'=>$user->id,
        'team_id'=>$isInvited->team_id,
        'is_primary'=>false,
       ]);

        return redirect()->route('loginPage')->with('success','Coach Account Created Successfully.');
    }



       else{

       $user=User::create([
       'firstName'=>$validatedData['firstName'],
       'lastName'=>$validatedData['lastName'],
       'town'=>$validatedData['program'],
       'teamName'=>$validatedData['teamName'],
       'email'=> $validatedData['email'],
       'password'=> Hash::make($validatedData['password']),
       'is_joined'=>true,
       'is_coach'=>true,
       'current_role'=>'coach',
       ]);

       $user->roles()->attach(Role::COACH);

       $team=Team::create([

        'user_id'=>$user->id,
        'name'=> $validatedData['teamName'],

       ]);
        TeamCoach::create([
        'coach_id'=>$user->id,
        'team_id'=>$team->id,
        'is_primary'=>true,
       ]);


       return redirect()->route('loginPage')->with('success','Coach Account Created Successfully.');
    }

    }




  public function index(Request $request, $team = null)
{
    $user = auth()->user();

    if (!$user) {
        return redirect()->back()->withErrors(['message' => 'UNAUTHORIZED ACCESS']);
    }

    if ($team) {

    $teamId = intval($team);

    $teamCoach = TeamCoach::where('team_id', $teamId)
        ->where('coach_id', $user->id)
        ->first();
    if (!$teamCoach) {
        return redirect()->route('coach.dashboard');
    }


    $team = Team::where('id', $teamId)
        ->first();
    if (!$team) {
        return redirect()->route('coach.dashboard')->with('error', 'Team not found or access denied.');
    }

         $currentDate = Carbon::today()->toDateString();

        $currentProgram = TeamProgram::where('team_id', $teamId)
            ->whereHas('program', function ($query) use ($currentDate) {
                $query->where('registration_closing_date', '>=', $currentDate);
            })
            ->with('program')
            ->first();



             $notifications = collect();

            if($currentProgram){



        $notifications = PlayerInvitation::where('team_id', $teamId)
             ->where('program_id', $currentProgram->program_id)
            ->orderBy('created_at', 'desc')
            ->paginate(15);
            }





        $playerIds = TeamPlayer::where('team_id', $teamId)->pluck('player_id');
        $pastPlayers = User::whereIn('id', $playerIds)->get();
        $teamProgram = TeamProgram::where('team_id', $teamId)->pluck('program_id');
        $pastPlayersInTeam = PlayerProgram::whereIn('player_id', $playerIds)
            ->whereIn('program_id', $teamProgram)
            ->whereHas('program', function ($query) {
                $query->where('end_date', '<', now());
            })
            ->get();
        $pastPlayerIds = $pastPlayersInTeam->pluck('player_id');
        $pastPlayers = User::whereIn('id', $pastPlayerIds)->get();
        $playerInvitations = PlayerInvitation::whereIn('user_id', $playerIds)
            ->where('team_id', $teamId)
            ->get();

        $pastPrograms = TeamProgram::where('team_id', $teamId)
            ->whereHas('program', function ($query) use ($currentDate): void {
                $query->where('end_date', '<', $currentDate);
            })
            ->with('program')
            ->get();
        $guardianEmails = User::whereHas('roles', function ($q) {
            $q->where('name', 'guardian');
        })
        ->whereIn('id', $pastPlayers->pluck('primary_parent_id'))
        ->pluck('email', 'id');

  $pastPlayersData = $pastPlayers->map(function ($player) use ($guardianEmails) {
    return [
        'id' => $player->id,
        'firstName' => $player->firstName,
        'lastName' => $player->lastName,
        'email' => $player->email ?? '',
        'guardianEmail' => $guardianEmails[$player->primary_parent_id] ?? 'N/A',
    ];
});






$invitationStatuses = PlayerInvitation::where('team_id', $team->id)
    ->get()
    ->keyBy('user_id');


if($invitationStatuses){

$pastPlayersData = $pastPlayersData->map(function ($player) use ($invitationStatuses) {
    $player['invitationStatus'] = $invitationStatuses[$player['id']]->invitation_status ?? null;
    return $player;
});
    }



$currentActivePrograms = Program::whereIn('id', function ($query) use ($team) {
    $query->select('program_id')
          ->from('team_program')
          ->where('team_id', $team->id);
})
->where('end_date', '>=', Carbon::now())
->get();




 $acceptedNotifications = GuardianInvitationByCoach::where('coach_id', $user->id)
            ->where('status', 'accepted')
            ->whereBetween('updated_at', [
                now()->subDays(2)->startOfDay(),
                now()->endOfDay()
            ])
            ->get();

        return view("coach.dashboard", compact("user", "notifications", 'team', 'pastPlayersData', 'playerInvitations', 'currentProgram','currentActivePrograms', 'pastPrograms','acceptedNotifications'));
    }








   $teamCoach = TeamCoach::where('coach_id', $user->id)
    ->orderBy('created_at', 'asc')
    ->first();

        $team = Team::find($teamCoach->team_id);

      $currentDate = Carbon::today()->toDateString();
      $currentProgram = TeamProgram::where('team_id', $team->id)
    ->whereHas('program', function ($query) use ($currentDate) {
        $query->where('registration_closing_Date', '>=', $currentDate);
    })
    ->with('program')
    ->first();




     $notifications = collect();




   if($currentProgram){

        $notifications = PlayerInvitation::where('team_id', $team->id)
             ->where('program_id', $currentProgram->program_id)
            ->orderBy('created_at', 'desc')
            ->paginate(15);

   }

    $playerIds = TeamPlayer::where('team_id', $team->id)->pluck('player_id');
    $pastPlayers = User::whereIn('id', $playerIds)->get();

    $teamProgram = TeamProgram::where('team_id', $team->id)->pluck('program_id');
    $pastPlayersInTeam = PlayerProgram::whereIn('player_id', $playerIds)
        ->whereIn('program_id', $teamProgram)
        ->whereHas('program', function ($query) {
            $query->where('end_date', '<', now());
        })
        ->get();
    $pastPlayerIds = $pastPlayersInTeam->pluck('player_id');
    $pastPlayers = User::whereIn('id', $pastPlayerIds)->get();

    $playerInvitations = PlayerInvitation::whereIn('user_id', $playerIds)
        ->where('team_id', $team->id)
        ->get();

    $pastPrograms = TeamProgram::where('team_id', $team->id)
        ->whereHas('program', function ($query) use ($currentDate) {
            $query->where('end_date', '<', $currentDate);
        })
        ->with('program')
        ->get();


    $guardianEmails = User::whereHas('roles', function ($q) {
        $q->where('name', 'guardian');
    })
    ->whereIn('id', $pastPlayers->pluck('primary_parent_id'))
    ->pluck('email', 'id');

$pastPlayersData = $pastPlayers->map(function ($player) use ($guardianEmails) {
    return [
        'id' => $player->id,
        'firstName' => $player->firstName,
        'lastName' => $player->lastName,
        'email' => $player->email ?? '',
        'guardianEmail' => $guardianEmails[$player->primary_parent_id] ?? 'N/A',
    ];
});



$invitationStatuses = PlayerInvitation::where('team_id', $team->id)
    ->get()
    ->keyBy('user_id');


if($invitationStatuses){


$pastPlayersData = $pastPlayersData->map(function ($player) use ($invitationStatuses) {
    $player['invitationStatus'] = $invitationStatuses[$player['id']]->invitation_status ?? null;
    return $player;
});
}




$currentActivePrograms = Program::whereIn('id', function ($query) use ($team) {
    $query->select('program_id')
          ->from('team_program')
          ->where('team_id', $team->id);
})
->where('end_date', '>=', Carbon::now())
->get();



 $acceptedNotifications = GuardianInvitationByCoach::where('coach_id', $user->id)
            ->where('status', 'accepted')
            ->whereBetween('updated_at', [
                now()->subDays(2)->startOfDay(),
                now()->endOfDay()
            ])
            ->get();

    return view("coach.dashboard", compact("user", "notifications", 'team', 'pastPlayersData', 'playerInvitations', 'currentProgram',"currentActivePrograms", 'pastPrograms', 'acceptedNotifications'));
}



        public function coach_edit($id)
        {
            $user = auth()->user();


            if ($user->id !== (int)$id) {
                return response()->json(['success' => false, 'message' => 'Unauthorized access.'], 403);
            }


            $coach = User::find($id);

            if (!$coach) {
                return response()->json(['success' => false, 'message' => 'Coach not found.'], 404);
            }

            return response()->json([
                'success' => true,
                'coach' => $coach,
            ]);
        }



        public function coach_team_edit($id)
        {
            $user = auth()->user();

            if ($user->id !== (int) $id) {
                return response()->json(['success' => false, 'message' => 'Unauthorized access.'], 403);
            }

            $coach = User::find($id);

            if (!$coach) {
                return response()->json(['success' => false, 'message' => 'Coach not found.'], 404);
            }

            return response()->json([
                'success' => true,
                'coach' => $coach,
            ]);
        }



   public function coach_team_update(Request $request)
{
    $request->validate([
        'user_id' => 'required',
        'team_name' => 'required',
        'town_name' => 'required',
    ]);

    $user = auth()->user();
    if ($user->id !== (int) $request->input('user_id')) {
        return response()->json(['success' => false, 'message' => 'Unauthorized access.'], 403);
    }


    $coach = User::find($request->input('user_id'));

    if ($coach) {
        $coach->teamName = $request->input('team_name');
        $coach->town = $request->input('town_name');
        $coach->save();

        $team = Team::where('user_id', $coach->id)->first();
        if ($team) {
            $team->name = $request->input('team_name');
            $team->save();
        }

        return response()->json(['success' => true]);
    } else {
        return response()->json(['success' => false, 'message' => 'Coach not found.'], 404);
    }
}



 public function coach_update(Request $request)
{
    $request->validate([
        'coach_id' => 'required',
        'firstName' => 'required|string|max:255',
        'lastName' => 'required|string|max:255',
        'email' => 'required|email|max:255',
    ]);
    $user = auth()->user();

    if ($user->id !== (int) $request->input('coach_id')) {
        return response()->json(['success' => false, 'message' => 'Unauthorized access.'], 403);
    }
    $coach = User::find($request->input('coach_id'));

    if ($coach) {

        $coach->firstName = $request->input('firstName');
        $coach->lastName = $request->input('lastName');
        $coach->email = $request->input('email');
        $coach->save();

        return response()->json(['success' => true]);
    } else {
        return response()->json(['success' => false, 'message' => 'Coach not found.'], 404);
    }
}




public function searchPlayer(Request $request)
{
    $user = auth()->user();

    $query = User::select('users.*')
        ->join('users as guardians', 'users.primary_parent_id', '=', 'guardians.id')
        ->whereHas('roles', function ($q) {
            $q->where('name', 'player');
        });

    if ($request->filled('firstName')) {
        $query->whereRaw('LOWER(users.firstName) LIKE ?', ['%' . strtolower($request->firstName) . '%']);
    }

    if ($request->filled('lastName')) {
        $query->whereRaw('LOWER(users.lastName) LIKE ?', ['%' . strtolower($request->lastName) . '%']);
    }

    if ($request->filled('email')) {
        $emailSearch = strtolower($request->email);
        $query->where(function($q) use ($emailSearch){
            $q->whereRaw('LOWER(users.email) LIKE ?', ['%' . $emailSearch . '%'])
                ->orWhereRaw('LOWER(guardians.email) LIKE ?', ['%' . $emailSearch . '%']);
        });
    }

    $players = $query->paginate(10)->appends($request->all());

    if ($request->filled('team')) {


        $teamCoach = TeamCoach::where('coach_id', $user->id)->where('team_id', $request->team)->first();

        $team=Team::where('id', $teamCoach->team_id)->first();

        $currentDate = now();
        $currentProgram = TeamProgram::where('team_id', $team->id)
            ->whereHas('program', function ($query) use ($currentDate) {
                $query->where('end_date', '>=', $currentDate);
            })
            ->with('program')
            ->first();

    } else {
        $team = Team::where('user_id', $user->id)->first();
        $currentDate = now();


        $currentProgram = TeamProgram::where('team_id', $team->id)
            ->whereHas('program', function ($query) use ($currentDate) {
                $query->where('end_date', '>=', $currentDate);
            })
            ->with('program')
            ->first();
    }

    $playerIds = $players->pluck('id');
    $playerInvitations = PlayerInvitation::whereIn('user_id', $playerIds)
        ->where('team_id', $team->id)
        ->where('program_id', $currentProgram->program_id)
        ->get();


    $guardianEmails = User::whereHas('roles', function ($q) {
        $q->where('name', 'guardian');
    })
    ->whereIn('id', $players->pluck('primary_parent_id'))
    ->pluck('email', 'id');


    $playersData = $players->map(function ($player) use ($guardianEmails) {
        return [
            'id' => $player->id,
            'firstName' => $player->firstName,
            'lastName' => $player->lastName,
            'email' => $player->email ?? '',
            'guardianEmail' => $guardianEmails[$player->primary_parent_id] ?? 'N/A',
        ];
    });

    return response()->json([
        'success' => true,
        'players' => $playersData,
        'team' => $team,
        'last_page' => $players->lastPage(),
        'playerInvitations' => $playerInvitations,
    ]);
}




public function searchCoach(Request $request)
{
    $user = auth()->user();
    $query = User::select('users.*')
        ->whereHas('roles', function ($q) {
            $q->where('name', 'coach');
        })
        ->where('id', '<>', $user->id);

    if ($request->filled('firstName')) {
        $query->whereRaw('LOWER(users.firstName) LIKE ?', ['%' . strtolower($request->firstName) . '%']);
    }

    if ($request->filled('lastName')) {
        $query->whereRaw('LOWER(users.lastName) LIKE ?', ['%' . strtolower($request->lastName) . '%']);
    }

    if ($request->filled('email')) {
        $emailSearch = strtolower($request->email);
        $query->whereRaw('LOWER(users.email) LIKE ?', ['%' . $emailSearch . '%']);
    }

    if ($request->filled('town')) {
        $townSearch = strtolower($request->town);
        $query->whereRaw('LOWER(users.town) LIKE ?', ['%' . $townSearch . '%']);
    }
    $coaches = $query->paginate(10)->appends($request->all());

    if ($request->filled('email') && $coaches->isEmpty()) {
        $isInvitedBefore = InvitationForNotFoundCoaches::where('email', $request->email)
            ->where('invited_by', $user->id)
            ->exists();

        if ($isInvitedBefore) {
            return response()->json([
                'success' => true,
                'message' => 'User with the mail has been already invited by you'
            ]);
        }

        if (strtolower($request->email) === strtolower($user->email)) {
            return response()->json([
                'success' => false,
                'message' => 'You cannot send an invite to yourself.'
            ], 400);
        }

        $teamId = $request->input('team');
         $invitationToken = Str::random(64);

        InvitationForNotFoundCoaches::create([
            'invited_by' => $user->id,
            'email' => $request->email,
            'team_id' => $teamId,
            'invitation_token' => $invitationToken,
        ]);


       try {
    Mail::to($request->email)->send(new CoachNotFound($user, $request->email, $invitationToken));
        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to send the invitation. Please try again later.'
            ], 500);
        }


        return response()->json([
            'success' => true,
            'message' => 'We were unable to find a coach with that email.'
        ], 200);
    }


    $coachIds = $coaches->pluck('id');


    $invitations = CoachInvitation::whereIn('user_id', $coachIds)
        ->where('coach_id', $user->id)
        ->get();


    $invitationsByCoach = $invitations->keyBy('user_id');


    $teams = Team::where('user_id', $user->id)->get(['id', 'name']);


    $coachesData = $coaches->map(function ($coach) use ($invitationsByCoach) {
        $invitation = $invitationsByCoach->get($coach->id);
        $invitationStatus = $invitation ? ucfirst($invitation->invitation_status) : 'Not Invited';

        return [
            'id' => $coach->id,
            'firstName' => $coach->firstName,
            'lastName' => $coach->lastName,
            'email' => $coach->email,
            'invitationStatus' => $invitationStatus,
            'team_id' => $invitation->team_id ?? null,
        ];
    });


    return response()->json([
        'success' => true,
        'coaches' => $coachesData,
        'last_page' => $coaches->lastPage(),
        'teams' => $teams,
    ]);
}




public function checkCoachInvitationStatus($coachId, $teamId)
{
    $invitation = CoachInvitation::where('user_id', $coachId)
        ->where('team_id', $teamId)
        ->first(['invitation_status']);

    if ($invitation) {
        return response()->json(['invited' => true, 'status' => $invitation->invitation_status]);
    } else {
        return response()->json(['invited' => false]);
    }
}


public function inviteCoach(Request $request, $coachId)
{
    $user = auth()->user();

    $validator = Validator::make($request->all(), [
        'team_id_hidden' => 'required|exists:teams,id',
    ], [
        'team_id_hidden.required' => 'Please select a team.',
        'team_id_hidden.exists' => 'The selected team is invalid.',
    ]);

    if ($validator->fails()) {
        $errorMessage = $validator->errors()->first();
        return response()->json([
            'success' => false,
            'message' => $errorMessage,
        ]);
    }

    $coach = User::where('id', $coachId)
        ->whereHas('roles', function($query) {
            $query->where('name', 'coach');
        })
        ->first();

    if (!$coach) {
        return response()->json([
            'success' => false,
            'message' => 'Coach not found or does not have the coach role.',
        ]);
    }

    try {
        $teamId = $request->input('team_id_hidden');
        $team = Team::find($teamId);
        if (!$team) {
            return response()->json([
                'success' => false,
                'message' => 'Selected team not found.',
            ]);
        }


        $teamPrograms = TeamProgram::where('team_id', $teamId)->get();


            $invitation = CoachInvitation::firstOrCreate(
                ['user_id' => $coachId, 'team_id' => $team->id],
                [
                    'coach_id' => $user->id,
                    'invitation_status' => 'pending',

                ]
            );


            Mail::to($coach->email)->send(new SendInvitationToCoach($coach, $team, $user));

            return response()->json([
                'success' => true,
                'message' => 'Invitation sent successfully!',
            ]);



    } catch (Exception $e) {
        return response()->json([
            'success' => false,
            'message' => 'Failed to send the invitation. Please try again later.',
            'error' => $e->getMessage(),
        ]);
    }
}




 public function invitePlayer(Request $request, $playerId)
{
    $user=auth()->user();
    $request->validate([
        'team_id' => 'required|integer|exists:teams,id',
        'payment_amount' => 'required|numeric|min:0',
        'emails' => 'required',
    ]);

    $teamId = $request->input('team_id');
    $emailsString = $request->input('emails');
    $emails = array_map('trim', explode(',', $emailsString));
    $emails = array_slice($emails, 0, 2);

     $currentProgram = TeamProgram::where('team_id', $teamId)
        ->whereHas('program', function ($query) {
            $query->where('registration_closing_date', '>=', Carbon::today()->toDateString());
        })
        ->first();

    if(!$currentProgram){
         return response()->json(['success' => false, 'message' => 'Unable to find Program for the team.']);
    }

    $player = User::where('id', $playerId)->first();

    if (!$player) {
        return response()->json(['success' => false, 'message' => 'Unable to find Player.']);
    }

    $playerEmail = $player->email;
    $guardianEmail = User::where('id', $player->primary_parent_id)
                         ->orWhere('id', $player->parent_id)
                         ->pluck('email')->first();

    $emailsMatch = false;

    if (count($emails) === 1) {
        $emailsMatch = ($emails[0] === $playerEmail || $emails[0] === $guardianEmail);
    } elseif (count($emails) === 2) {
        $emailsMatch = (
            ($emails[0] === $playerEmail || $emails[0] === $guardianEmail) &&
            ($emails[1] === $playerEmail || $emails[1] === $guardianEmail)
        );
    }

    if (!$emailsMatch) {
        return response()->json(['success' => false, 'message' => 'The provided emails do not match the player or guardian emails.']);
    }

   $invitation = PlayerInvitation::where([
    'user_id' => $playerId,
    'team_id' => $teamId,
    'program_id' => $currentProgram->program_id
])->first();

if (!$invitation) {

    $invitation = PlayerInvitation::create([
        'user_id' => $playerId,
        'team_id' => $teamId,
        'program_id' => $currentProgram->program_id,
        'coach_id' => auth()->id(),
        'invitation_status' => 'pending',
        'balance_due' => $request->payment_amount,
        'balance_assigned'=>$request->payment_amount,
    ]);
} else {

    return response()->json(['message' => 'Invitation already exists.'], 409);
}

 $redirectUrl = route('coach.dashboard', ['team' => $teamId]);

    event(new PlayerInvited($player, $emails,   $user));

    return response()->json(['success' => true,
     'redirectUrl' => $redirectUrl,
     'message' => 'Invitation sent successfully.']);
}




 public function createNewTeam(Request $request): JsonResponse
{
    try {
        $validator = Validator::make($request->all(), [
            'newTeamName' => 'required|unique:teams,name',
        ]);

        if ($validator->fails()) {

            return response()->json([
                'success' => false,
                'message' => 'This team name has already been taken.',
            ], 422);
        }


        $team = Team::create([
            'user_id' => auth()->id(),
            'name' => $request->input('newTeamName'),
        ]);

        TeamCoach::create([
            'team_id'=>$team->id,
            'coach_id'=>auth()->id(),
            'is_primary'=>true,

        ]);

        return response()->json([
            'success' => true,
            'data' => $team,
            'message' => 'Team created successfully.'
        ]);
    } catch (Exception $e) {
        return response()->json([
            'success' => false,
            'message' => 'Failed to create the team. Please try again later.',
            'error' => $e->getMessage()
        ], 500);
    }
}

public function coachTeams(Request $request): JsonResponse
{
    try {
        $coach = auth()->user();
        $teamIds = TeamCoach::where('coach_id', $coach->id)
            ->pluck('team_id');


        $teams = Team::whereIn('id', $teamIds)->get();

        return response()->json([
            'success' => true,
            'data' => $teams,
            'message' => 'Teams fetched successfully.'
        ]);
    } catch (Exception $e) {
        return response()->json([
            'success' => false,
            'message' => 'Failed to fetch teams. Please try again later.',
            'error' => $e->getMessage()
        ], 500);
    }
}

public function manageTeam(Request $request)
{
    $user = auth()->user();
    $userId = $user->id;
    $request->validate([
        'teamId' => 'required|exists:team_coach,team_id',
    ]);

    $teamId = $request->input('teamId');


    $teamCoach = TeamCoach::where('team_id', $teamId)
        ->where('coach_id', $userId)
        ->first();

    if (!$teamCoach) {
        return response()->json([
            'success' => false,
            'message' => 'Failed to find team or unauthorized access.',
        ]);
    }


    $team = Team::where('id', $teamId)->first();
    $teamName = $team;




    $redirectUrl = route('coach.dashboard', ['team' => $teamId]);


    return response()->json([
        'success' => true,
        'redirectUrl' => $redirectUrl,
        'team' => $teamName,
        'message' => 'Team management page is ready.',
    ]);
}



public function manageTeams(Request $request)
{
    $user = auth()->user();

    $primaryTeams = TeamCoach::where('coach_id', $user->id)
        ->where('is_primary', true)
        ->pluck('team_id');


    $invitedCoaches = CoachInvitation::with(['team', 'user'])
        ->whereIn('team_id', $primaryTeams)
        ->where('coach_id', $user->id)
        ->get();

    $invitedTeamIds = $invitedCoaches->pluck('team.id')->unique();


    $coachTeams = TeamCoach::with('team')
        ->where('coach_id', $user->id)
        ->where('is_primary', true)
        ->whereNotIn('team_id', $invitedTeamIds)
        ->get();

    return view('coach.manageTeams', compact('user', 'invitedCoaches', 'coachTeams'));
}








public function sendInviteMailToGuardian(Request $request)
{
    $coach = auth()->user();

      $data = $request->json()->all();
    $validator = Validator::make($data, [
          'guardian_email' => 'required|email|unique:guardian_invitations_by_coach,guardian_email',
        'guardian_name' => 'required|string',
        'player_name' => 'required|string',
        'team_id' => 'required',
    ]);
    if ($validator->fails()) {
        return response()->json([
            'errors' => $validator->errors(),
        ], 422);
    }
      $guardian_email = $request->input('guardian_email');
    $guardian_name = $request->input('guardian_name');
    $player_name = $request->input('player_name');
    $team_id = $request->input('team_id');

    $team = Team::findOrFail($team_id);
    $programIds = TeamProgram::where('team_id', $team->id)
        ->pluck('program_id');

    if ($programIds->isEmpty()) {
        return response()->json([
            'success' => false,
            'message' => 'No programs found for this team.',
        ], 404);
    }
    $teamCurrentProgram = Program::whereIn('id', $programIds)
        ->whereDate('start_date', '>', now())
        ->orderBy('start_date', 'asc')
        ->first();

    if (!$teamCurrentProgram) {
        return response()->json([
            'success' => false,
            'message' => 'No upcoming program found for this team.',
        ], 404);
    }
        $startingDate = $teamCurrentProgram->start_date;
     if (is_string($startingDate)) {
        $startingDate = Carbon::parse($startingDate);
    }

    $coachInviteToken = Str::random(32);



    try {
        GuardianInvitationByCoach::create([
            'coach_id' => $coach->id,
            'guardian_email' => $guardian_email,
            'guardian_name' => $guardian_name,
            'player_name' => $player_name,
            'token' =>  $coachInviteToken,
            'status' => 'pending',
        ]);
       Mail::to($guardian_email)->send(new GuardianInvitationMail(
             $coachInviteToken,
            $guardian_name,
            $player_name,
            $team->name,
            $teamCurrentProgram->name,
            $startingDate->format('F j, Y'),
            $coach,
        ));

        return response()->json([
            'success' => true,
            'message' => 'Invitation sent successfully.',
        ]);

    } catch (Exception $e) {

        Log::error('Error sending guardian invitation', [
            'exception' => $e->getMessage(),
            'guardian_email' => $guardian_email,
            'guardian_name' => $guardian_name,
            'player_name' => $player_name,
            'team_id' => $team_id
        ]);


        return response()->json([
            'success' => false,
            'message' => 'An error occurred while sending the invitation.',
            'error' => $e->getMessage(),
        ], 500);
    }
}

public function switchRoleToGuardian(Request $request, $coachId) {
    $coach = User::findOrFail($coachId);

    if ($coach->roles()->where('name', 'coach')->exists()) {

        if (!$coach->roles()->where('name', 'guardian')->exists()) {
            $coach->roles()->attach(Role::GUARDIAN);
            $coach->update([
                'is_guardian' => true,
                'current_role' => 'guardian'
            ]);
        } else {

            $coach->update(['current_role' => 'guardian',
               'is_guardian'=>true]);
        }

        return response()->json([
            'success' => true,
            'redirectUrl' => route('guardian.dashboard')
        ]);
    }

    return response()->json([
        'success' => false,
        'message' => 'Coach not found or does not have the coach role'
    ], 404);
}




public function coachAcceptedInvite(Request $request, $coachId)
{
    $coach = User::findOrFail($coachId);
    $invitationAccepted = CoachInvitation::where('user_id', $coach->id)->first();
    if (!$invitationAccepted) {
        abort(404, 'Invitation not found');
    }
    if ($invitationAccepted->invitation_status === 'accepted') {
        return redirect()->route('loginPage');
    }
    $invitationAccepted->update([
        'invitation_status' => 'accepted'
    ]);

    TeamCoach::create([
        'team_id'=>$invitationAccepted->team_id,
        'coach_id'=>$invitationAccepted->user_id,
        'is_primary'=>false,
    ]);
    return redirect()->route('loginPage')->with('success', 'Invitation accepted successfully.');
}


}
