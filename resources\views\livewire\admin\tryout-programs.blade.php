<div>
    <div class="container">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <div class="heading">
                <h2 class="text-uppercase fs-6 mb-0">Tryout Programs</h2>
            </div>
            <div class="d-flex gap-2">
                <button wire:click="togglePostTryouts" class="cta">
                    <i class="bi bi-clock-history me-2"></i>
                    {{ $showPostTryouts ? 'Hide' : 'Show' }} Post-Tryout Programs
                </button>
                <a href="{{route('admin.program.add')}}" class="cta">
                    <i class="bi bi-plus-circle me-2"></i> Add New Program
                </a>
            </div>
        </div>

        {{-- Livewire Filter Form --}}
        <div class="form row table-filter justify-content-center">
            <div class="col-md-auto">
                <div class="filter-option">
                    <div class="mb-4">
                        <label class="form-label text-uppercase" for="filter">Filter By</label>
                        <div class="select-arrow position-relative">
                            <select class="form-control" id="filter" wire:model.live="filter">
                                <option value="">All</option>
                                @foreach ($this->getAvailableFilters() as $filterOption)
                                    <option value="{{ $filterOption }}">{{ $filterOption }}</option>
                                @endforeach
                            </select>
                            <span class="arrow"><i class="bi bi-chevron-down"></i></span>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-auto">
                <div class="filter-option">
                    <div class="mb-4">
                        <label class="form-label text-uppercase" for="search">Search</label>
                        <div class="flex items-center">
                            <input class="form-control" id="search" type="text"
                                wire:model.live.debounce.500ms="search" placeholder="Search programs..." />
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-auto">
                <div class="filter-option">
                    <div class="mb-4">
                        <label class="form-label text-uppercase" for="perPage">Per Page</label>
                        <div class="select-arrow position-relative">
                            <select class="form-control" id="perPage" wire:model.live="perPage">
                                <option value="5">5</option>
                                <option value="10">10</option>
                                <option value="25">25</option>
                                <option value="50">50</option>
                            </select>
                            <span class="arrow"><i class="bi bi-chevron-down"></i></span>
                        </div>
                    </div>
                </div>
            </div>
        </div>






        {{-- programs Table --}}
        <div class="program-table">
            <div class="table-program table-responsive" wire:loading.class="table-updating" wire:target="updatedSearch,updatedFilter,updatedPerPage,togglePostTryouts">
                <table class="table table-hover" width="100%" id="user-table">
                    <thead>
                        <tr>
                            <th class="py-4">Name</th>
                            <th class="py-4">Sport</th>
                            <th class="py-4">Gender</th>
                            <th class="py-4">Age</th>
                            <th class="py-4">Dates</th>
                            <th class="py-4">Days</th>
                            <th class="py-4">Enrollment</th>
                            <th class="py-4">Cost</th>
                            <th class="py-4">Payment</th>
                            <th class="py-4">Status</th>
                            <th class="py-4" width="20"></th>
                            <th class="py-4" width="20"></th>
                        </tr>
                    </thead>
                    <tbody>
                        @forelse ($programs as $program)
                            <tr wire:key="program-{{ $program->id }}">



                                <td class="py-4" valign="middle">{{ $program->name }}</td>
                                <td class="py-4" valign="middle">{{ $program->sport }}</td>
                                <td class="py-4" valign="middle">{{ ucfirst($program->gender) }}</td>



                                <td class="py-4" valign="middle">
                                    {{ $program->age_restriction_from }}-{{ $program->age_restriction_to }}</td>
                                <td class="py-4" valign="middle">{{ $program->start_date }} -
                                    {{ $program->end_date }}</td>
                                <td class="py-4 text-capitalize" valign="middle">
                                    {{ implode(', ', $program->frequency_days) }}<br>{{ $program->frequency }}
                                </td>
                                <td class="py-4" valign="middle">{{ $program->number_of_registers }}</td>
                                <td class="py-4" valign="middle">${{ number_format($program->cost, 2) }}</td>
                                <td class="py-4" valign="middle">{{ ucfirst($program->payment) }}</td>
                                <td class="py-4" valign="middle">{{ ucfirst($program->status) }}</td>
                                <td class="py-4" valign="middle">
                                    <span class="action edit">
                                        <a href="{{ route('admin.program.edit', $program->slug) }}">
                                            <img src="{{ asset('images/edit-icon.svg') }}" alt=""
                                                width="20" height="20" />
                                        </a>
                                    </span>
                                </td>
                                <td class="py-4" valign="middle">
                                    <form id="removeProgram-{{ $program->slug }}"
                                        action="{{ route('admin.program.destroy', $program->slug) }}" method="POST"
                                        onsubmit="showConfirmation(event, 'removeProgram-{{ $program->slug }}')"
                                        style="display: inline;">
                                        @csrf
                                        @method('DELETE')
                                        <button type="submit"
                                            style="border: none; background: none; padding: 0; cursor: pointer;">
                                            <span class="action delete">
                                                <img src="{{ asset('images/delete-icon.svg') }}" alt=""
                                                    width="18" height="20" />
                                            </span>
                                        </button>
                                    </form>
                                </td>
                            </tr>
                            @empty
                                <tr>
                                    <td colspan="9" class="text-center py-4">
                                        @if ($search || $filter)
                                            <p class="mb-0">No programs found matching your search criteria.</p>
                                            <button class="btn btn-link p-0" wire:click="clearFilters">Clear
                                                filters</button>
                                        @else
                                            <p class="mb-0">No programs found.</p>
                                        @endif
                                    </td>
                                </tr>
                            @endforelse
                        </tbody>
                    </table>

                    {{-- Custom Livewire Pagination --}}
                    <div class="mt-4 d-flex justify-content-center">
                        {{ $programs->onEachSide(1)->links('custom-pagination') }}
                    </div>
                </div>
            </div>
        </div>

        {{-- Post-Tryout Programs Section --}}
        @if($showPostTryouts)
            <div class="mt-5">
                <div class="heading text-center mb-4">
                    <h2 class="text-uppercase fs-6 mb-0 navy-text">Post-Tryout Programs</h2>
                    <p class="text-muted small">Programs where tryout period has ended</p>
                </div>

                @forelse($postTryoutPrograms as $program)
                    <div class="program-table mb-4">
                        <div class="table-program">
                            {{-- Program Header --}}
                            <div class="d-flex justify-content-between align-items-center p-3" style="background: #d9d9d9; border-radius: 10px 10px 0 0;">
                                <div>
                                    <h5 class="mb-1 navy-text" style="font: 900 18px/1 'Montserrat', sans-serif;">{{ $program->name }}</h5>
                                    <small class="text-muted" style="font: 500 14px/1 'Montserrat', sans-serif;">
                                        {{ $program->sport }} • {{ ucfirst($program->gender) }} •
                                        Ages {{ $program->age_restriction_from }}-{{ $program->age_restriction_to }} •
                                        Ended: {{ \Carbon\Carbon::parse($program->end_date)->format('M d, Y') }}
                                    </small>
                                </div>
                                <span class="badge text-white" style="background: #0b4499; font: 700 12px/1 'Montserrat', sans-serif; padding: 0.5rem 1rem;">
                                    {{ $program->registrations->count() }} Players
                                </span>
                            </div>

                            {{-- Players Table --}}
                            @if($program->registrations->count() > 0)
                                <div class="table-responsive">
                                    <table class="table table-hover" style="margin-bottom: 0;">
                                        <thead>
                                            <tr>
                                                <th class="py-4" style="color: #194795; font: 700 14px/1 'Montserrat', sans-serif;">Player Name</th>
                                                <th class="py-4" style="color: #194795; font: 700 14px/1 'Montserrat', sans-serif;">Email</th>
                                                <th class="py-4" style="color: #194795; font: 700 14px/1 'Montserrat', sans-serif;">Invitation Status</th>
                                                <th class="py-4" style="color: #194795; font: 700 14px/1 'Montserrat', sans-serif;">Team</th>
                                                <th class="py-4" style="color: #194795; font: 700 14px/1 'Montserrat', sans-serif;">Actions</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            @foreach($this->getPlayersForProgram($program->id) as $player)
                                                <tr>
                                                    <td class="py-4" valign="middle" style="font: 14px/1 'Montserrat', sans-serif;">{{ $player['name'] }}</td>
                                                    <td class="py-4" valign="middle" style="font: 14px/1 'Montserrat', sans-serif;">{{ $player['email'] }}</td>
                                                    <td class="py-4" valign="middle">
                                                        @switch($player['invitation_status'])
                                                            @case('not_invited')
                                                                <span class="badge" style="background: #6c757d; color: white; font: 700 11px/1 'Montserrat', sans-serif; padding: 0.4rem 0.8rem;">Not Invited</span>
                                                                @break
                                                            @case('pending')
                                                                <span class="badge" style="background: #c1b05c; color: #062e69; font: 700 11px/1 'Montserrat', sans-serif; padding: 0.4rem 0.8rem;">Pending</span>
                                                                @break
                                                            @case('accepted')
                                                                <span class="badge" style="background: #28a745; color: white; font: 700 11px/1 'Montserrat', sans-serif; padding: 0.4rem 0.8rem;">Accepted</span>
                                                                @break
                                                            @case('rejected')
                                                                <span class="badge" style="background: #dc3545; color: white; font: 700 11px/1 'Montserrat', sans-serif; padding: 0.4rem 0.8rem;">Rejected</span>
                                                                @break
                                                            @case('completed')
                                                                <span class="badge" style="background: #0b4499; color: white; font: 700 11px/1 'Montserrat', sans-serif; padding: 0.4rem 0.8rem;">In Team</span>
                                                                @break
                                                        @endswitch
                                                    </td>
                                                    <td class="py-4" valign="middle" style="font: 14px/1 'Montserrat', sans-serif;">{{ $player['invited_team'] ?? '-' }}</td>
                                                    <td class="py-4" valign="middle">
                                                        @if($player['invitation_status'] === 'not_invited')
                                                            <button wire:click="showInviteModal({{ $player['id'] }}, {{ $program->id }})"
                                                                    class="cta" style="font-size: 12px; padding: 0.5rem 1rem;"
                                                                    wire:loading.attr="disabled" wire:target="showInviteModal">
                                                                <i class="bi bi-envelope me-1"></i> Invite
                                                            </button>
                                                        @elseif($player['invitation_status'] === 'accepted')
                                                            <button wire:click="movePlayerToTeam({{ $player['id'] }}, {{ $player['invitation_id'] }})"
                                                                    class="cta gold" style="font-size: 12px; padding: 0.5rem 1rem;"
                                                                    wire:confirm="Are you sure you want to move this player to the team?"
                                                                    wire:loading.attr="disabled" wire:target="movePlayerToTeam">
                                                                <span wire:loading.remove wire:target="movePlayerToTeam">
                                                                    <i class="bi bi-arrow-right me-1"></i> Move to Team
                                                                </span>
                                                                <span wire:loading wire:target="movePlayerToTeam">
                                                                    <i class="bi bi-arrow-clockwise spin me-1"></i> Moving...
                                                                </span>
                                                            </button>
                                                        @endif
                                                    </td>
                                                </tr>
                                            @endforeach
                                        </tbody>
                                    </table>
                                </div>
                            @else
                                <div class="p-4 text-center" style="background: white; border-radius: 0 0 10px 10px;">
                                    <p class="text-muted mb-0" style="font: 500 16px/1 'Montserrat', sans-serif;">No players registered for this program.</p>
                                </div>
                            @endif
                        </div>
                    </div>
                @empty
                    <div class="text-center py-4">
                        <p class="text-muted mb-0" style="font: 500 16px/1 'Montserrat', sans-serif;">No post-tryout programs found.</p>
                    </div>
                @endforelse
            </div>
        @endif

        {{-- Team Invitation Modal --}}
        @if($inviteModalOpen)
            <div class="modal fade show" style="display: block;" tabindex="-1">
                <div class="modal-dialog">
                    <div class="modal-content" style="border-radius: 20px; border: none; box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);">
                        <div class="modal-header" style="border-bottom: 2px solid #d9d9d9;">
                            <h5 class="modal-title navy-text" style="font: 900 18px/1 'Montserrat', sans-serif;">Invite Player to Team</h5>
                            <button type="button" class="btn-close" wire:click="closeInviteModal"></button>
                        </div>
                        <div class="modal-body" style="padding: 2rem;">
                            <div class="mb-3">
                                <label for="teamSelect" class="form-label text-uppercase" style="color: #194795; font: 700 14px/1 'Montserrat', sans-serif; letter-spacing: 1px;">Select Team</label>
                                <div class="select-arrow position-relative">
                                    <select class="form-control" id="teamSelect" wire:model.live="selectedTeamId" style="background-color: #d9d9d9; border-radius: 12px; border: none; padding: 12px 15px; font: 500 16px/1 'Montserrat', sans-serif;">
                                        <option value="">Choose a team...</option>
                                        @foreach($this->getAvailableTeams() as $team)
                                            <option value="{{ $team->id }}">{{ $team->name }}</option>
                                        @endforeach
                                    </select>
                                    <span class="arrow"><i class="bi bi-chevron-down"></i></span>
                                </div>
                            </div>

                            <div class="mb-3">
                                <label for="assignedBalance" class="form-label text-uppercase" style="color: #194795; font: 700 14px/1 'Montserrat', sans-serif; letter-spacing: 1px;">Assign Balance Amount</label>
                                <div class="input-group">
                                    <span class="input-group-text" style="background-color: #d9d9d9; border: none; border-radius: 12px 0 0 12px; font: 500 16px/1 'Montserrat', sans-serif;">$</span>
                                    <input type="number" class="form-control @error('assignedBalance') is-invalid @enderror" id="assignedBalance" wire:model.live="assignedBalance"
                                           placeholder="0.00" step="0.01" min="0" max="9999.99"
                                           style="background-color: #d9d9d9; border: none; border-radius: 0 12px 12px 0; padding: 12px 15px; font: 500 16px/1 'Montserrat', sans-serif;">
                                </div>
                                @error('assignedBalance')
                                    <div class="invalid-feedback d-block" style="font: 400 12px/1 'Montserrat', sans-serif;">{{ $message }}</div>
                                @enderror
                                <small class="text-muted" style="font: 400 12px/1 'Montserrat', sans-serif;">Enter the balance amount to assign to this player (optional)</small>
                            </div>
                        </div>
                        <div class="modal-footer" style="border-top: 2px solid #d9d9d9; padding: 1.5rem 2rem;">
                            <button type="button" class="cta" style="background: #6c757d; border-color: #6c757d; margin-right: 1rem;" wire:click="closeInviteModal">Cancel</button>
                            <button type="button" class="cta" wire:click="invitePlayerToTeam"
                                    wire:loading.attr="disabled" wire:target="invitePlayerToTeam"
                                    @if(!$selectedTeamId) disabled style="opacity: 0.6; cursor: not-allowed;" @endif>
                                <span wire:loading.remove wire:target="invitePlayerToTeam">Send Invitation</span>
                                <span wire:loading wire:target="invitePlayerToTeam">
                                    <i class="bi bi-arrow-clockwise spin me-1"></i> Sending...
                                </span>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-backdrop fade show"></div>
        @endif
    </div>
